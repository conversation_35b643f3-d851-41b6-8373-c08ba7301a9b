/**
 * Service LMS MC - he thong quan ly khoan vay
 * Noi tinh yeu bat dau
 */
const express = require('express')
const app = express()
const bodyParser = require('body-parser')
const httpServer = require('http').Server(app)
const common = require('./utils/common')
const constant = require('./utils/constant')
const loadConfig = require('./utils/load-config')
const redisClient = require('./utils/redis')
const requestLogService = require('./services/request-log-service')
const healthCheckApi = require('./routers/index')
const merchantLimitRouter = require('./routers/merchant-limit-router')
const debtAckContractRouter = require('./routers/debt-ack-contract-router')
const irRouter = require('./routers/ir-router')
const paymentRouter = require('./routers/payment-router')
const billRouter = require('./routers/bill-router')
const contractRiskGrpRouter = require('./routers/contract-risk-grp-router')
const installmentRouter = require('./routers/installment-router')
const loanAnnexRouter = require('./routers/loan-annex-router')
const loanAccountRouter = require('./routers/loan-account-router')
const collectionRouter = require('./routers/collection-router')
const writeOffRouter = require('./routers/write-off-router')
const welcomePackageRouter = require('./routers/welcome-package-router')
const thirdPartyRouter = require('./routers/third-party-router')
const holidayRouter = require('./routers/holiday-router')
const freezeRouter = require('./routers/freeze-router')
const moment = require('moment')
const fileUpload = require('express-fileupload')
const merchantLimitRouterV2 = require('./routers/v2-merchant-limit-router')

process.env.NODE_TLS_REJECT_UNAUTHORIZED = 0

async function main() {
  await loadConfig.loadConfigService()
  await loadConfig.loadConfigData()
  await redisClient.initialize()
  common.log('Service phuc vu o port:' + global.config.data.http.port)
  httpServer.listen(global.config.data.http.port)
}

main()

app.use(
  bodyParser.urlencoded({
    extended: true
  })
)
app.use(bodyParser.json())
app.use(function (req, res, next) {
  req.requestTime = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS)
  next()
  res.on('finish', () => {
    if (req.isLog == true) {
      requestLogService.saveRequestLog(req, res)
    }
  })
})
app.use(
  fileUpload({
    limits: { fileSize: 50 * 1024 * 1024 }
  })
)
const BASE_PATH = '/lms-mc'
app.get(`${BASE_PATH}/v1/reload`, async (req, res) => {
  try {
    await loadConfig.loadConfigService()

    await loadConfig.loadConfigData()
    return res.status(200).json({
      status: 1,
      msg: 'Reload config service success!'
    })
  } catch (e) {
    common.log(e)
    res.status(502).json({ status: -4, msg: 'Service Error' })
  }
})

const { LoanAccountRepository } = require('./repositories-v2/index')
const clearDataRepo = require('./repositories/clear-data.repo')
app.post(`${BASE_PATH}/v1/clear-data`, async (req, res) => {
  try {
    const { kunnNumber, contractNumber } = req.body

    if (kunnNumber) {
      const kunn = await LoanAccountRepository.findOne({ where: { debt_ack_contract_number: kunnNumber } })
      if (kunn) {
        await clearDataRepo.clearKunn(kunn.debt_ack_contract_number, kunn.cust_id)
      }
    }
    if (contractNumber) {
      const listKunn = await LoanAccountRepository.findAllBy({ contract_number: contractNumber })
      for (const kunn of listKunn) {
        await clearDataRepo.clearKunn(kunn.debt_ack_contract_number, kunn.cust_id)
      }
      await clearDataRepo.clearHM(contractNumber)
    }
    return res.status(200).json({
      status: 1,
      msg: 'Clear data success!'
    })
  } catch (e) {
    common.log(e)
    res.status(502).json({ status: -4, msg: 'Service Error' })
  }
})
app.use(`${BASE_PATH}/v1`, healthCheckApi)
app.use(`${BASE_PATH}/v1`, contractRiskGrpRouter)
app.use(BASE_PATH, installmentRouter)
app.use(BASE_PATH, paymentRouter)
app.use(`${BASE_PATH}/v1`, merchantLimitRouter)
app.use(`${BASE_PATH}/v2`, merchantLimitRouterV2)
app.use(BASE_PATH, debtAckContractRouter)
app.use(`${BASE_PATH}/v1`, irRouter)
app.use(`${BASE_PATH}/v1`, billRouter)
app.use(BASE_PATH, loanAnnexRouter)
app.use(BASE_PATH, loanAccountRouter)
app.use(`${BASE_PATH}/v1`, collectionRouter)
app.use(`${BASE_PATH}/v1`, writeOffRouter)
app.use(BASE_PATH, welcomePackageRouter)
app.use(`${BASE_PATH}/v1`, thirdPartyRouter)
app.use(`${BASE_PATH}/v1`, holidayRouter)
app.use(`${BASE_PATH}/v1`, freezeRouter)
