const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'freeze_hst',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class FreezeHstEntity extends BaseEntity {
  constructor({
    id,
    debt_ack_contract_number,
    status,
    created_date,
    updated_date,
    created_user,
    owner_id,
    flag_active
  }) {
    super()
    this.id = id
    this.debt_ack_contract_number = debt_ack_contract_number
    this.status = status
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_user = created_user
    this.owner_id = owner_id
    this.flag_active = flag_active
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  FreezeHstEntity,
  metadata
}
