const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'coll_dpd_hist',
  primaryKey: '',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class CollDpdHistEntity extends BaseEntity {
  constructor({
    contract_number,
    dpd,
    dpd_strategy,
    owner_id,
    created_date,
    updated_date,
    is_delete,
    created_user,
    flag_active,
    value_date,
    debt_ack_contract_number
  }) {
    super()
    this.contract_number = contract_number
    this.dpd = dpd
    this.dpd_strategy = dpd_strategy
    this.owner_id = owner_id
    this.created_date = created_date
    this.updated_date = updated_date
    this.is_delete = is_delete
    this.created_user = created_user
    this.flag_active = flag_active
    this.value_date = value_date
    this.debt_ack_contract_number = debt_ack_contract_number
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  CollDpdHistEntity,
  metadata
}
