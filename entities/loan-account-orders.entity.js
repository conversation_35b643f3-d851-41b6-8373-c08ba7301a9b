const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'loan_account_orders',
  primaryKey: 'loan_account_order_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class LoanAccountOrdersEntity extends BaseEntity {
  constructor({
    loan_account_order_id,
    created_date,
    updated_date,
    debt_ack_contract_number,
    order_index,
    order_number,
    prin_amt,
    order_amt,
    payment_date
  }) {
    super()
    this.loan_account_order_id = loan_account_order_id
    this.created_date = created_date
    this.updated_date = updated_date
    this.debt_ack_contract_number = debt_ack_contract_number
    this.order_index = order_index
    this.order_number = order_number
    this.prin_amt = prin_amt
    this.order_amt = order_amt
    this.payment_date = payment_date
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  LoanAccountOrdersEntity,
  metadata
}
