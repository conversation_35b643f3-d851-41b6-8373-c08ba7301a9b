const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'installment',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'status'
}

class InstallmentEntity extends BaseEntity {
  constructor({
    id,
    debt_ack_contract_id,
    num_cycle,
    amount,
    remain_amount,
    cycle_date,
    type,
    payment_status,
    owner_id,
    is_testing,
    created_date,
    updated_date,
    created_by,
    contract_number,
    debt_ack_contract_number,
    start_date,
    end_date,
    due_date,
    closed,
    of_num_cycle,
    is_annex,
    lpi_at_cycle,
    ir_num_cycle,
    ir_from_date,
    ir_to_date,
    ir_on_prin,
    status,
    origin_amt,
    outstanding_prin,
    description,
    ir_rate,
    annex_number,
    amort_id,
    payment_priority,
    accept_payment_date,
    invoiced_date
  }) {
    super()
    this.id = id
    this.debt_ack_contract_id = debt_ack_contract_id
    this.num_cycle = num_cycle
    this.amount = amount
    this.remain_amount = remain_amount
    this.cycle_date = cycle_date
    this.type = type
    this.payment_status = payment_status
    this.owner_id = owner_id
    this.is_testing = is_testing
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_by = created_by
    this.contract_number = contract_number
    this.debt_ack_contract_number = debt_ack_contract_number
    this.start_date = start_date
    this.end_date = end_date
    this.due_date = due_date
    this.closed = closed
    this.of_num_cycle = of_num_cycle
    this.is_annex = is_annex
    this.lpi_at_cycle = lpi_at_cycle
    this.ir_num_cycle = ir_num_cycle
    this.ir_from_date = ir_from_date
    this.ir_to_date = ir_to_date
    this.ir_on_prin = ir_on_prin
    this.status = status
    this.origin_amt = origin_amt
    this.outstanding_prin = outstanding_prin
    this.description = description
    this.ir_rate = ir_rate
    this.annex_number = annex_number
    this.amort_id = amort_id
    this.payment_priority = payment_priority
    this.accept_payment_date = accept_payment_date
    this.invoiced_date = invoiced_date
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  InstallmentEntity,
  metadata
}
