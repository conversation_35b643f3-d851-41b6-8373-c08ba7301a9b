const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'ir',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class IrEntity extends BaseEntity {
  constructor({
    id,
    ir_charge_id,
    installment_id,
    ir_date,
    ir_rate,
    ir_value,
    ir_type,
    ir_amount,
    owner_id,
    is_testing,
    created_date,
    updated_date,
    created_by,
    contract_number,
    debt_ack_contract_number,
    prin_amount,
    flag_active
  }) {
    super()
    this.id = id
    this.ir_charge_id = ir_charge_id
    this.installment_id = installment_id
    this.ir_date = ir_date
    this.ir_rate = ir_rate
    this.ir_value = ir_value
    this.ir_type = ir_type
    this.ir_amount = ir_amount
    this.owner_id = owner_id
    this.is_testing = is_testing
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_by = created_by
    this.contract_number = contract_number
    this.debt_ack_contract_number = debt_ack_contract_number
    this.prin_amount = prin_amount
    this.flag_active = flag_active
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  IrEntity,
  metadata
}
