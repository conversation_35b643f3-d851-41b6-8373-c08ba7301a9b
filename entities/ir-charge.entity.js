const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'ir_charge',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class IrChargeEntity extends BaseEntity {
  constructor({
    id,
    mc_limit_id,
    product_code,
    ir_code,
    ir_name,
    ir_type,
    ir_value,
    status,
    owner_id,
    is_testing,
    created_date,
    updated_date,
    created_by,
    debt_ack_contract_number,
    tenor_from,
    tenor_to,
    instal_from,
    instal_to
  }) {
    super()
    this.id = id
    this.mc_limit_id = mc_limit_id
    this.product_code = product_code
    this.ir_code = ir_code
    this.ir_name = ir_name
    this.ir_type = ir_type
    this.ir_value = ir_value
    this.status = status
    this.owner_id = owner_id
    this.is_testing = is_testing
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_by = created_by
    this.debt_ack_contract_number = debt_ack_contract_number
    this.tenor_from = tenor_from
    this.tenor_to = tenor_to
    this.instal_from = instal_from
    this.instal_to = instal_to
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  IrChargeEntity,
  metadata
}
