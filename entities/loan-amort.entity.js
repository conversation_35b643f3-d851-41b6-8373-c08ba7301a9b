const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'loan_amort',
  primaryKey: 'amort_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class LoanAmortEntity extends BaseEntity {
  constructor({
    amort_id,
    debt_ack_contract_number,
    amt_amort,
    int_rate,
    start_date,
    end_date,
    tenor,
    flag_active,
    annex_number,
    prev_amort_id,
    created_date,
    updated_date
  }) {
    super()
    this.amort_id = amort_id
    this.debt_ack_contract_number = debt_ack_contract_number
    this.amt_amort = amt_amort
    this.int_rate = int_rate
    this.start_date = start_date
    this.end_date = end_date
    this.tenor = tenor
    this.flag_active = flag_active
    this.annex_number = annex_number
    this.prev_amort_id = prev_amort_id
    this.created_date = created_date
    this.updated_date = updated_date
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  LoanAmortEntity,
  metadata
}
