const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'loan_account',
  primaryKey: 'loan_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class LoanAccountEntity extends BaseEntity {
  constructor({
    loan_id,
    contract_number,
    contract_limit_id,
    cust_id,
    apr_limit_amt,
    prin_amt,
    status,
    approval_date,
    signing_in_progress_date,
    signature_date,
    activation_date,
    dpd,
    dpd_strategy,
    is_writing_off,
    is_contract_breach,
    insurance_type,
    termination_motive,
    rls_date,
    created_date,
    updated_date,
    owner_id,
    product_code,
    tenor,
    start_date,
    end_date,
    noti_date_num,
    product_child_code,
    hold_money,
    is_testing,
    created_by,
    mc_limit_id,
    active_date,
    periodicity,
    debt_ack_contract_number,
    payment_status,
    grace_day_number,
    grace_amount,
    bill_day,
    cust_code,
    int_ir1_amt,
    ccycd,
    rls_amt,
    prin_ovr_amt,
    prin_due_amt,
    prin_paid,
    int_amt,
    int_ir2_amt,
    int_ir3_amt,
    int_ovr_amt,
    int_due_amt,
    int_paid,
    lpi_amt,
    fee_amt,
    fee_paid_amt,
    to_collect,
    non_allocation_amt,
    contract_type,
    partner_code,
    lpi_paid,
    phone_number,
    termination_date,
    channel,
    disbursement_type,
    is_freeze,
    order_amt,
    debt_tolerance_amt,
    suspend_account_holding_day,
                suspend_amt,
                normal_int_amt,
                pref_int_amt,
                normal_int_amt_paid,
                pref_int_amt_paid,
                factoring_fee_amt,
                factoring_fee_amt_paid,
  }) {
    super()
    this.loan_id = loan_id
    this.contract_number = contract_number
    this.contract_limit_id = contract_limit_id
    this.cust_id = cust_id
    this.apr_limit_amt = apr_limit_amt
    this.prin_amt = prin_amt
    this.status = status
    this.approval_date = approval_date
    this.signing_in_progress_date = signing_in_progress_date
    this.signature_date = signature_date
    this.activation_date = activation_date
    this.dpd = dpd
    this.dpd_strategy = dpd_strategy
    this.is_writing_off = is_writing_off
    this.is_contract_breach = is_contract_breach
    this.insurance_type = insurance_type
    this.termination_motive = termination_motive
    this.rls_date = rls_date
    this.created_date = created_date
    this.updated_date = updated_date
    this.owner_id = owner_id
    this.product_code = product_code
    this.tenor = tenor
    this.start_date = start_date
    this.end_date = end_date
    this.noti_date_num = noti_date_num
    this.product_child_code = product_child_code
    this.hold_money = hold_money
    this.is_testing = is_testing
    this.created_by = created_by
    this.mc_limit_id = mc_limit_id
    this.active_date = active_date
    this.periodicity = periodicity
    this.debt_ack_contract_number = debt_ack_contract_number
    this.payment_status = payment_status
    this.grace_day_number = grace_day_number
    this.grace_amount = grace_amount
    this.bill_day = bill_day
    this.cust_code = cust_code
    this.int_ir1_amt = int_ir1_amt
    this.ccycd = ccycd
    this.rls_amt = rls_amt
    this.prin_ovr_amt = prin_ovr_amt
    this.prin_due_amt = prin_due_amt
    this.prin_paid = prin_paid
    this.int_amt = int_amt
    this.int_ir2_amt = int_ir2_amt
    this.int_ir3_amt = int_ir3_amt
    this.int_ovr_amt = int_ovr_amt
    this.int_due_amt = int_due_amt
    this.int_paid = int_paid
    this.lpi_amt = lpi_amt
    this.fee_amt = fee_amt
    this.fee_paid_amt = fee_paid_amt
    this.to_collect = to_collect
    this.non_allocation_amt = non_allocation_amt
    this.contract_type = contract_type
    this.partner_code = partner_code
    this.lpi_paid = lpi_paid
    this.phone_number = phone_number
    this.termination_date = termination_date
    this.channel = channel
    this.disbursement_type = disbursement_type
    this.is_freeze = is_freeze
    this.order_amt = order_amt
    this.debt_tolerance_amt = debt_tolerance_amt
    this.suspend_account_holding_day = suspend_account_holding_day
    this.suspend_amt = suspend_amt
    this.normal_int_amt = normal_int_amt
    this.pref_int_amt = pref_int_amt
    this.normal_int_amt_paid = normal_int_amt_paid
    this.pref_int_amt_paid = pref_int_amt_paid
    this.factoring_fee_amt = factoring_fee_amt
    this.factoring_fee_amt_paid = factoring_fee_amt_paid
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  LoanAccountEntity,
  metadata
}
