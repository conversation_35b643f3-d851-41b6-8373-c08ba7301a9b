const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'welcome_package',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class WelcomePackageEntity extends BaseEntity {
  constructor({
    id,
    contract_number,
    body_data,
    url,
    created_date,
    updated_date,
    created_user,
    owner_id,
    is_delete,
    phone_number,
    is_sent_sms,
    auth_code
  }) {
    super()
    this.id = id
    this.contract_number = contract_number
    this.body_data = body_data
    this.url = url
    this.owner_id = owner_id
    this.created_date = created_date
    this.updated_date = updated_date
    this.is_delete = is_delete
    this.created_user = created_user
    this.phone_number = phone_number
    this.is_sent_sms = is_sent_sms
    this.auth_code = auth_code
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  WelcomePackageEntity,
  metadata
}
