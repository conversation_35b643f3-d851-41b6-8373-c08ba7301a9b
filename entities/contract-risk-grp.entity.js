const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'contract_risk_grp',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class ContractRiskGrpEntity extends BaseEntity {
  constructor({
    id,
    contract_number,
    cust_id,
    dpd_risk_grp,
    start_date,
    day_over_due,
    obs_risk_grp,
    user_risk_grp,
    user_update_date,
    cont_risk_grp,
    individual_risk_grp,
    cic_risk_grp,
    cic_import_date,
    final_risk_grp,
    created_date,
    updated_date,
    created_user,
    owner_id,
    job_detail_id,
    debt_ack_contract_number,
    cic_risk_grp_import_id,
    customer_code,
    dpd_num_day,
    cal_dpd_date,
    dpd_strategy,
    observation_time
  }) {
    super()
    this.id = id
    this.contract_number = contract_number
    this.cust_id = cust_id
    this.dpd_risk_grp = dpd_risk_grp
    this.start_date = start_date
    this.day_over_due = day_over_due
    this.obs_risk_grp = obs_risk_grp
    this.user_risk_grp = user_risk_grp
    this.user_update_date = user_update_date
    this.cont_risk_grp = cont_risk_grp
    this.individual_risk_grp = individual_risk_grp
    this.cic_risk_grp = cic_risk_grp
    this.cic_import_date = cic_import_date
    this.final_risk_grp = final_risk_grp
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_user = created_user
    this.owner_id = owner_id
    this.job_detail_id = job_detail_id
    this.debt_ack_contract_number = debt_ack_contract_number
    this.cic_risk_grp_import_id = cic_risk_grp_import_id
    this.customer_code = customer_code
    this.dpd_num_day = dpd_num_day
    this.cal_dpd_date = cal_dpd_date
    this.dpd_strategy = dpd_strategy
    this.observation_time = observation_time
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  ContractRiskGrpEntity,
  metadata
}
