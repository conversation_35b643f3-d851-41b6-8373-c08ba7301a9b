const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'loan_contract_limit',
  primaryKey: 'contract_limit_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class LoanContractLimitEntity extends BaseEntity {
  constructor({
    contract_limit_id,
    cust_id,
    contract_number,
    channel,
    apr_limit_amt,
    remain_limit_amt,
    tenor,
    grace_day_number,
    start_date,
    end_date,
    status,
    created_date,
    updated_date,
    owner_id,
    hold_money,
    payment_status,
    product_code,
    due_date,
    bill_day,
    periodicity,
    active_date,
    is_testing,
    created_by,
    partner_code,
    contract_type,
    phone_number
  }) {
    super()
    this.contract_limit_id = contract_limit_id
    this.cust_id = cust_id
    this.contract_number = contract_number
    this.channel = channel
    this.apr_limit_amt = apr_limit_amt
    this.remain_limit_amt = remain_limit_amt
    this.tenor = tenor
    this.grace_day_number = grace_day_number
    this.start_date = start_date
    this.end_date = end_date
    this.status = status
    this.created_date = created_date
    this.updated_date = updated_date
    this.owner_id = owner_id
    this.hold_money = hold_money
    this.payment_status = payment_status
    this.product_code = product_code
    this.due_date = due_date
    this.bill_day = bill_day
    this.periodicity = periodicity
    this.active_date = active_date
    this.is_testing = is_testing
    this.created_by = created_by
    this.partner_code = partner_code
    this.contract_type = contract_type
    this.phone_number = phone_number
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  LoanContractLimitEntity,
  metadata
}
