const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'disbursement',
  primaryKey: 'disb_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class DisbursementEntity extends BaseEntity {
  constructor({
    disb_id,
    contract_number,
    loan_id,
    amt,
    tran_id,
    tran_date,
    value_date,
    partner_code,
    receiver_name,
    partner_tran_no,
    bank_code,
    bank_name,
    bank_account,
    api_code,
    tran_status,
    created_date,
    update_date,
    created_user,
    session_id,
    owner_id,
    debt_ack_contract_number,
    branch_code,
    email,
    payment_reconciled,
    reference,
    comments,
    re_disburse_date
  }) {
    super()
    this.disb_id = disb_id
    this.contract_number = contract_number
    this.loan_id = loan_id
    this.amt = amt
    this.tran_id = tran_id
    this.tran_date = tran_date
    this.value_date = value_date
    this.partner_code = partner_code
    this.receiver_name = receiver_name
    this.partner_tran_no = partner_tran_no
    this.bank_code = bank_code
    this.bank_name = bank_name
    this.bank_account = bank_account
    this.api_code = api_code
    this.tran_status = tran_status
    this.created_date = created_date
    this.update_date = update_date
    this.created_user = created_user
    this.session_id = session_id
    this.owner_id = owner_id
    this.debt_ack_contract_number = debt_ack_contract_number
    this.branch_code = branch_code
    this.email = email
    this.payment_reconciled = payment_reconciled
    this.reference = reference
    this.comments = comments
    this.re_disburse_date = re_disburse_date
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  DisbursementEntity,
  metadata
}
