const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'loan_order_fees',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class LoanOrderFeesEntity extends BaseEntity {
  constructor({
    id,
    fee_id,
    installment_id,
    cal_fee_date,
    fee_rate,
    fee_type,
    fee_amount,
    owner_id,
    is_testing,
    created_date,
    updated_date,
    created_by,
    contract_number,
    debt_ack_contract_number,
    prin_amount,
    flag_active
  }) {
    super()
    this.id = id
    this.fee_id = fee_id
    this.installment_id = installment_id
    this.cal_fee_date = cal_fee_date
    this.fee_rate = fee_rate
    this.fee_type = fee_type
    this.fee_amount = fee_amount
    this.owner_id = owner_id
    this.is_testing = is_testing
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_by = created_by
    this.contract_number = contract_number
    this.debt_ack_contract_number = debt_ack_contract_number
    this.prin_amount = prin_amount
    this.flag_active = flag_active
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  LoanOrderFeesEntity,
  metadata
}
