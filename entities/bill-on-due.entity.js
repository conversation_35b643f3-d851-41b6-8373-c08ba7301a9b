const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'bill_on_due',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'status'
}

class BillOnDueEntity extends BaseEntity {
  constructor({
    id,
    contract_number,
    debt_ack_contract_number,
    amount,
    remain_amount,
    type,
    num_cycle,
    on_due_date,
    payment_status,
    start_date,
    end_date,
    due_date,
    owner_id,
    created_by,
    created_date,
    updated_date,
    installment_id,
    is_annex,
    annex_number,
    status,
    payment_priority,
    accept_payment_date,
    invoiced_date
  }) {
    super()
    this.id = id
    this.contract_number = contract_number
    this.debt_ack_contract_number = debt_ack_contract_number
    this.amount = amount
    this.remain_amount = remain_amount
    this.type = type
    this.num_cycle = num_cycle
    this.on_due_date = on_due_date
    this.payment_status = payment_status
    this.start_date = start_date
    this.end_date = end_date
    this.due_date = due_date
    this.owner_id = owner_id
    this.created_by = created_by
    this.created_date = created_date
    this.updated_date = updated_date
    this.installment_id = installment_id
    this.is_annex = is_annex
    this.annex_number = annex_number
    this.status = status
    this.payment_priority = payment_priority
    this.accept_payment_date = accept_payment_date
    this.invoiced_date = invoiced_date
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  BillOnDueEntity,
  metadata
}
