const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'insurance',
  primaryKey: 'insur_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class InsuranceEntity extends BaseEntity {
  constructor({
    insur_id,
    loan_id,
    contract_number,
    debt_ack_contract_number,
    amort_id,
    insur_id_ctlg,
    insur_name,
    insur_type,
    insur_amt,
    start_date,
    end_date,
    flag_active,
    created_date,
    updated_date,
    owner_id,
    is_delete,
    is_testing,
    cust_id,
    id_tras,
    so_hd,
    gcn,
    so_id,
    phi,
    thue,
    file,
    request_body,
    response_body,
    created_by,
    amount,
    s3_url
  }) {
    super()
    this.insur_id = insur_id
    this.loan_id = loan_id
    this.contract_number = contract_number
    this.debt_ack_contract_number = debt_ack_contract_number
    this.amort_id = amort_id
    this.insur_id_ctlg = insur_id_ctlg
    this.insur_name = insur_name
    this.insur_type = insur_type
    this.insur_amt = insur_amt
    this.start_date = start_date
    this.end_date = end_date
    this.flag_active = flag_active
    this.created_date = created_date
    this.updated_date = updated_date
    this.owner_id = owner_id
    this.is_delete = is_delete
    this.is_testing = is_testing
    this.cust_id = cust_id
    this.id_tras = id_tras
    this.so_hd = so_hd
    this.gcn = gcn
    this.so_id = so_id
    this.phi = phi
    this.thue = thue
    this.file = file
    this.request_body = request_body
    this.response_body = response_body
    this.created_by = created_by
    this.amount = amount
    this.s3_url = s3_url
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  InsuranceEntity,
  metadata
}
