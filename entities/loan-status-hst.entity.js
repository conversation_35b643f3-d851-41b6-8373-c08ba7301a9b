const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'loan_status_hst',
  primaryKey: 'hst_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class LoanStatusHstEntity extends BaseEntity {
  constructor({
    hst_id,
    debt_ack_contract_number,
    loan_status,
    status_date,
    created_date,
    updated_date,
    created_user,
    owner_id,
    flag_active
  }) {
    super()
    this.hst_id = hst_id
    this.debt_ack_contract_number = debt_ack_contract_number
    this.loan_status = loan_status
    this.status_date = status_date
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_user = created_user
    this.owner_id = owner_id
    this.flag_active = flag_active
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  LoanStatusHstEntity,
  metadata
}
