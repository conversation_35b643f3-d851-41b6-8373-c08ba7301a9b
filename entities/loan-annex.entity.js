const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'loan_annex',
  primaryKey: 'annex_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class LoanAnnexEntity extends BaseEntity {
  constructor({
    annex_id,
    annex_number,
    annex_type,
    annex_status,
    status_date,
    termination_motive,
    amendment_type,
    effect_start_date,
    termination_date,
    prin_amt,
    penalities_amt,
    discount_amt,
    frees_amt,
    insur_adjust_amt,
    instal_adjust_amt,
    already_due_amt,
    lpi_amt,
    created_date,
    updated_date,
    created_by,
    owner_id,
    debt_ack_contract_number,
    penalty_rate,
    total_amt,
    account_request_status,
    due_capital_amt,
    due_interest_amt,
    due_fee_amt,
    non_allocate_amt,
    cod_fee_amt,
    total_bill_amt,
    lpi_interest_amt,
    lpi_capital_amt,
    penalty_fee_amt
  }) {
    super()
    this.annex_id = annex_id
    this.annex_number = annex_number
    this.annex_type = annex_type
    this.annex_status = annex_status
    this.status_date = status_date
    this.termination_motive = termination_motive
    this.amendment_type = amendment_type
    this.effect_start_date = effect_start_date
    this.termination_date = termination_date
    this.prin_amt = prin_amt
    this.penalities_amt = penalities_amt
    this.discount_amt = discount_amt
    this.frees_amt = frees_amt
    this.insur_adjust_amt = insur_adjust_amt
    this.instal_adjust_amt = instal_adjust_amt
    this.already_due_amt = already_due_amt
    this.lpi_amt = lpi_amt
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_by = created_by
    this.owner_id = owner_id
    this.debt_ack_contract_number = debt_ack_contract_number
    this.penalty_rate = penalty_rate
    this.total_amt = total_amt
    this.account_request_status = account_request_status
    this.due_capital_amt = due_capital_amt
    this.due_interest_amt = due_interest_amt
    this.due_fee_amt = due_fee_amt
    this.non_allocate_amt = non_allocate_amt
    this.cod_fee_amt = cod_fee_amt
    this.total_bill_amt = total_bill_amt
    this.lpi_interest_amt = lpi_interest_amt
    this.lpi_capital_amt = lpi_capital_amt
    this.penalty_fee_amt = penalty_fee_amt
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  LoanAnnexEntity,
  metadata
}
