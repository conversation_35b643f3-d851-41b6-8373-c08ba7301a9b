const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'payment',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class PaymentEntity extends BaseEntity {
  constructor({
    id,
    installment_amort,
    partner_code,
    payment_date,
    status,
    owner_id,
    is_testing,
    created_date,
    updated_date,
    created_by,
    contract_number,
    debt_ack_contract_number,
    non_allocated_amt,
    reference,
    payment_method,
    tran_id,
    tran_date,
    value_date,
    cancel_date,
    last_allocation_date,
    customer_name,
    tran_status,
    tran_desc,
    comments,
    comment1,
    comment2,
    bank_code,
    bank_name,
    bank_account,
    session_id,
    ccycd,
    partner_tran_no,
    payment_reconciled,
    pay_type,
    original_pay_id
  }) {
    super()
    this.id = id
    this.installment_amort = installment_amort
    this.partner_code = partner_code
    this.payment_date = payment_date
    this.status = status
    this.owner_id = owner_id
    this.is_testing = is_testing
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_by = created_by
    this.contract_number = contract_number
    this.debt_ack_contract_number = debt_ack_contract_number
    this.non_allocated_amt = non_allocated_amt
    this.reference = reference
    this.payment_method = payment_method
    this.tran_id = tran_id
    this.tran_date = tran_date
    this.value_date = value_date
    this.cancel_date = cancel_date
    this.last_allocation_date = last_allocation_date
    this.customer_name = customer_name
    this.tran_status = tran_status
    this.tran_desc = tran_desc
    this.comments = comments
    this.comment1 = comment1
    this.comment2 = comment2
    this.bank_code = bank_code
    this.bank_name = bank_name
    this.bank_account = bank_account
    this.session_id = session_id
    this.ccycd = ccycd
    this.partner_tran_no = partner_tran_no
    this.payment_reconciled = payment_reconciled
    this.pay_type = pay_type
    this.original_pay_id = original_pay_id
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  PaymentEntity,
  metadata
}
