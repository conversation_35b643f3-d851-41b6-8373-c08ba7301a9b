const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'loan_risk_grp',
  primaryKey: 'risk_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class LoanRiskGrpEntity extends BaseEntity {
  constructor({
    risk_id,
    cust_id,
    loan_id,
    contract_number,
    risk_type,
    risk_date,
    risk_grp_val,
    dpd,
    total_bal_amt,
    outs_prin,
    prin_bal_due,
    int_bal_due,
    created_user,
    created_date,
    updated_date,
    owner_id,
    is_delete,
    flag_active,
    debt_ack_contract_number
  }) {
    super()
    this.risk_id = risk_id
    this.cust_id = cust_id
    this.loan_id = loan_id
    this.contract_number = contract_number
    this.risk_type = risk_type
    this.risk_date = risk_date
    this.risk_grp_val = risk_grp_val
    this.dpd = dpd
    this.total_bal_amt = total_bal_amt
    this.outs_prin = outs_prin
    this.prin_bal_due = prin_bal_due
    this.int_bal_due = int_bal_due
    this.created_user = created_user
    this.created_date = created_date
    this.updated_date = updated_date
    this.owner_id = owner_id
    this.is_delete = is_delete
    this.flag_active = flag_active
    this.debt_ack_contract_number = debt_ack_contract_number
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  LoanRiskGrpEntity,
  metadata
}
