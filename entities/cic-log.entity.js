const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'cic_log',
  primaryKey: 'cic_log_id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: ''
}

class CicLogEntity extends BaseEntity {
  constructor({
    cic_log_id,
    cust_id,
    cust_name,
    cic_id,
    risk_type,
    risk_date,
    risk_grp_val,
    risk_grp_val_es,
    total_bal_amt_es,
    credit_institution_name,
    product_code,
    created_user,
    created_date,
    updated_date,
    owner_id
  }) {
    super()
    this.cic_log_id = cic_log_id
    this.cust_id = cust_id
    this.cust_name = cust_name
    this.cic_id = cic_id
    this.risk_type = risk_type
    this.risk_date = risk_date
    this.risk_grp_val = risk_grp_val
    this.risk_grp_val_es = risk_grp_val_es
    this.total_bal_amt_es = total_bal_amt_es
    this.credit_institution_name = credit_institution_name
    this.product_code = product_code
    this.created_user = created_user
    this.created_date = created_date
    this.updated_date = updated_date
    this.owner_id = owner_id
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  CicLogEntity,
  metadata
}
