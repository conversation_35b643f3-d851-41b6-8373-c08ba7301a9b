const BaseEntity = require('./base.entity')

const metadata = {
  tableName: 'payment_detail',
  primaryKey: 'id',
  createdDateColumn: 'created_date',
  updatedDateColumn: 'updated_date',
  flagActiveColumn: 'flag_active'
}

class PaymentDetailEntity extends BaseEntity {
  constructor({
    id,
    bill_id,
    payment_id,
    amount,
    owner_id,
    is_testing,
    created_date,
    updated_date,
    created_by,
    flag_active,
    post_affect,
    debt_ack_contract_number,
    value_date,
    cancel_date
  }) {
    super()
    this.id = id
    this.bill_id = bill_id
    this.payment_id = payment_id
    this.amount = amount
    this.owner_id = owner_id
    this.is_testing = is_testing
    this.created_date = created_date
    this.updated_date = updated_date
    this.created_by = created_by
    this.flag_active = flag_active
    this.post_affect = post_affect
    this.debt_ack_contract_number = debt_ack_contract_number
    this.value_date = value_date
    this.cancel_date = cancel_date
  }

  async save() {
    return super.save(metadata)
  }
}

module.exports = {
  PaymentDetailEntity,
  metadata
}
