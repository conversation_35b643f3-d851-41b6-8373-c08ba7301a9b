const paymentRepo = require('../repositories/payment-repo')
const debtAckContractRepo = require('../repositories/debt-ack-contract-repo')
const installmentRepo = require('../repositories/installment-repo')
const paymentDetailRepo = require('../repositories/payment-detail-repo')
const camelcaseKeys = require('camelcase-keys')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const constant = require('../utils/constant')
const common = require('../utils/common')
const tranLogRepo = require('../repositories/tran-log-repo')
const crmService = require('../other-services/crm-service')
const moment = require('moment')
const loanAnnexRepo = require('../repositories/loan-annex-repo')
const contractRiskGrpRepo = require('../repositories/contract-risk-grp-repo')
const contractRiskGrpService = require('../services/contract-risk-grp-service')
const irService = require('../services/ir-service')
const billService = require('../services/bill-service')
const loanAccountRepo = require('../repositories/debt-ack-contract-repo')
const lodash = require('lodash')
const loanAccountV2Repo = require('./../repositories/loan-account-repo')
const productService = require('../other-services/product-service')
const backendMobileService = require('../other-services/backend-mobile-service')
const actionAuditService = require('../other-services/action-audit-service')
const repaymentService = require('./repayment-service')
const loanAnnexService = require('../services/loan-annex-service')
const loanAnnexPendingRepo = require('../repositories/loan-annex-pending-repo')
const loanAmortRepo = require('../repositories/loan-amort-repo')
const { isCashLoanPartnerCode } = require('../utils/helper')
const {ANNEX} = require("../utils/constant");

/**
 * Ham thanh toan cho khach hang
 * @param {*} req
 * @param {*} res
 * @returns
 */
const usagePayment = async function (payload) {
  try {
    console.log('req body usagePayment: ', JSON.stringify(payload))
    if (
      !payload ||
      payload.installmentAmort == undefined ||
      !payload.partnerCode ||
      !payload.paymentDate ||
      !payload.debtAckContractNumber
    ) {
      return {
        statusCode: 400,
        code: 1,
        message: '[MC-LMS] Ban phai nhap du lieu dau vao'
      }
    }
    const checkLoanAccountExisted = await debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, {
      debtAckContractNumber: payload.debtAckContractNumber
    })

    if (checkLoanAccountExisted.rowCount == 0) {
      return {
        statusCode: 200,
        code: 3,
        message: '[MC-LMS] Khong tim thay ma hop dong'
      }
    }
    const loanAccount = checkLoanAccountExisted.rows[0]
    payload.status = constant.PAYMENT.STATUS.ACTIVE
    payload.ownerId = loanAccount.owner_id || constant.config.ownerId
    payload.isTesting = constant.config.isTesting
    !payload.createdBy && (payload.createdBy = constant.config.createdBy)
    payload.reference = common.makeId(8).toUpperCase()
    payload.ccycd = payload.ccycd || 'VND'
    !payload.valueDate && (payload.valueDate = common.formatDate({ format: constant.DATE_FORMAT.YYYYMMDD_HHmmss }))
    !payload.payType && (payload.payType = constant.PAYMENT.PAY_TYPE.IN)
    payload.contractNumber = loanAccount.contract_number
    if (payload.installmentAmort) {
      const rsInsertPayment = await paymentRepo.insertPayment(payload)
      if (!rsInsertPayment) {
        return {
          statusCode: 200,
          code: 3,
          message: '[MC-LMS] Thanh toan that bai'
        }
      }
      insertTranLog(payload, loanAccount, payload.installmentAmort)
      await loanAccountV2Repo.updateLoanAccountAmount({
        debtAckContractNumber: payload.debtAckContractNumber,
        toCollect: -payload.installmentAmort,
        nonAllocationAmt: payload.installmentAmort
      })
      if (
        isCashLoanPartnerCode(loanAccount.partner_code) ||
        global.config?.data?.mobile?.listChannel.split(',').includes(loanAccount.channel)
      ) {
        backendMobileService.callReceiveMoneyBE(
          payload.debtAckContractNumber,
          payload.installmentAmort,
          payload.transactionId,
          loanAccount.channel ?? loanAccount.partner_code
        )
      }
      actionAuditService.saveActionAudit(
        payload.debtAckContractNumber,
        { actionAuditType: 'REPAYMENT', actionCodeType: 'RECEIVE_REPAYMENT_FROM' },
        { title: payload.customerName, createdUser: payload.createdBy }
      )
    }
    const result = await repaymentService.doRepayment(payload)
    if (result.code == 0) {
      if (
        isCashLoanPartnerCode(loanAccount.partner_code) &&
        result?.data?.totalNonAllocateAmt > 0 &&
        payload.installmentAmort
      ) {
        loanAnnexService.handleCalculatedAnnex(loanAccount, result?.data?.totalNonAllocateAmt, payload.paymentDate)
      }
      if (global.config?.data?.mobile?.listChannel.split(',').includes(loanAccount.channel)) {
        await loanAnnexService.cancelAnnex({
          debtAckContractNumber: payload.debtAckContractNumber,
          scanDate: payload.paymentDate
        })
      }
    }
    payload.installmentAmort && (result.data = { imx_payment_ref: payload.reference })
    return result
  } catch (err) {
    console.log(err)
    console.error('Error while usagePayment: ', err.message)
    return { statusCode: 500, code: 99, message: err.message }
  }
}
const usageSuspendPayment = async function (payload) {
  try {
    console.log('req body usageSuspendPayment: ', JSON.stringify(payload))
    if (
      !payload ||
      !payload.partnerCode ||
      !payload.paymentDate ||
      !payload.debtAckContractNumber
    ) {
      return {
        statusCode: 400,
        code: 1,
        message: '[MC-LMS] Ban phai nhap du lieu dau vao'
      }
    }
    const checkLoanAccountExisted = await debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, {
      debtAckContractNumber: payload.debtAckContractNumber
    })

    if (checkLoanAccountExisted.rowCount == 0) {
      return {
        statusCode: 200,
        code: 3,
        message: '[MC-LMS] Khong tim thay ma hop dong'
      }
    }
    const loanAccount = checkLoanAccountExisted.rows[0]
    payload.status = constant.PAYMENT.STATUS.ACTIVE
    payload.ownerId = loanAccount.owner_id || constant.config.ownerId
    payload.isTesting = constant.config.isTesting
    !payload.createdBy && (payload.createdBy = constant.config.createdBy)
    payload.reference = common.makeId(8).toUpperCase()
    payload.ccycd = payload.ccycd || 'VND'
    !payload.valueDate && (payload.valueDate = common.formatDate({ format: constant.DATE_FORMAT.YYYYMMDD_HHmmss }))
    !payload.payType && (payload.payType = constant.PAYMENT.PAY_TYPE.IN)
    payload.contractNumber = loanAccount.contract_number

    const result = await repaymentService.doRepaymentSuspend(payload)
    payload.installmentAmort && (result.data = { imx_payment_ref: payload.reference })
    return result
  } catch (err) {
    console.log(err)
    console.error('Error while usagePayment: ', err.message)
    return { statusCode: 500, code: 99, message: err.message }
  }
}

async function insertTranLog(payload, loanAccObj, amt) {
  const dateNow = moment().toDate()
  const pl = {
    contractNumber: loanAccObj.contract_number,
    loanId: loanAccObj.loan_id,
    tranType: 'REPAYMENT',
    refId: payload.transactionId,
    amtNumber: amt,
    tranDate: dateNow,
    valueDate: dateNow,
    tranDesc: 'repayment for ' + loanAccObj.debt_ack_contract_number,
    // tranStatus: '',
    createdUser: global.createdBy,
    sessionId: dateNow.getTime(),
    ownerId: constant.config.ownerId
  }
  await tranLogRepo.insTranLog(global.poolWrite, pl)
}
const checkRepayment = async function (req, res) {
  try {
    const pl = req.body
    if (!pl.partner_code || !pl.contract_number) {
      return res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Truyen thieu truong thong tin du lieu'
        })
      )
    }
    const plQuery = {
      debtAckContractNumber: pl.contract_number
    }
    const listFunc = await Promise.all([
      billOnDueRepo.getBillOnDuePaymentNotComplete(global.poolRead, plQuery),
      installmentRepo.getInsIsNotClosed(global.poolRead, plQuery),
      debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, plQuery)
    ])
    const rsBill = listFunc[0]
    const rsInstall = listFunc[1]
    const rsLoanAccount = listFunc[2]

    if (rsLoanAccount.rowCount == 0) {
      return res.status(200).json(
        (res.body = {
          code: 2,
          message: '[LMS-MC] Khong tim thay khe uoc'
        })
      )
    }
    const loanAccObj = rsLoanAccount.rowCount > 0 ? rsLoanAccount.rows[0] : {}
    const nonAllocationAmt = loanAccObj.non_allocation_amt ? loanAccObj.non_allocation_amt : 0

    let totalDueAmount = 0
    const currentDate = moment().toDate()
    currentDate.setDate(currentDate.getDate() + global.calcuCfg.futureDateRePayment)
    let due_date = moment().toDate()
    const paid_installment = 1
    let totalDueAmountInsm = 0
    for (const i in rsInstall.rows) {
      const installObj = rsInstall.rows[i]
      if (installObj.end_date.getTime() <= currentDate.getTime()) {
        totalDueAmountInsm += Number(installObj.remain_amount)
        due_date = installObj.end_date
      }
    }
    let flagAnnex = false
    for (const i in rsBill.rows) {
      const billObj = rsBill.rows[i]
      if (billObj.is_annex == 1 && billObj.on_due_date.getTime() <= moment().toDate().getTime()) {
        totalDueAmount += Number(billObj.remain_amount)
        due_date = billObj.on_due_date
        flagAnnex = true
      } else if (billObj.is_annex == 0) {
        totalDueAmount += Number(billObj.remain_amount)
      }
    }
    if (!flagAnnex) totalDueAmount = totalDueAmount + totalDueAmountInsm
    const custObj = await crmService.getCustomerInfo(
      global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj.cust_id,
      {}
    )
    // console.log('custObj:', custObj)
    res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Lay thong tin thanh cong',
        data: {
          contract_number: loanAccObj.debt_ack_contract_number,
          contract_status_code: loanAccObj.payment_status == 0 ? 'TER' : 'ACT',
          customer_name: custObj.fullName,
          identity_card_id: custObj.idNumber,
          total_due_amount: Math.max(Number(totalDueAmount) - nonAllocationAmt, 0),
          paid_installment,
          due_date: common.convertDatetoString(due_date, 'dd-mm-yyyy'),
          response_code: 'S200-RPM',
          response_message: 'checked contract successfully'
        }
      })
    )
  } catch (err) {
    console.log(err)
    res.status(500).json(
      (res.body = {
        code: 99,
        message: err.message
      })
    )
  }
}

const checkRepaymentV2 = async function (req, res) {
  try {
    const pl = req.body
    if (!pl.partner_code || pl.contractNumber.length == 0) {
      return res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Truyen thieu truong thong tin du lieu'
        })
      )
    }
    const contractList = []

    for (const value of pl.contractNumber) {
      const plQuery = {
        debtAckContractNumber: value
      }
      // khuend
      const listFunc = await Promise.all([
        billOnDueRepo.getBillOnDuePaymentNotCompleteV2(global.poolRead, plQuery),
        installmentRepo.getInsIsNotClosed(global.poolRead, plQuery),
        debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, plQuery),
        contractRiskGrpRepo.findContractRiskGrp(global.poolRead, plQuery),
        paymentRepo.getAllByDebtAckContractNumber(global.poolRead, plQuery),
        installmentRepo.getNextDueDateIns(global.poolRead, plQuery),
        loanAnnexRepo.findAnnexInitByDebtAckContractNumber(value),
        paymentRepo.getLastPaymentAmt(global.poolRead, plQuery),
        installmentRepo.getEmi(global.poolRead, plQuery),
        installmentRepo.getCurrentIrNumCycle(global.poolRead, plQuery),
        installmentRepo.getSumAmount(global.poolRead, plQuery, '2,5'),
        installmentRepo.getSumAmount(global.poolRead, plQuery, '1,2,5'),
        installmentRepo.getMaxIrNumCycle(global.poolRead, plQuery),
        loanAnnexPendingRepo.findAnnexPendingByKunnNumber(value),
        installmentRepo.getSumAmountV2(global.poolRead, plQuery, '2,5'),
        installmentRepo.getSumAmountV2(global.poolRead, plQuery, '1,2,5'),
      ])
      const rsBill = listFunc[0]
      const rsInstall = listFunc[1]
      const rsLoanAccount = listFunc[2]
      const getRiskGroup = listFunc[3].rows[0] || {}
      const rsNextDueDate = listFunc[5]
      const annexAmt = listFunc[6]
      const lastPaymentAmt = listFunc[7]
      const emi = listFunc[8]
      const installmentSEDate = listFunc[5].rows[0]
      const nextDueDate = rsNextDueDate.rows[0]
        ? moment(new Date(rsNextDueDate.rows[0].end_date)).format('YYYY-MM-DD hh:mm:ss')
        : null

      const loanAccObj = rsLoanAccount.rowCount > 0 ? rsLoanAccount.rows[0] : {}

      const currentIrNumCycle = listFunc[9].rows[0] ? Number(listFunc[9].rows[0].ir_num_cycle) : 0
      const sumAmountType25 = Number(listFunc[10].rows[0]?.sum || 0)
      const sumAmountType125 = Number(listFunc[11].rows[0]?.sum || 0)
      const maxIrNumCycle = Number(listFunc[12].rows[0]?.ir_num_cycle || 0)
      const rsPendingAnnex = listFunc[13]

      // Với các sản phẩm cũ những ngày ondue số tiền thanh toán kỳ tiếp theo sẽ bằng 0
      // Không rõ tại sao lại thế nên sửa lại hàm V2 để áp dụng cho các sản phẩm từ DNSE trở đi (đầu năm 2025) cho giống lms cash loan
      const sumAmountType25V2 = Number(listFunc[14].rows[0]?.sum || 0)
      const sumAmountType125V2 = Number(listFunc[15].rows[0]?.sum || 0)

      let nextDuePayment =
        currentIrNumCycle < maxIrNumCycle && loanAccObj.contract_type == constant.CONTRACT_TYPE.CREDITLINE
          ? sumAmountType25
          : sumAmountType125

      if ([constant.PARTNER_CODE.DNSE, constant.PARTNER_CODE.VUIAPP].includes(loanAccObj.partner_code)) {
        nextDuePayment =
            currentIrNumCycle < maxIrNumCycle && loanAccObj.contract_type == constant.CONTRACT_TYPE.CREDITLINE
                ? sumAmountType25V2
                : sumAmountType125V2
      }

      const nonAllocationAmt = loanAccObj.non_allocated_amt ? loanAccObj.non_allocated_amt : 0
      let totalDueAmount = 0
      const paid_installment = 1
      let totalDueAmountInsm = 0

      for (const i in rsNextDueDate.rows) {
        const installObj = rsInstall?.rows[i]
        totalDueAmountInsm += Number(installObj?.remain_amount || 0)
      }

      let flagAnnex = false
      let printOverDue = 0
      let intOverDue = 0
      let lpi = 0
      let feeOverDue = 0
      if (rsBill.rows.length > 0) {
        for (const i in rsBill.rows) {
          const billObj = rsBill.rows[i]
          if (billObj.is_annex == 1 && billObj.on_due_date.getTime() <= moment().toDate().getTime()) {
            totalDueAmount += Number(billObj.remain_amount)
            flagAnnex = true
          } else if (billObj.is_annex == 0) {
            totalDueAmount += Number(billObj?.remain_amount || 0)
          }
          if (billObj.type === constant.BILL_ON_DUE.TYPE.PRIN) {
            printOverDue += Number(billObj?.remain_amount || 0)
          }
          if (billObj.type === constant.BILL_ON_DUE.TYPE.INT) {
            intOverDue += Number(billObj?.remain_amount || 0)
          }
          if (billObj.type === constant.BILL_ON_DUE.TYPE.FEE) {
            feeOverDue += Number(billObj?.remain_amount || 0)
          }
          if (
            billObj?.type === constant.BILL_ON_DUE.TYPE.LPI_PRIN ||
            billObj?.type === constant.BILL_ON_DUE.TYPE.LPI_INT
          ) {
            lpi += Number(billObj.remain_amount)
          }
        }
      }

      let totalPayment = 0
      let totalPaymentInMonth = 0
      if (listFunc[4].rows.length > 0) {
        for (const value of listFunc[4].rows) {
          totalPayment += Number(value.installment_amort)
          if (moment(value.payment_date).format('YYYY-MM') == moment().format('YYYY-MM')) {
            totalPaymentInMonth += Number(value.installment_amort)
          }
        }
      }
      let confirm_partial_annex = false
      if (rsPendingAnnex.length) {
        confirm_partial_annex = true
      }
      if (!flagAnnex) totalDueAmount = totalDueAmount + totalDueAmountInsm
      const custObj = await crmService.getCustomerInfo(
        global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj.cust_id,
        {}
      )
      const currentDateDpd = new Date()
      const productInfo = await productService.getProductInfo(loanAccObj.product_code)
      const data = {
        contract_number: loanAccObj.debt_ack_contract_number,
        contract_status_code: loanAccObj.payment_status == 0 ? 'TER' : 'ACT',
        customer_name: custObj.fullName,
        identity_card_id: custObj.idNumber,
        total_due_amount: Number(totalDueAmount) - nonAllocationAmt,
        paid_installment,
        due_date: nextDueDate,
        response_code: 'S200-RPM',
        response_message: 'checked contract successfully',
        confirm_partial_annex,
        // dpdVas: installmentSEDate
        //   ? (common.convertDatetoString(currentDateDpd, 'yyyy-mm-dd') == common.convertDatetoString(installmentSEDate?.end_date, 'yyyy-mm-dd'))
        //       ? 0
        //       : Math.round(Number((new Date(common.convertDatetoString(currentDateDpd, 'yyyy-mm-dd')).getTime() - new Date(common.convertDatetoString(installmentSEDate?.end_date, 'yyyy-mm-dd')).getTime()) / (1000 * 24 * 60 * 60)))
        //   : loanAccObj.dpd == null ? getRiskGroup?.dpd_num_day : loanAccObj.dpd,
        dpdStrategy: getRiskGroup.dpd_strategy,
        totalDebt: Number(loanAccObj.to_collect),
        printOverDue: Number(printOverDue),
        intOverDue: Number(intOverDue),
        lpi: Number(lpi),
        feeOverDue,
        prinOutstading: Number(loanAccObj.prin_amt),
        nextDueDate,
        nextDuePaymentTotal: nextDuePayment, // update theo yc a.Rin
        totalPaymentInMonth: Number(totalPaymentInMonth),
        lastPaymentDate: lastPaymentAmt.rows[0]?.payment_date
          ? moment(lastPaymentAmt.rows[0].payment_date).format('YYYY-MM-DD HH:mm:ss')
          : null,
        totalPayment,
        statusContract: null,
        productName: loanAccObj.product_code,
        annexAmt: annexAmt[0] ? Number(annexAmt[0].total_amt) + Number(loanAccObj.non_allocation_amt) : 0,
        annexId: annexAmt[0] ? annexAmt[0].annex_id : null,
        bucketBom: getBucketCurrent(getRiskGroup.dpd_num_day),
        bucketCurrent: getBucketCurrent(getRiskGroup.dpd_num_day),
        emi: emi.rows[0] ? Number(emi.rows[0].amt) : 0,
        nextDuePaymentToCollected: nextDuePayment + Number(loanAccObj.to_collect),
        lastPaymentAmt: lastPaymentAmt.rows[0] ? Number(lastPaymentAmt.rows[0].installment_amort) : null,
        insuranceAmt: 0,
        insuranceCode: null,
        productNameCol: productInfo?.mktDesc
      }

      data.dpdVas = loanAccObj.dpd != null && loanAccObj.dpd != undefined ? loanAccObj.dpd : getRiskGroup?.dpd_num_day
      if (data.dpdVas == 0) {
        if (
          common.convertDatetoString(currentDateDpd, 'yyyy-mm-dd') !==
          common.convertDatetoString(installmentSEDate?.end_date, 'yyyy-mm-dd')
        ) {
          data.dpdVas = Math.round(
            Number(
              (new Date(common.convertDatetoString(currentDateDpd, 'yyyy-mm-dd')).getTime() -
                new Date(common.convertDatetoString(installmentSEDate?.end_date, 'yyyy-mm-dd')).getTime()) /
                (1000 * 24 * 60 * 60)
            )
          )
        }
      }

      contractList.push(data)

      try {
        const results = await Promise.all([
          loanAccountRepo.findByContractNumberAndStatusMQueue(global.poolRead, plQuery),
          contractRiskGrpRepo.findContractRiskGrpMQueue(global.poolRead, plQuery),
          installmentRepo.getNextDueDateIns(global.poolRead, plQuery)
        ])
        const dataQueue = {}
        const loan_account = results[0]
        const contract_risk_grp = results[1]
        const installment = results[2].rows[0]
        dataQueue.debtAckContractNumber = plQuery.debtAckContractNumber
        const loanAccountData = loan_account.rows[0]
        const currentDate = new Date()

        if (loanAccountData) {
          // loanAccountData.dpd = installment
          //   ? (common.convertDatetoString(currentDate, 'yyyy-mm-dd') == common.convertDatetoString(installment?.end_date, 'yyyy-mm-dd'))
          //       ? 0
          //       : Math.round(Number((new Date(common.convertDatetoString(currentDate, 'yyyy-mm-dd')).getTime() - new Date(common.convertDatetoString(installment?.end_date, 'yyyy-mm-dd')).getTime()) / (1000 * 24 * 60 * 60)))
          //   : loanAccountData.dpd == null ? contract_risk_grp.rows[0]?.dpd_num_day : loanAccountData.dpd

          if (loanAccountData.dpd == null || loanAccountData.dpd == undefined) {
            loanAccountData.dpd = contract_risk_grp.rows[0]?.dpd_num_day
          }
          if (loanAccountData.dpd == 0) {
            if (
              common.convertDatetoString(currentDate, 'yyyy-mm-dd') !==
              common.convertDatetoString(installment?.start_date, 'yyyy-mm-dd')
            ) {
              loanAccountData.dpd = Math.round(
                Number(
                  (new Date(common.convertDatetoString(currentDate, 'yyyy-mm-dd')).getTime() -
                    new Date(common.convertDatetoString(installment?.end_date, 'yyyy-mm-dd')).getTime()) /
                    (1000 * 24 * 60 * 60)
                )
              )
            }
          }

          loanAccountData.dpd_strategy =
            loanAccountData.dpd_strategy == null
              ? contract_risk_grp.rows[0]?.dpd_strategy
              : loanAccountData.dpd_strategy
          // dataQueue.loan_account = producerService.convertTimeLoanAccountGMT7(loanAccountData || {})
          // const contractRG = contract_risk_grp.rows[0]
          // dataQueue.contract_risk_grp = producerService.convertTimeRiskGroupGMT7(contractRG || {})

          // console.log('mc-dpd: ', dataQueue.debtAckContractNumber)
          // producerService.sendMessageV2(dataQueue, 'mc-dpd')
        }
      } catch (error) {
        console.log('MQueue DPD error: ', error.message)
      }
    }

    return res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Lay thong tin thanh cong',
        data: {
          contractList
        }
      })
    )
  } catch (err) {
    console.log(err)
    return res.status(500).json(
      (res.body = {
        code: 99,
        message: err.message
      })
    )
  }
}

const checkRepaymentV3 = async function (req, res) {
  try {
    const pl = req.body
    if (!pl.contractNumber) {
      return res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Truyen thieu truong thong tin du lieu'
        })
      )
    }
    const plQuery = {
      debtAckContractNumber: pl.contractNumber
    }
    const rsLoanAccount = await debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, plQuery)

    if (rsLoanAccount.rowCount == 0) {
      return res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Khong tim thay khe uoc'
        })
      )
    }
    const loanAccObj = rsLoanAccount.rows[0]
    const custObj = await crmService.getCustomerInfo(
      global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj.cust_id,
      {}
    )

    res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Lay thong tin thanh cong',
        data: {
          contract_number: loanAccObj.debt_ack_contract_number,
          contract_status_code: loanAccObj.payment_status == 0 ? 'TER' : 'ACT',
          customer_name: custObj.fullName,
          identity_card_id: custObj.idNumber,
          phone_number: custObj.phoneNumber1 || custObj.phoneNumber2 || custObj.phoneNumber3,
          to_collected: Number(loanAccObj.to_collect),
          channel: loanAccObj.partner_code,
          response_code: 'S200-RPM',
          response_message: 'checked contract successfully'
        }
      })
    )
  } catch (err) {
    console.log(err)
    res.status(500).json(
      (res.body = {
        code: 99,
        message: err.message
      })
    )
  }
}

const getBucketCurrent = function (dpd) {
  if (dpd == 0) return 'B0'
  if (dpd >= 1 && dpd <= 30) return 'B1'
  if (dpd >= 31 && dpd <= 60) return 'B2'
  if (dpd >= 61 && dpd <= 90) return 'B3'
  if (dpd >= 91 && dpd <= 120) return 'B4'
  if (dpd >= 121 && dpd <= 150) return 'B5'
  if (dpd >= 151 && dpd <= 180) return 'B6'
  if (dpd >= 181) return 'B7'
  return ''
}

const getRepaymentHistory = async function (req, res) {
  try {
    const payload = req.query

    const findLoanAcc = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(payload.debtAckContractNumber)

    if (!findLoanAcc.length) {
      return res.status(200).json(
        (res.body = {
          message: 'Do not find kunn number: ' + payload.debtAckContractNumber,
          code: 1,
          statusCode: 200
        })
      )
    }
    const rsData = await paymentRepo.getAllPaymentByDebtAckContractNumber(global.poolRead, payload)
    const result = []
    const listPayment = rsData.rows
    for (const payment of listPayment) {
      let status = 'NORMAL'
      if (
        payment.pay_type == constant.PAYMENT.PAY_TYPE.OUT ||
        payment.tran_status == constant.PAYMENT.TRANS_STATUS.TRANSFER
      ) {
        status = payment.comments
      }
      if (payment.status == constant.PAYMENT.STATUS.CANCELED) {
        status = 'CANCEL PAYMENT'
      }
      result.push({
        id: payment.id,
        type: 'Repayment',
        reference: payment.reference,
        amount: payment.installment_amort,
        dateOfRepayment: payment.payment_date,
        paymentMethod: payment.payment_method,
        nonAllocatedAmt: payment.non_allocated_amt,
        status,
        payerName: payment.customer_name || '',
        partnerCode: payment.partner_code || ''
      })
    }
    return res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Thuc hien lay thong tin thanh cong',
        data: result
      })
    )
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}
const getRepaymentHistoryDetail = async function (req, res) {
  try {
    const payload = req.query
    if (!payload.paymentId) {
      return res.status(200).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Missing payment id'
        })
      )
    }
    const [rsData, rsPayment] = await Promise.all([
      paymentDetailRepo.getAllByPaymentIdV2(global.poolRead, payload),
      paymentRepo.findById(global.poolRead, payload)
    ])
    if (rsData.rowCount == 0 || rsPayment.rowCount == 0) {
      return res.status(200).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Khong tim thay du lieu'
        })
      )
    }
    const result = []
    let record
    let totalRepayPrin = 0
    let totalRepayInt = 0
    let totalRepayFee = 0
    let totalEarlyTerFee = 0
    let totalRepayIntOfPrinLate = 0
    let totalRepayIntOfIrLate = 0
    for (const i in rsData.rows) {
      switch (rsData.rows[i].type) {
        case 1:
          record = {
            title: 'Refund capital',
            principal: rsData.rows[i].amount,
            interest: 0,
            periodicalFees: 0,
            earlyTermination: 0,
            latePaymentInterest: 0,
            nonAllocationAmt: 0,
            invoiceDate: rsData.rows[i].on_due_date,
            allocationDate: rsData.rows[i].created_date
          }
          result.push(record)
          totalRepayPrin += Number(rsData.rows[i].amount)
          break
        case 2:
          record = {
            title: 'Interest',
            principal: 0,
            interest: rsData.rows[i].amount,
            periodicalFees: 0,
            earlyTermination: 0,
            latePaymentInterest: 0,
            nonAllocationAmt: 0,
            invoiceDate: rsData.rows[i].on_due_date,
            allocationDate: rsData.rows[i].created_date
          }
          result.push(record)
          totalRepayInt += Number(rsData.rows[i].amount)
          break
        case 3:
          record = {
            title: 'Interest of principal expire',
            principal: 0,
            interest: 0,
            periodicalFees: 0,
            earlyTermination: 0,
            latePaymentInterest: rsData.rows[i].amount,
            nonAllocationAmt: 0,
            invoiceDate: rsData.rows[i].on_due_date,
            allocationDate: rsData.rows[i].created_date
          }
          result.push(record)
          totalRepayIntOfPrinLate += Number(rsData.rows[i].amount)
          break
        case 4:
          record = {
            title: 'Interest of interest expire',
            principal: 0,
            interest: 0,
            periodicalFees: 0,
            earlyTermination: 0,
            latePaymentInterest: rsData.rows[i].amount,
            nonAllocationAmt: 0,
            invoiceDate: rsData.rows[i].on_due_date,
            allocationDate: rsData.rows[i].created_date
          }
          result.push(record)
          totalRepayIntOfIrLate += Number(rsData.rows[i].amount)
          break
        case 5:
          if (rsData.rows[i].description == 'Phí phạt tất toán sớm') {
            record = {
              title: 'Early Termination',
              principal: 0,
              interest: 0,
              periodicalFees: 0,
              earlyTermination: rsData.rows[i].amount,
              latePaymentInterest: 0,
              nonAllocationAmt: 0,
              invoiceDate: rsData.rows[i].on_due_date,
              allocationDate: rsData.rows[i].created_date
            }
            result.push(record)
            totalEarlyTerFee += Number(rsData.rows[i].amount)
          } else {
            record = {
              title: 'Periodical fees',
              principal: 0,
              interest: 0,
              periodicalFees: rsData.rows[i].amount,
              earlyTermination: 0,
              latePaymentInterest: 0,
              nonAllocationAmt: 0,
              invoiceDate: rsData.rows[i].on_due_date,
              allocationDate: rsData.rows[i].created_date
            }
            result.push(record)
            totalRepayFee += Number(rsData.rows[i].amount)
          }

          break
        default:
          break
      }
    }
    const totalObj = {
      title: 'Repayment',
      principal: -1 * totalRepayPrin,
      interest: -1 * totalRepayInt,
      periodicalFees: -1 * totalRepayFee,
      earlyTermination: -1 * totalEarlyTerFee,
      latePaymentInterest: -1 * (totalRepayIntOfPrinLate + totalRepayIntOfIrLate),
      nonAllocationAmt: -1 * Number(rsPayment.rows[0].non_allocated_amt)
    }
    result.push(totalObj)

    res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Thuc hien lay thong tin thanh cong',
        data: result
      })
    )
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}

const checkRepaymentByCustId = async function (req, res) {
  try {
    const pl = req.query
    if (!pl || (!pl.custId && !pl.contractNumber && !pl.debtAckContractNumber)) {
      return res.status(400).json((res.body = { code: 1, message: '[LMS-MC] Thieu du lieu dau vao' }))
    }
    pl.status = 1
    const rsLoanAcc = await loanAccountRepo.findLoanAccByPaymentStatusNotCompleted(global.poolRead, pl)
    if (rsLoanAcc.rowCount == 0) {
      return res.status(200).json(
        (res.body = {
          code: 2,
          message: '[LMS-MC] Khong tim thay khoan vay ung voi ma KH'
        })
      )
    }
    const listResult = []
    for (const i in rsLoanAcc.rows) {
      const contractListObj = camelcaseKeys(rsLoanAcc.rows[i])
      // contractListObj.debtAckContractNumber =
      // let plInsm =
      const listFunc = await Promise.all([
        installmentRepo.findByContractNumberAndPaymentStatusNotCompleted(global.poolRead, {
          debtAckContractNumber: rsLoanAcc.rows[i].debt_ack_contract_number
        }),
        paymentRepo.getAllByDebtAckContractNumber(global.poolRead, {
          debtAckContractNumber: rsLoanAcc.rows[i].debt_ack_contract_number
        })
      ])
      contractListObj.installmentList = camelcaseKeys(listFunc[0].rows)
      contractListObj.paymentList = camelcaseKeys(listFunc[1].rows)

      listResult.push(contractListObj)
    }
    res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Success',
        data: listResult
      })
    )
  } catch (err) {
    console.log(err)
    console.error('Error while ', err.message)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}
const getPaymentHistory = async function (req, res) {
  try {
    const input = req.query
    if (!input || !input.contract_number) {
      return res.status(400).json({ response_code: 1, response_message: 'input invalid' })
    }

    const payload = {
      contractNumber: input.contract_number,
      fromDate: input.from_date ? moment(input.from_date, 'DD-MM-YYYY') : undefined,
      toDate: input.to_date ? moment(input.to_date, 'DD-MM-YYYY') : undefined,
      partnerCode: input.partner_code
    }
    const paymentList = await paymentRepo.findByCondition(payload)
    const paymentHistory = []
    paymentList.rows.forEach((element) => {
      paymentHistory.push({
        payment_date: common.convertDatetoString(element.payment_date, 'yyyy-mm-dd HH:MM:ss'),
        payment_amount: Number(element.installment_amort),
        partner_code: element.partner_code,
        partner_tran_no: element.partner_tran_no,
        pint_paid: element.type == 1 ? Number(element.pd_amount) : null,
        int_paid: element.type == 2 ? Number(element.pd_amount) : null,
        fee_paid: element.type == 5 ? Number(element.pd_amount) : null,
        lpi_paid: element.type == (3 || 4) ? Number(element.pd_amount) : null,
        redund_amt: Number(element.non_allocated_amt),
        status: 'Normal'
      })
    })

    res.body = {
      response_code: 0,
      response_message: 'success',
      contract_number: input.contract_number,
      payment_history: paymentHistory
    }
    res.status(200).json(res.body)
  } catch (err) {
    console.log(err)
    res.body = { message: err.message, command: err }
    res.status(500).json(res.body)
  }
}

const getPaymentHistoryV2 = async function (req, res) {
  try {
    const input = req.query
    if (!input || !input.contract_number) {
      return res.status(400).json({ response_code: 1, response_message: 'input invalid' })
    }

    const payload = {
      contractNumber: input.contract_number,
      fromDate: input.from_date ? moment(input.from_date, 'DD-MM-YYYY') : undefined,
      toDate: input.to_date ? moment(input.to_date, 'DD-MM-YYYY') : undefined,
      partnerCode: input.partner_code
    }
    const paymentList = await paymentRepo.findByCondition(payload)
    const paymentHistory = []
    const groupByPaymentId = lodash.groupBy(paymentList.rows, 'pay_id')
    for (const paymentId of Object.keys(groupByPaymentId)) {
      const paymentDetailList = groupByPaymentId[paymentId]
      const paymentObj = paymentDetailList[0]
      let status = 'Normal'
      if (
        paymentObj.pay_type == constant.PAYMENT.PAY_TYPE.OUT ||
        paymentObj.tran_status == constant.PAYMENT.TRANS_STATUS.TRANSFER
      ) {
        status = paymentObj.comments
      }
      if (paymentObj.status == constant.PAYMENT.STATUS.CANCELED) {
        status = 'Canceled'
      }
      const resultObj = {
        pay_id: paymentObj.pay_id,
        payment_date: common.convertDatetoString(
          paymentObj.value_date || paymentObj.payment_date,
          'yyyy-mm-dd HH:MM:ss'
        ),
        payment_amount: Number(paymentObj.installment_amort),
        partner_code: paymentObj.partner_code,
        partner_tran_no: paymentObj.partner_tran_no,
        pint_paid: 0,
        int_paid: 0,
        fee_paid: 0,
        lpi_paid: 0,
        redund_amt: Number(paymentObj.non_allocated_amt),
        status
      }
      paymentDetailList.forEach((element) => {
        if (element.type == constant.BILL_ON_DUE.TYPE.PRIN) {
          resultObj.pint_paid += Number(element.pd_amount)
        }
        if (element.type == constant.BILL_ON_DUE.TYPE.INT) {
          resultObj.int_paid += Number(element.pd_amount)
        }
        if (element.type == constant.BILL_ON_DUE.TYPE.FEE) {
          resultObj.fee_paid += Number(element.pd_amount)
        }
        if (element.type == constant.BILL_ON_DUE.TYPE.LPI_INT || element.type == constant.BILL_ON_DUE.TYPE.LPI_PRIN) {
          resultObj.lpi_paid += Number(element.pd_amount)
        }
      })
      paymentHistory.push(resultObj)
    }

    res.body = {
      response_code: 0,
      response_message: 'success',
      contract_number: input.contract_number,
      payment_history: paymentHistory
    }
    res.status(200).json(res.body)
  } catch (err) {
    console.log(err)
    res.body = { message: err.message, command: err }
    res.status(500).json(res.body)
  }
}
const usagePaymentBatchJob = async function (now = moment().format(constant.DATE_FORMAT.YYYYMMDD2)) {
  try {
    common.log('STARTING usagePaymentBatchJob ' + new Date())
    const insList = await billOnDueRepo.findAllBillPaymentNotCompleted(global.poolRead, {})
    const arrList = lodash.chunk(insList.rows, 100)
    for (const lst of arrList) {
      const promiseArr = []
      for (const debtAck of lst) {
        promiseArr.push(
          usagePayment({
            installmentAmort: 0,
            partnerCode: 'EVNFC',
            debtAckContractNumber: debtAck.debt_ack_contract_number,
            paymentDate: now,
            transactionId: moment().unix(),
            bankName: '',
            comments: 'BATCH JOB',
            comment1: '',
            comment2: ''
          })
        )
      }
      Promise.all(promiseArr)
    }
    common.log('END usagePaymentBatchJob ' + new Date())
    return { statusCode: 200, code: 0, message: 'Success usagePaymentBatchJob' }
  } catch (e) {
    console.error('Error at usagePaymentBatchJob: ', e.message)
    console.log(e)
    return { statusCode: 500, code: -1, message: e.message }
  }
}

const usageSuspendPaymentBatchJob = async function (now = moment().format(constant.DATE_FORMAT.YYYYMMDD2)) {
  try {
    common.log('STARTING usageSuspendPaymentBatchJob ' + new Date())
    const insList = await billOnDueRepo.findAllBillPaymentNotCompletedAndAcceptPayment(global.poolRead, now)
    const arrList = lodash.chunk(insList.rows, 100)
    for (const lst of arrList) {
      const promiseArr = []
      for (const debtAck of lst) {
        promiseArr.push(
            usageSuspendPayment({
            partnerCode: 'EVNFC',
            debtAckContractNumber: debtAck.debt_ack_contract_number,
            paymentDate: now,
            transactionId: moment().unix(),
            bankName: '',
            comments: 'BATCH JOB',
            comment1: '',
            comment2: ''
          })
        )
      }
      Promise.all(promiseArr)
    }
    common.log('END usageSuspendPaymentBatchJob ' + new Date())
    return { statusCode: 200, code: 0, message: 'Success usageSuspendPaymentBatchJob' }
  } catch (e) {
    console.error('Error at usageSuspendPaymentBatchJob: ', e.message)
    console.log(e)
    return { statusCode: 500, code: -1, message: e.message }
  }
}
async function transferCaseToCase(payload) {
  const { debtAckContractNumber, payId, amt, date = new Date(), createdUser } = payload
  const findLoanAcc = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)
  if (!findLoanAcc.length) {
    return { message: 'Do not find contract_number: ' + debtAckContractNumber, code: -2, statusCode: 400 }
  }

  const payment = await paymentRepo.findPaymentById(payId)

  if (!payment) {
    return { code: -2, message: `Do not find payId: ${payId}`, statusCode: 400 }
  }
  if (payment.cancel_date != null) {
    return { code: -2, message: `Pay canceled, payId: ${payId}`, statusCode: 400 }
  }
  if (payment.debt_ack_contract_number == debtAckContractNumber) {
    return { code: -2, message: 'Can not transfer in the same debt ack contract number', statusCode: 400 }
  }
  const reduntAmt = +payment.non_allocated_amt

  if (reduntAmt < Number(amt) || reduntAmt == 0 || amt <= 0) {
    return { code: -2, message: `Do not transfer amt ${amt} with redundAmt: ${reduntAmt}`, statusCode: 400 }
  }
  const dataUpdatePayment = {
    payId,
    nonAllocatedAmt: reduntAmt - Number(amt),
    comments: `Transfer to ${debtAckContractNumber}`,
    tranStatus: constant.PAYMENT.TRANS_STATUS.TRANSFER
  }
  await Promise.all([
    paymentRepo.updateNonAllocatedAmtById(dataUpdatePayment),
    loanAccountV2Repo.updateLoanAccountAmount({
      debtAckContractNumber: payment.debt_ack_contract_number,
      toCollect: amt,
      nonAllocationAmt: -1 * amt
    })
  ])

  await usagePayment({
    installmentAmort: amt,
    partnerCode: payment.partner_code,
    debtAckContractNumber,
    paymentDate: moment(date).format(constant.DATE_FORMAT.YYYYMMDD2),
    originalPayId: payment.id,
    createBy: createdUser,
    transactionId: payment.partner_tran_no,
    tranId: payment.tran_id,
    reference: payment.reference,
    comments: `Received from ${payment.debt_ack_contract_number}`,
    comment1: payment.comment1,
    comment2: payment.comment2,
    ccycd: payment.ccycd || 'VND',
    bankCode: payment.bank_code,
    bankName: payment.bank_name,
    bankAccount: payment.bank_account,
    tranStatus: constant.PAYMENT.TRANS_STATUS.RECEIVE,
    payType: constant.PAYMENT.PAY_TYPE.OUT
  })
  actionAuditService.saveActionAudit(
    debtAckContractNumber,
    { actionAuditType: 'CASE_TO_CASE_TRANSFER', actionCodeType: 'CASE_TO_CASE' },
    { createdUser: payload.createdBy }
  )
  return {
    code: 0,
    statusCode: 200,
    message: 'Transfer case successfully'
  }
}
function validateCancelPayment(payload, findLoanAcc, payment) {
  const { debtAckContractNumber, payId } = payload

  if (!findLoanAcc.length) {
    return {
      message: 'Do not find kunn number: ' + debtAckContractNumber,
      isSuccess: false
    }
  }
  if (!payment || payment.debt_ack_contract_number != debtAckContractNumber) {
    return {
      message: `Can not find payId: ${payId} with kunn number ${debtAckContractNumber}`,
      isSuccess: false
    }
  }
  if (payment.cancel_date != null || payment.status == constant.PAYMENT.STATUS.CANCELED) {
    return {
      isSuccess: false,
      message: `Pay canceled, payId: ${payId}`
    }
  }
  if (new Date(payment.payment_date) > new Date(payload.cancelDate)) {
    return {
      isSuccess: false,
      message: 'Payment date invalid'
    }
  }
  return {
    isSuccess: true
  }
}
async function cancelPayment(payload) {
  !payload.cancelDate && (payload.cancelDate = new Date())
  !payload.createdUser && (payload.createdUser = 'system')

  const { debtAckContractNumber, payId, cancelDate, createdUser } = payload

  const [findLoanAcc, payment] = await Promise.all([
    loanAccountV2Repo.findLoanAccByDebtAckContractNumber(debtAckContractNumber),
    paymentRepo.findPaymentById(payId)
  ])

  const validated = validateCancelPayment(payload, findLoanAcc, payment)

  if (!validated.isSuccess) {
    return {
      code: -2,
      message: validated.message,
      statusCode: 400
    }
  }
  const loanAccount = findLoanAcc[0]

  if (payment.non_allocated_amt == payment.installment_amort) {
    return cancelPaymentNotAllocated({ loanAccObj: loanAccount, paymentObj: payment, cancelDate, createdUser })
  }

  let listPaymentDetails = await paymentRepo.getListPaymentDetailByDebtAckContractNumber({
    debtAckContractNumber,
    fromDate: payment.value_date,
    createdDate: payment.created_date
  })

  const isAnnexAllocatedPayment = listPaymentDetails.find((item) => item.is_annex == 1)

  if (isAnnexAllocatedPayment) {
    listPaymentDetails = await cancelAllocatedAnnexAffected({
      loanAccObj: loanAccount,
      listPaymentDetails,
      paymentObj: payment,
      cancelDate
    })
  }

  let listValueDateToRepayment = await cancelPaymentAllocated({
    loanAccObj: loanAccount,
    listPaymentDetails,
    paymentObj: payment,
    cancelDate,
    createdUser
  })

  const paymentCancelValueDate = common.formatDate({
    date: payment.value_date,
    format: constant.DATE_FORMAT.YYYYMMDD_HHmmss
  })
  listValueDateToRepayment.push(paymentCancelValueDate)
  listValueDateToRepayment = lodash.uniq(listValueDateToRepayment)
  listValueDateToRepayment.sort(function (first, second) {
    return new Date(first) - new Date(second)
  })
  const valueDateLpi = listValueDateToRepayment[0]

  await deactiveLpiAndRiskGroup({ loanAccObj: loanAccount, valueDateLpi })

  await repaymentLpiAccordingToTimeline({ debtAckContractNumber, listValueDateToRepayment })

  return {
    code: 0,
    statusCode: 200,
    message: 'Cancel payment successfully. Payment allocated'
  }
}

async function cancelPaymentNotAllocated({ loanAccObj, paymentObj, cancelDate, createdUser }) {
  try {
    const paymentNonAllocatedAmt = Number(paymentObj.non_allocated_amt)

    if (Number(loanAccObj.non_allocation_amt) < paymentNonAllocatedAmt) {
      return {
        code: -2,
        statusCode: 400,
        message: 'Can not cancel. Non allocation amt not enough'
      }
    }
    await Promise.all([
      paymentRepo.updatePaymentCanceled({
        payId: paymentObj.id,
        cancelDate,
        status: constant.PAYMENT.STATUS.CANCELED,
        createdUser,
        nonAllocatedAmt: paymentObj.installment_amort
      }),
      loanAccountV2Repo.updateLoanAccountAmount({
        debtAckContractNumber: loanAccObj.debt_ack_contract_number,
        toCollect: paymentNonAllocatedAmt,
        nonAllocationAmt: -1 * paymentNonAllocatedAmt
      })
    ])
    return {
      code: 0,
      statusCode: 200,
      message: 'Cancel successfully. Payment not allocated'
    }
  } catch (error) {
    return {
      code: 1,
      statusCode: error.status,
      message: 'Cancel error. ERROR DETAIL: ' + error.message
    }
  }
}
async function cancelAllocatedAnnexAffected({ loanAccObj, listPaymentDetails, paymentObj, cancelDate }) {
  const listAnnexNotCanceled = await loanAnnexRepo.findAnnexNotCancelByDebtAckContractNumber(
    loanAccObj.debt_ack_contract_number
  )
  let isCancelAnnex = false
  const lishPaymentDetailNotCancel = listPaymentDetails.filter((item) => item.payment_id != paymentObj.id)
  let sumAmountToReAllocated = lodash.sumBy(lishPaymentDetailNotCancel, (item) => Number(item.amount)) || 0
  sumAmountToReAllocated += Number(loanAccObj.non_allocation_amt)
  let annexToUnactive
  const listAnnexToCancel = {}
  for (const paymentDetail of listPaymentDetails) {
    const paymentDetailAmount = Number(paymentDetail.amount)
    sumAmountToReAllocated -= paymentDetailAmount
    if (sumAmountToReAllocated < 0 && paymentDetail.is_annex == constant.BILL_ON_DUE.IS_ANNEX.TRUE) {
      const annex = listAnnexNotCanceled.find((item) => item.annex_number == paymentDetail.annex_number && item.annex_status != ANNEX.STATUS.CALCULATE)
      if (annex) {
        if (common.formatDate({ date: cancelDate }) > common.formatDate({ date: annex?.termination_date })) {
          isCancelAnnex = true
          !listAnnexToCancel[paymentDetail.annex_number] && (listAnnexToCancel[paymentDetail.annex_number] = annex)
        } else {
          !annexToUnactive && (annexToUnactive = annex)
        }
      }
    }
  }
  let listInstallment
  if (isCancelAnnex || annexToUnactive) {
    listInstallment = await installmentRepo.findInstallmentByDebtAck(loanAccObj.debt_ack_contract_number)
  }
  if (annexToUnactive) {
    console.log('Deactive annex', annexToUnactive.annex_number)
    await handleCancelActiveAnnex(annexToUnactive, listInstallment, true)
  }
  const newListPaymentDetails = listPaymentDetails
  if (isCancelAnnex) {
    const dataAnnex = Object.entries(listAnnexToCancel)
    dataAnnex.sort(function (a, b) {
      return b[1].termination_date - a[1].termination_date
    })
    for (const [annexNumber, annexObj] of dataAnnex) {
      console.log('Cancel annex', annexNumber)
      const listPaymentDetailAllocatedAnnex = await handleCancelActiveAnnex(annexObj, listInstallment)
      for (const paymentDetailAnnex of listPaymentDetailAllocatedAnnex) {
        if (paymentDetailAnnex.payment_id != paymentObj.id) {
          const checkExisted = newListPaymentDetails.find((item) => item.id == paymentDetailAnnex.id)
          !checkExisted && newListPaymentDetails.push(paymentDetailAnnex)
        }
      }
    }
    // on due lai lich tra no
    await billService.doCreateBillOnDue({
      debtAckContractNumber: loanAccObj.debt_ack_contract_number,
      onDueDate: cancelDate
    })
  }
  return newListPaymentDetails
}
async function cancelPaymentAllocated({ loanAccObj, listPaymentDetails, paymentObj, cancelDate, createdUser }) {
  const paymentReAllocated = {}
  const listPaymentDetailIdUpdate = []
  const plUpdateLoanAccAmt = {
    prinPaid: 0,
    intPaid: 0,
    feePaid: 0,
    lpiPaid: 0,
    toCollect: 0,
    nonAllocationAmt: 0,
    debtAckContractNumber: loanAccObj.debt_ack_contract_number
  }
  console.log('listPaymentDetails', JSON.stringify(listPaymentDetails))
  for (const paymentDetail of listPaymentDetails) {
    const paymentDetailAmount = Number(paymentDetail.amount)
    listPaymentDetailIdUpdate.push(paymentDetail.id)
    if (paymentDetail.payment_id != paymentObj.id) {
      !paymentReAllocated[paymentDetail.payment_id] && (paymentReAllocated[paymentDetail.payment_id] = 0)
      paymentReAllocated[paymentDetail.payment_id] != null &&
        (paymentReAllocated[paymentDetail.payment_id] += paymentDetailAmount)
    }
    if (paymentDetail.type == constant.INSTALLMENT.TYPE.PRIN) {
      plUpdateLoanAccAmt.prinPaid += paymentDetailAmount
    } else if (paymentDetail.type == constant.INSTALLMENT.TYPE.INT) {
      plUpdateLoanAccAmt.intPaid += paymentDetailAmount
    } else if (paymentDetail.type == constant.INSTALLMENT.TYPE.FEE) {
      plUpdateLoanAccAmt.feePaid += paymentDetailAmount
    } else if (
      paymentDetail.type == constant.INSTALLMENT.TYPE.LPI_INT ||
      paymentDetail.type == constant.INSTALLMENT.TYPE.LPI_PRIN
    ) {
      plUpdateLoanAccAmt.lpiPaid += paymentDetailAmount
    }
    await Promise.all([
      billOnDueRepo.updateBillRemainAmountAndPaymentStatus(
        paymentDetail.bill_id,
        paymentDetailAmount,
        constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE
      ),
      installmentRepo.updateInstallmentRemainAmount(
        paymentDetail.installment_id,
        paymentDetailAmount,
        constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE
      )
    ])
  }
  // update non allocation amount
  console.log('paymentReAllocated', JSON.stringify(paymentReAllocated))
  let sumNonAllocatedAmount = 0
  const listValueDateToRepayment = []
  listValueDateToRepayment.push(common.formatDate({ date: cancelDate, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss }))

  for (const paymentId of Object.keys(paymentReAllocated)) {
    if (paymentReAllocated[paymentId]) {
      sumNonAllocatedAmount += paymentReAllocated[paymentId]
      await paymentRepo.updatePaymentNonAllocationAmount({
        payId: paymentId,
        nonAllocatedAmt: paymentReAllocated[paymentId]
      })
      // listValueDateToRepayment.push(common.formatDate({ date: paymentUpdated?.[0].value_date, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss }))
    }
  }
  // tinh lai to collect / load bill
  const [listBillOnDue, listPaymentMoreDate] = await Promise.all([
    billOnDueRepo.getBillOnDuePaymentNotCompleteV3(loanAccObj.debt_ack_contract_number),
    paymentRepo.getListPaymentMoreDate(
      loanAccObj.debt_ack_contract_number,
      common.formatDate({ date: paymentObj.value_date, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss })
    )
  ])
  let listBillRemain = 0
  for (const bill of listBillOnDue) {
    listBillRemain += Number(bill.remain_amount)
    listValueDateToRepayment.push(
      common.formatDate({ date: bill.due_date, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss })
    )
  }

  for (const payment of listPaymentMoreDate) {
    listValueDateToRepayment.push(
      common.formatDate({ date: payment.value_date, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss })
    )
  }
  console.log('listBillRemain', listBillRemain)
  console.log('sumNonAllocatedAmount', sumNonAllocatedAmount)
  if (Number(paymentObj.non_allocated_amt) > 0) {
    sumNonAllocatedAmount -= Number(paymentObj.non_allocated_amt)
  }
  plUpdateLoanAccAmt.toCollectUpdated = listBillRemain - (sumNonAllocatedAmount + Number(loanAccObj.non_allocation_amt))
  plUpdateLoanAccAmt.nonAllocationAmt = sumNonAllocatedAmount

  console.log('plUpdateLoanAccAmt', JSON.stringify(plUpdateLoanAccAmt))

  await Promise.all([
    paymentRepo.updatePaymentCanceled({
      payId: paymentObj.id,
      cancelDate,
      status: constant.PAYMENT.STATUS.CANCELED,
      createdUser,
      nonAllocatedAmt: paymentObj.installment_amort
    }),
    loanAccountV2Repo.updateLoanAccountAmount(plUpdateLoanAccAmt),
    paymentDetailRepo.updateFlagActiveByListId(constant.FLAG_NOT_ACTIVE, listPaymentDetailIdUpdate, cancelDate)
  ])
  return listValueDateToRepayment
}
async function handleCancelActiveAnnex(annexObj, listInstallment, isDeactiveAnnex = false) {
  const listInsmUpdatedByAnnex = listInstallment.filter((item) => item.annex_number == annexObj.annex_number)
  const listInsmUpdatedActive = []
  const listInsmUpdatedCancel = []
  for (const insm of listInsmUpdatedByAnnex) {
    if (insm.status == constant.INSTALLMENT.STATUS.ACTIVE) {
      listInsmUpdatedCancel.push(insm.id)
    }
    if (insm.status == constant.INSTALLMENT.STATUS.ARCHIVE) {
      listInsmUpdatedActive.push(insm.id)
    }
  }
  const plUpdateLoanAnnex = {
    annexStatusNew: constant.ANNEX.STATUS.CANCEL,
    annexNumber: annexObj.annex_number,
    annexStatusOld: constant.ANNEX.STATUS.DONE
  }
  let insmStatus = constant.INSTALLMENT.STATUS.ARCHIVE
  let billStatus = constant.BILL_ON_DUE.STATUS.CANCEL

  if (isDeactiveAnnex) {
    plUpdateLoanAnnex.annexStatusNew = constant.ANNEX.STATUS.INIT
    insmStatus = constant.INSTALLMENT.STATUS.DEACTIVE
    billStatus = constant.BILL_ON_DUE.STATUS.NOT_ACTIVE
  }
  const listProcess = await Promise.all([
    billOnDueRepo.updateBillStatusByAnnexNumber(annexObj.annex_number, billStatus),
    installmentRepo.updateStatusByListId(constant.INSTALLMENT.STATUS.ACTIVE, listInsmUpdatedActive),
    installmentRepo.updateStatusByListId(insmStatus, listInsmUpdatedCancel),
    loanAnnexRepo.updateStatusLoanAnnex(global.poolWrite, plUpdateLoanAnnex)
  ])
  if (!isDeactiveAnnex) {
    const listBillOnDueAnnex = listProcess[0]
    const listBillOnDueId = listBillOnDueAnnex.map((item) => Number(item.id))
    const [listPaymentDetailAllocatedAnnex, amortObj] = await Promise.all([
      paymentDetailRepo.getPaymentDetailByBillId(listBillOnDueId),
      loanAmortRepo.updateAmortFlagActiveByAnnexNumber(annexObj.annex_number, constant.FLAG_NOT_ACTIVE)
    ])
    await loanAmortRepo.updateAmortFlagActiveByAmortId(amortObj.prev_amort_id, constant.FLAG_ACTIVE)
    console.log('listPaymentDetailAllocatedAnnex', listPaymentDetailAllocatedAnnex)
    return listPaymentDetailAllocatedAnnex
  }
  return []
}

async function deactiveLpiAndRiskGroup({ loanAccObj, valueDateLpi }) {
  await Promise.all([
    contractRiskGrpService.deactiveRiskGroup({ loanAccObj, riskDate: common.formatDate({ date: valueDateLpi }) }),
    irService.deactiveLpi({
      debtAckContractNumber: loanAccObj.debt_ack_contract_number,
      irDate: common.formatDate({ date: valueDateLpi })
    })
  ])
}
async function repaymentLpiAccordingToTimeline({ debtAckContractNumber, listValueDateToRepayment }) {
  for (let i = 0; i < listValueDateToRepayment.length; i++) {
    const valueDate = listValueDateToRepayment[i]
    const nextValueDate = listValueDateToRepayment?.[i + 1] || valueDate

    const res = await repaymentService.doRepayment({
      debtAckContractNumber,
      paymentDate: common.formatDate({ date: valueDate }),
      valueDate
    })
    console.log('END REPAYMENT: ', JSON.stringify(res))

    await irService.runIrToDate({ fromDate: valueDate, toDate: nextValueDate, debtAckContractNumber })
  }
}
const getRepaymentHistoryV2 = async function (payload) {
  try {
    if (!payload || !payload.custId) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Missing input custId'
      }
    }
    const rsData = await paymentRepo.getAllPaymentByCustId(payload)
    if (rsData.rowCount == 0) {
      return {
        statusCode: 200,
        code: 1,
        message: '[LMS-MC] Khong tim thay du lieu'
      }
    }
    const result = []
    const listPayment = rsData.rows
    for (const payment of listPayment) {
      let status = 'NORMAL'
      if (
        payment.pay_type == constant.PAYMENT.PAY_TYPE.OUT ||
        payment.tran_status == constant.PAYMENT.TRANS_STATUS.TRANSFER
      ) {
        status = payment.comments
      }
      if (payment.status == constant.PAYMENT.STATUS.CANCELED) {
        status = 'CANCEL PAYMENT'
      }
      result.push({
        id: payment.id,
        type: 'Repayment',
        reference: payment.reference,
        amount: Number(payment.installment_amort),
        dateOfRepayment: payment.payment_date,
        paymentMethod: payment.payment_method,
        nonAllocatedAmt: Number(payment.non_allocated_amt),
        status,
        paymentStatus: payment.status,
        payerName: payment.customer_name || '',
        partnerCode: payment.partner_code || '',
        debtAckContractNumber: payment.debt_ack_contract_number || ''
      })
    }
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien lay thong tin thanh cong',
      data: result
    }
  } catch (error) {
    common.log('Error at getRepaymentHistoryV2: ' + error.message)
    console.error(error)
    return {
      statusCode: error.statusCode || 500,
      code: 99,
      message: error.message
    }
  }
}
const getDataDebtReminderCollection = async function (req, res) {
  try {
    const { dpd } = req.query
    common.log('request body getDataToDebtReminderCollection: ' + JSON.stringify(dpd))

    if (!dpd) {
      return res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Truyen thieu truong thong tin du lieu'
        })
      )
    }

    const listDebtContracts = await installmentRepo.findDebtContractsByDpd(dpd)
    const listDebtNumbers = []
    if (Array.isArray(listDebtContracts) && listDebtContracts.length > 0) {
      listDebtContracts.forEach((item) => {
        listDebtNumbers.push(item?.debt_ack_contract_number)
      })
    }

    return res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Lay thong tin thanh cong',
        data: listDebtNumbers
      })
    )
  } catch (err) {
    console.log(err)
    return res.status(500).json(
      (res.body = {
        code: 99,
        message: err.message
      })
    )
  }
}
const getPaymentInfoDebtSms = async function (req, res) {
  try {
    const pl = req.body
    common.log('request body getPaymentInfoDebtSms: ' + JSON.stringify(pl))

    if (!pl || !pl.dpd || !Array.isArray(pl.listContracts)) {
      return res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Du lieu dau vao khong hop le'
        })
      )
    }

    const contractList = []
    if (pl.dpd < 0) {
      for (const contractNumber of pl.listContracts) {
        const plQuery = {
          debtAckContractNumber: contractNumber
        }

        const listFunc = await Promise.all([
          debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, plQuery),
          installmentRepo.getNextDueDateIns(global.poolRead, plQuery),
          installmentRepo.getCurrentIrNumCycle(global.poolRead, plQuery),
          installmentRepo.getSumAmount(global.poolRead, plQuery, '2,5'),
          installmentRepo.getSumAmount(global.poolRead, plQuery, '1,2,5'),
          installmentRepo.getMaxIrNumCycle(global.poolRead, plQuery)
        ])

        const rsLoanAccount = listFunc[0]
        const loanAccObj = rsLoanAccount.rowCount > 0 ? rsLoanAccount.rows[0] : {}
        const custObj = await crmService.getCustomerInfo(
          global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj?.cust_id,
          {}
        )

        if (custObj?.phoneNumber1) {
          const rsNextDueDate = listFunc[1]
          const nextDueDate = rsNextDueDate.rows[0]
            ? moment(new Date(rsNextDueDate.rows[0].end_date)).format('YYYY-MM-DD hh:mm:ss')
            : null
          const currentIrNumCycle = listFunc[2].rows[0] ? Number(listFunc[2].rows[0].ir_num_cycle) : 0
          const sumAmountType25 = Number(listFunc[3].rows[0]?.sum || 0)
          const sumAmountType125 = Number(listFunc[4].rows[0]?.sum || 0)
          const maxIrNumCycle = Number(listFunc[5].rows[0]?.ir_num_cycle || 0)
          const nextDuePayment =
            currentIrNumCycle < maxIrNumCycle && loanAccObj.contract_type == constant.CONTRACT_TYPE.CREDITLINE
              ? sumAmountType25
              : sumAmountType125

          const data = {
            contract_number: loanAccObj.debt_ack_contract_number,
            product_code: loanAccObj?.product_code,
            partner_code: loanAccObj?.partner_code,
            phone_number: custObj?.phoneNumber1,
            end_instal_date: nextDueDate,
            totalemi: nextDuePayment + Number(loanAccObj.to_collect)
          }
          contractList.push(data)
        }
      }
    } else {
      for (const contractNumber of pl.listContracts) {
        const plQuery = {
          debtAckContractNumber: contractNumber
        }

        const rsLoanAccount = await debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, plQuery)
        const loanAccObj = rsLoanAccount.rowCount > 0 ? rsLoanAccount.rows[0] : {}
        const custObj = await crmService.getCustomerInfo(
          global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj?.cust_id,
          {}
        )

        if (custObj?.phoneNumber1) {
          const data = {
            contract_number: loanAccObj?.debt_ack_contract_number,
            product_code: loanAccObj?.product_code,
            partner_code: loanAccObj?.partner_code,
            phone_number: custObj?.phoneNumber1,
            totalemi: Number(loanAccObj.to_collect)
          }
          contractList.push(data)
        }
      }
    }

    return res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Lay thong tin thanh cong',
        data: contractList
      })
    )
  } catch (err) {
    console.log(err)
    return res.status(500).json(
      (res.body = {
        code: 99,
        message: err.message
      })
    )
  }
}

const getPaymentInfoDebtEmail = async function (req, res) {
  try {
    const pl = req.body
    common.log('request body getPaymentInfoDebtEmail: ' + JSON.stringify(pl))

    if (!pl || !pl.dpd || !Array.isArray(pl.listContracts)) {
      return res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Truyen thieu truong thong tin du lieu'
        })
      )
    }

    const contractList = []
    if (pl.dpd < 0) {
      for (const contractNumber of pl.listContracts) {
        const plQuery = {
          debtAckContractNumber: contractNumber
        }

        const listFunc = await Promise.all([
          debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, plQuery),
          installmentRepo.getNextDueDateIns(global.poolRead, plQuery),
          installmentRepo.getCurrentIrNumCycle(global.poolRead, plQuery),
          installmentRepo.getSumAmount(global.poolRead, plQuery, '2,5'),
          installmentRepo.getSumAmount(global.poolRead, plQuery, '1,2,5'),
          installmentRepo.getMaxIrNumCycle(global.poolRead, plQuery)
        ])

        const rsLoanAccount = listFunc[0]
        const loanAccObj = rsLoanAccount.rowCount > 0 ? rsLoanAccount.rows[0] : {}
        const custObj = await crmService.getCustomerInfo(
          global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj?.cust_id,
          {}
        )

        if (custObj?.email) {
          const rsNextDueDate = listFunc[1]
          const nextDueDate = rsNextDueDate.rows[0]
            ? moment(new Date(rsNextDueDate.rows[0].end_date)).format('YYYY-MM-DD hh:mm:ss')
            : null
          const currentIrNumCycle = listFunc[2].rows[0] ? Number(listFunc[2].rows[0].ir_num_cycle) : 0
          const sumAmountType25 = Number(listFunc[3].rows[0]?.sum || 0)
          const sumAmountType125 = Number(listFunc[4].rows[0]?.sum || 0)
          const maxIrNumCycle = Number(listFunc[5].rows[0]?.ir_num_cycle || 0)
          const nextDuePayment =
            currentIrNumCycle < maxIrNumCycle && loanAccObj.contract_type == constant.CONTRACT_TYPE.CREDITLINE
              ? sumAmountType25
              : sumAmountType125

          const data = {
            contract_number: loanAccObj.debt_ack_contract_number,
            email: custObj?.email,
            end_instal_date: nextDueDate,
            totalemi: nextDuePayment + Number(loanAccObj.to_collect)
          }
          contractList.push(data)
        }
      }
    } else {
      for (const contractNumber of pl.listContracts) {
        const plQuery = {
          debtAckContractNumber: contractNumber
        }

        const listFunc = await Promise.all([
          billOnDueRepo.getBillOnDuePaymentNotCompleteV2(global.poolRead, plQuery),
          debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, plQuery)
        ])

        const rsBill = listFunc[0]
        const rsLoanAccount = listFunc[1]
        const loanAccObj = rsLoanAccount.rowCount > 0 ? rsLoanAccount.rows[0] : {}
        const custObj = await crmService.getCustomerInfo(
          global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj.cust_id,
          {}
        )

        if (custObj?.email && custObj?.fullName) {
          let printOverDue = 0
          let intOverDue = 0
          if (rsBill.rows.length > 0) {
            for (const i in rsBill.rows) {
              const billObj = rsBill.rows[i]
              if (billObj.type === constant.BILL_ON_DUE.TYPE.PRIN) {
                printOverDue += Number(billObj?.remain_amount || 0)
              }
              if (billObj.type === constant.BILL_ON_DUE.TYPE.INT) {
                intOverDue += Number(billObj?.remain_amount || 0)
              }
            }
          }

          const data = {
            contract_number: loanAccObj.debt_ack_contract_number,
            full_name: custObj?.fullName,
            email: custObj?.email,
            to_collect: Number(loanAccObj.to_collect || 0),
            prin_ovr_amt: Number(printOverDue),
            int_ovr_amt: Number(intOverDue),
            fee_ovr_amt: Number(loanAccObj.to_collect || 0) - Number(printOverDue) - Number(intOverDue)
          }
          contractList.push(data)
        }
      }
    }

    return res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Lay thong tin thanh cong',
        data: contractList
      })
    )
  } catch (err) {
    console.log(err)
    return res.status(500).json(
      (res.body = {
        code: 99,
        message: err.message
      })
    )
  }
}

module.exports = {
  usagePayment,
  getRepaymentHistory,
  getPaymentHistory,
  getPaymentHistoryV2,
  getRepaymentHistoryDetail,
  checkRepaymentByCustId,
  checkRepayment,
  checkRepaymentV2,
  usagePaymentBatchJob,
  usageSuspendPaymentBatchJob,
  usageSuspendPayment,
  transferCaseToCase,
  cancelPayment,
  checkRepaymentV3,
  getRepaymentHistoryV2,
  getDataDebtReminderCollection,
  getPaymentInfoDebtSms,
  getPaymentInfoDebtEmail
}
