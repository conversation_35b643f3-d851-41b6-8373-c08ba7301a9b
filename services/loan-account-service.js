const common = require('../utils/common')
const constant = require('../utils/constant')
const camelcaseKeys = require('camelcase-keys')
const moment = require('moment')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const contractRiskGrpRepo = require('../repositories/contract-risk-grp-repo')
const loanAccountRepo = require('../repositories/debt-ack-contract-repo')
const paymentRepo = require('../repositories/payment-repo')
const paymentDetailRepo = require('../repositories/payment-detail-repo')
const tranLogRepo = require('../repositories/tran-log-repo')
const installmentRepo = require('../repositories/installment-repo')
const welcomePackageRepo = require('../repositories/welcome-package-repo')
const irChargeRepo = require('../repositories/ir-charge-repo')
const axios = require('axios')
const merchantLimitRepo = require('../repositories/merchant-limit-repo')
const crmService = require('../other-services/crm-service')
const losService = require('../other-services/los-service')
const backendMobileService = require('../other-services/backend-mobile-service')
const bssEsignService = require('../other-services/bss-esigning-service')
const loanAccountV2Repo = require('../repositories/loan-account-repo')
const disbursementRepo = require('../repositories/disbursement-repo')
const requestLogRepo = require('../repositories/request-log-repo')
const installmentDeductionRepo = require('./../repositories/promotion-installment-deduction-repo')
const loanAmortRepo = require('../repositories/loan-amort-repo')
const loanStatusHstService = require('../services/loan-status-hst-service')
const { LoanAccountRepository } = require('./../repositories-v2')
const path = require('path')
const _ = require('lodash')
const { In, DifferentFrom } = require('../utils/find-operator')
const insuranceRepo = require('./../repositories/insurance-repo')
const installmentService = require('./installment-service')
const { isPrinEveryCycle } = require('../utils/helper')

const getCollectionInfo = async function (req, res) {
  try {
    const pl = req.query
    if (!pl.debtAckContractNumber) {
      res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Thieu du lieu dau vao'
        })
      )
      return
    }
    pl.isAnnex = 0

    const listFuncs = await Promise.all([
      loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, pl),
      billOnDueRepo.getBillOnDuePaymentNotComplete(global.poolRead, pl),
      paymentRepo.getAllByDebtAckContractNumber(global.poolRead, pl),
      contractRiskGrpRepo.findContractRiskGrp(global.poolRead, pl),
      installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompleted(global.poolRead, pl)
    ])
    const rsLoanAccount = listFuncs[0]
    const rsBill = listFuncs[1]
    const rsPayment = listFuncs[2]
    const rsRiskGroup = listFuncs[3]
    const rsInsm = listFuncs[4]

    const keyFinanceIndicators = {}
    const dueIndicators = {}
    let nonAllocateAmt = 0

    if (rsLoanAccount.rowCount > 0) {
      keyFinanceIndicators.proncipalOustan = rsLoanAccount.rows[0].rls_amt - rsLoanAccount.rows[0].prin_paid
      keyFinanceIndicators.principalOutstanding = rsLoanAccount.rows[0].rls_amt - rsLoanAccount.rows[0].prin_paid
      dueIndicators.toCollect = common.roundUp(
        rsLoanAccount.rows[0].to_collect,
        global.calcuCfg.scale,
        rsLoanAccount.rows[0].partner_code
      )
      nonAllocateAmt = rsLoanAccount.rows[0].non_allocation_amt ? Number(rsLoanAccount.rows[0].non_allocation_amt) : 0
    }
    keyFinanceIndicators.dueCapital = 0
    keyFinanceIndicators.dueInterests = 0
    keyFinanceIndicators.periodFees = 0
    keyFinanceIndicators.lpi = 0
    if (rsBill.rowCount > 0) {
      for (const i in rsBill.rows) {
        const billObj = rsBill.rows[i]
        if (billObj.type == 1) keyFinanceIndicators.dueCapital += Number(billObj.remain_amount)
        if (billObj.type == 2) keyFinanceIndicators.dueInterests += Number(billObj.remain_amount)
        if (billObj.type == 5) keyFinanceIndicators.periodFees += Number(billObj.remain_amount)
        if (billObj.type == 3 || billObj.type == 4) keyFinanceIndicators.lpi += Number(billObj.remain_amount)
      }
    }
    if (rsRiskGroup.rowCount > 0) {
      keyFinanceIndicators.dpdVas = rsRiskGroup.rows[0].dpd_num_day > 0 ? rsRiskGroup.rows[0].dpd_num_day : 0
      keyFinanceIndicators.dpdStrategy = rsRiskGroup.rows[0].dpd_strategy > 0 ? rsRiskGroup.rows[0].dpd_strategy : 0
      keyFinanceIndicators.dpdStratery = rsRiskGroup.rows[0].dpd_strategy > 0 ? rsRiskGroup.rows[0].dpd_strategy : 0
    }
    dueIndicators.paymentTotal = 0
    if (rsPayment.rowCount > 0) {
      for (const i in rsPayment.rows) {
        dueIndicators.paymentTotal += Number(rsPayment.rows[i].installment_amort)
        if (
          rsPayment.rows[i].installment_amort > 0 &&
          dueIndicators.lastPayment == undefined &&
          dueIndicators.on == undefined
        ) {
          dueIndicators.lastPayment = rsPayment.rows[i].installment_amort
          dueIndicators.on = moment(rsPayment.rows[i].value_date).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss)
        }
      }
    }
    keyFinanceIndicators.dueInterest = keyFinanceIndicators.dueInterests
    dueIndicators.due =
      keyFinanceIndicators.dueCapital +
      keyFinanceIndicators.dueInterests +
      keyFinanceIndicators.periodFees +
      keyFinanceIndicators.lpi
    dueIndicators.notDue = 0
    dueIndicators.disputedAmount = 0
    if (rsInsm.rowCount > 0) {
      const insmobj = rsInsm.rows[0]
      for (const i in rsInsm.rows) {
        if (
          moment(insmobj.end_date).format(constant.DATE_FORMAT.YYYYMMDD2) ==
          moment(rsInsm.rows[i].end_date).format(constant.DATE_FORMAT.YYYYMMDD2)
        ) {
          dueIndicators.notDue += Number(rsInsm.rows[i].remain_amount)
        }
      }
    }
    // them predue = due - nonAllocate
    dueIndicators.preDue = dueIndicators.due - nonAllocateAmt

    res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Thuc hien lay thong tin collection thanh cong',
        data: {
          keyFinanceIndicators,
          dueIndicators
        }
      })
    )
  } catch (err) {
    console.log(err)
    res.status(500).json((res.body = { code: 99, message: err.message }))
  }
}
const getWelcomePackage = async function (req, res) {
  try {
    const input = req.query
    if (!input || !input.contractNumber || !input.authCode) {
      return res.status(400).json((res.body = { code: 1, message: 'input invalid' }))
    }

    const wclList = await welcomePackageRepo.findByContractNumber(global.poolRead, input.contractNumber, input.authCode)
    if (wclList.rowCount == 0) {
      return res.status(200).json((res.body = { code: 1, message: 'welcome package not exists | auth code invalid' }))
    }

    const unsignedFileUrl = wclList.rows[0].url

    const image = await axios.get(unsignedFileUrl, { responseType: 'arraybuffer' })
    // let returnedB64 = Buffer.from(image.data).toString('base64');
    return res.status(200).send(Buffer.from(image.data).toString('base64'))
  } catch (err) {
    console.log(err)
    res.status(500).json((res.body = { code: 99, message: err.message }))
  }
}
const getLoanContract = async function (req, res) {
  try {
    const pl = req.query
    if (!pl.custId) return res.status(400).json((res.body = { code: 2, message: 'input invalid' }))
    const func = await Promise.all([
      loanAccountRepo.findByContractNumberAndStatus(global.poolRead, pl),
      merchantLimitRepo.findActoveByContractNumberAndCustId(global.poolRead, pl),
      crmService.getCustomerInfo(global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + pl.custId, {})
    ])
    const loanAccList = func[0].rows || []
    const merchantLimitList = func[1].rows || []
    const customerObj = func[2]
    const resultData = []

    for (const obj of merchantLimitList) {
      const objMcLimit = {
        customerName: customerObj.fullName,
        contractNumber: obj.contract_number,
        status: constant.CONTRACT_LIMIT_STATUS[obj.status],
        productName: obj.product_code,
        aprLimitAmt: Number(obj.apr_limit_amt),
        remainLimitAmt: Number(obj.remain_limit_amt),
        tenor: obj.tenor,
        rate: (28 / 12).toFixed(2),
        activated_date: common.convertDatetoString(obj.active_date, constant.DATE_FORMAT.YYYYMMDD) || '',
        debtAckList: []
      }
      for (const loanAcc of loanAccList) {
        if (obj.contract_number === loanAcc.contract_number) {
          const funcDebt = await Promise.all([
            installmentRepo.getInsMonthy(global.poolRead, {
              debtAckContractNumber: loanAcc.debt_ack_contract_number
            }),
            installmentRepo.getInsIsNotClosed(global.poolRead, {
              debtAckContractNumber: loanAcc.debt_ack_contract_number
            }),
            billOnDueRepo.getSumRemainAmount(
              global.poolRead,
              { debtAckContractNumber: loanAcc.debt_ack_contract_number },
              1
            ),
            billOnDueRepo.getSumRemainAmount(
              global.poolRead,
              { debtAckContractNumber: loanAcc.debt_ack_contract_number },
              2
            ),
            billOnDueRepo.getSumRemainAmount(
              global.poolRead,
              { debtAckContractNumber: loanAcc.debt_ack_contract_number },
              5
            ),
            paymentRepo.getSumInstallmentAmort(global.poolRead, {
              debtAckContractNumber: loanAcc.debt_ack_contract_number
            }),
            installmentRepo.getMaxIrNumCycle(global.poolRead, {
              debtAckContractNumber: loanAcc.debt_ack_contract_number
            }),
            installmentRepo.getSumAmount(
              global.poolRead,
              { debtAckContractNumber: loanAcc.debt_ack_contract_number },
              '2,5'
            ),
            installmentRepo.getSumAmount(
              global.poolRead,
              { debtAckContractNumber: loanAcc.debt_ack_contract_number },
              '1,2,5'
            ),
            installmentRepo.getCurrentIrNumCycle(global.poolRead, {
              debtAckContractNumber: loanAcc.debt_ack_contract_number
            }),
            contractRiskGrpRepo.findContractRiskGrp(global.poolRead, {
              debtAckContractNumber: loanAcc.debt_ack_contract_number
            }),
            installmentRepo.getNextDueDateIns(global.poolRead, {
              debtAckContractNumber: loanAcc.debt_ack_contract_number
            }),
            billOnDueRepo.getLpi(global.poolRead, { debtAckContractNumber: loanAcc.debt_ack_contract_number })
          ])

          const monthyObj = funcDebt[0].rows[0] || {}
          const insNotCloseObj = funcDebt[1].rows[0] || {}
          const priOverAmt = funcDebt[2].rows[0].sum_remain_amount
          const intOverAmt = funcDebt[3].rows[0].sum_remain_amount
          const feeAmt = funcDebt[4].rows[0].sum_remain_amount
          const paidTotal =
            funcDebt[5].rows[0].sumInstallmentAmort == null ? 0 : funcDebt[5].rows[0].sumInstallmentAmort
          const maxIrNumCycle = Number(funcDebt[6].rows[0]?.ir_num_cycle)
          const sumAmountType25 = Number(funcDebt[7].rows[0]?.sum)
          const sumAmountType125 = Number(funcDebt[8].rows[0]?.sum)
          const currentIrNumCycle = funcDebt[9].rows[0] ? Number(funcDebt[9].rows[0].ir_num_cycle) : 0

          const dpd = Number(funcDebt[10].rows[0]?.dpd_num_day || 0)
          const lpi = funcDebt[12].rows[0]?.lpi
          let dpdCollection = loanAcc.dpd
          const listInstallment =
            await installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
              debtAckContractNumber: loanAcc.debt_ack_contract_number,
              closed: 0,
            })
          if (listInstallment.length) {
            const minEndDate = _.minBy(
              listInstallment.filter((item) => item.type == 2),
              'end_date'
            ).end_date

            if (dpdCollection == 0) {
              const today = moment().format(constant.DATE_FORMAT.YYYYMMDD2)
              dpdCollection = common.getDifferencesDays(today, minEndDate, false)
            }
          }

          const nextDuePayment =
            currentIrNumCycle < maxIrNumCycle && loanAcc.contract_type == constant.CONTRACT_TYPE.CREDITLINE
              ? sumAmountType25
              : sumAmountType125
          let status = constant.DEBT_ACK_STATUS[loanAcc.status] || ''
          if (
            loanAcc.status == constant.DEBT_ACK_STATUS.TERMINATED ||
            loanAcc.payment_status == constant.PAYMENT_STATUS.DONE
          ) {
            status = 'TERMINATED'
          } else if (loanAcc.status == constant.DEBT_ACK_STATUS.ACTIVE) {
            status = 'ACTIVATED'
          }else if (loanAcc.status == constant.DEBT_ACK_STATUS.CANCEL) {
            status = 'CANCEL'
          }
          objMcLimit.debtAckList.push({
            debtAckContractNumber: loanAcc.debt_ack_contract_number,
            status: status || '',
            statusCommon: constant.DEBT_ACK_STATUS.codeToStatusCommon[loanAcc.status],
            aprLimitAmt: Number(loanAcc.apr_limit_amt),
            startDate: common.convertDatetoString(loanAcc.start_date, constant.DATE_FORMAT.DDMMYYYY2) || '',
            endDate: common.convertDatetoString(loanAcc.end_date, constant.DATE_FORMAT.DDMMYYYY2) || '',
            monthyAmt: Number(monthyObj.amt),
            remainPriAmt: Number(loanAcc.rls_amt) - Number(loanAcc.prin_paid),
            remainTenor: insNotCloseObj.ir_num_cycle
              ? Number(loanAcc.tenor) - Number(insNotCloseObj.ir_num_cycle) + 1
              : 0,
            tenor: Number(loanAcc.tenor),
            debtAmt: Number(loanAcc.to_collect),
            dpd,
            dpdCollection: dpdCollection || 0,
            priOutstandingAmt: Number(loanAcc.prin_amt),
            priOverAmt: Number(priOverAmt),
            intOverAmt: Number(intOverAmt),
            lpi: Number(lpi),
            feeAmt: Number(feeAmt),
            nextDuePayment,
            nextDuePaymentTotal: Number(loanAcc.to_collect) + nextDuePayment,
            paidTotal: Number(paidTotal)
          })
        }
      }
      resultData.push(objMcLimit)
    }
    res.status(200).json(
      (res.body = {
        code: 0,
        message: 'Success',
        data: resultData
      })
    )
  } catch (e) {
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
}
const getLoanDebtAckContract = async function (req, res) {
  try {
    const pl = req.query
    if (!pl.debtAckContractNumber) return res.status(400).json((res.body = { code: 2, message: 'input invalid' }))

    const funcDebt = await Promise.all([
      installmentRepo.getInsMonthy(global.poolRead, { debtAckContractNumber: pl.debtAckContractNumber }),
      installmentRepo.getInsIsNotClosed(global.poolRead, { debtAckContractNumber: pl.debtAckContractNumber }),
      loanAccountRepo.findByContractNumberAndStatus(global.poolRead, {
        debtAckContractNumber: pl.debtAckContractNumber
      })
    ])
    const monthyObj = funcDebt[0].rows[0] || {}
    const insNotCloseObj = funcDebt[1].rows[0] || {}
    const loanAcc = funcDebt[2].rows[0] || {}
    const resultData = {
      debtAckContractNumber: loanAcc.debt_ack_contract_number,
      status: constant.DEBT_ACK_STATUS[loanAcc.status] || '',
      aprLimitAmt: Number(loanAcc.apr_limit_amt),
      startDate: common.convertDatetoString(loanAcc.start_date, constant.DATE_FORMAT.DDMMYYYY2) || '',
      endDate: common.convertDatetoString(loanAcc.end_date, constant.DATE_FORMAT.DDMMYYYY2) || '',
      monthyAmt: Number(monthyObj.amt),
      remainPriAmt: Number(loanAcc.rls_amt) - Number(loanAcc.prin_paid),
      remainTenor: insNotCloseObj.ir_num_cycle ? Number(loanAcc.tenor) - Number(insNotCloseObj.ir_num_cycle) + 1 : 0,
      tenor: Number(loanAcc.tenor)
    }
    res.status(200).json(
      (res.body = {
        code: 0,
        message: 'Success',
        data: resultData
      })
    )
  } catch (e) {
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
}
const getCollectionTotalList = async function (req, res) {
  try {
    const pl = req.query
    if (!pl.size) pl.size = 20
    if (!pl.page) pl.page = 1
    common.log('req query getCollectionTotalList: ' + JSON.stringify(pl))
    const func = await Promise.all([
      loanAccountRepo.findTotalCollectionList(global.poolRead, { type: pl.type }),
      loanAccountRepo.findTotalCollectionList(global.poolRead, pl),
      loanAccountRepo.findCollectionList(global.poolRead, pl)
    ])
    // console.log(Number(undefined))
    // let totalCollection = func[0][0] || {}
    // let loanAccountList = await loanAccRepo.findCollectionList(pl)
    const totalList = func[0].rows || []
    const totalQueryList = func[1].rows || []
    const collectionList = func[2].rows || []
    res.status(200).json(
      (res.body = {
        code: 0,
        message: 'Success',
        data: {
          size: Number(pl.size),
          page: Number(pl.page),
          total: totalQueryList[0] && totalQueryList[0].total_contract ? Number(totalQueryList[0].total_contract) : 0,
          totalContract: totalList[0] && totalList[0].total_contract ? Number(totalList[0].total_contract) : 0,
          totalAllDebtAmt:
            totalList[0] && totalList[0].total_all_debt_amt ? Number(totalList[0].total_all_debt_amt) : 0,
          totalAllPriAmt: totalList[0] && totalList[0].total_all_pri_amt ? Number(totalList[0].total_all_pri_amt) : 0,
          contractList: camelcaseKeys(collectionList)
        }
      })
    )
  } catch (e) {
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
}

async function getLoanInfomationCaseSummary(debtAckContractNumber) {
  const findLoanAccount = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)
  if (!findLoanAccount.length) {
    return {
      code: 1,
      statusCode: 400,
      message: 'Contract number not found'
    }
  }
  const loanAccount = findLoanAccount[0]

  const contractType = common.getContractTypeByLoanAcc(loanAccount)

  const findDisbursement = await disbursementRepo.findByDebtAckContractNumber(debtAckContractNumber)

  const successDisbursement = findDisbursement.find(
    (item) => item.tran_status == constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
  )
  let disbursementChannel = ''
  let disbursementDate = ''

  if (successDisbursement) {
    successDisbursement.partner_code && (disbursementChannel = successDisbursement.partner_code)
    successDisbursement.update_date && (disbursementDate = successDisbursement.update_date)
    successDisbursement.tran_date && (disbursementDate = successDisbursement.tran_date)
  } else {
    const waitingDisbursement = findDisbursement.find(
      (item) => item.tran_status != constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
    )
    waitingDisbursement?.partner_code && (disbursementChannel = waitingDisbursement.partner_code)
    waitingDisbursement?.update_date && (disbursementDate = successDisbursement.update_date)
    waitingDisbursement?.tran_date && (disbursementDate = successDisbursement.tran_date)
  }
  return {
    code: 0,
    statusCode: 200,
    message: 'Success',
    data: {
      contractType,
      disbursementChannel,
      disbursementDate
    }
  }
}

async function getAccountStatementInfo(contractNumber, fromDate, toDate, page = 0, limit = 10) {
  try {
    const listLoanAccount = await loanAccountV2Repo.findListContractByContractNumber(contractNumber)

    if (!listLoanAccount.length) {
      return {
        statusCode: 200,
        code: 0,
        message: 'Khong tim thay KUNN nao trong hop dong'
      }
    }
    fromDate = fromDate || new Date()
    toDate = toDate || new Date()
    let listResult = []
    const generalData = {
      sumPayment: 0,
      sumDebit: 0,
      prinAmountBeforeFromDate: 0,
      prinAmountBeforeToDate: 0,
      nonAllocateFirst: 0,
      nonAllocateAfter: 0
    }

    for (const loanAcc of listLoanAccount) {
      const data = await getLoanStatementInfo(loanAcc, fromDate, toDate)
      if (data.result.length) {
        listResult = [...listResult, ...data.result]
      }
      generalData.prinAmountBeforeFromDate += data.prinAmountBeforeFromDate
      generalData.prinAmountBeforeToDate += data.prinAmountBeforeToDate
      generalData.nonAllocateFirst += data.nonAllocateFirst
      generalData.nonAllocateAfter += data.nonAllocateAfter
    }
    listResult.sort(function (a, b) {
      return new Date(a.accountingDate) - new Date(b.accountingDate)
    })

    for (const rs of listResult) {
      if (rs.paymentAmount) {
        generalData.sumPayment += rs.paymentAmount
      } else {
        generalData.sumDebit += rs.lpiAmount + rs.penaltyAmount + rs.feeAmount + rs.prinAmount + rs.interestAmount
      }
    }
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MCC] GET ACCOUNT STATEMENT SUCCESSFULLY',
      data: {
        ...common.getPaginationData(listResult, page, limit),
        generalData
      }
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 99,
      message: '[LMS-MCC] GET ACCOUNT STATEMENT FAILED' + error.message
    }
  }
}
async function exportAccountStamentInfo(contractNumber, fromDate, toDate, isBlockLimit = true) {
  const startDate = moment().startOf('quarter').format('YYYY-MM-DD')
  const endDate = moment().endOf('quarter').format('YYYY-MM-DD')
  const reqLog = await requestLogRepo.findRequestByContractNumberRouteAndTime(
    contractNumber,
    '/loan-account/statement/export',
    startDate,
    endDate
  )
  if (reqLog.rowCount >= 3 && isBlockLimit) {
    return {
      statusCode: 400,
      message: 'LIMIT EXPORT ACCOUNT STATEMENT'
    }
  }
  const result = await getAccountStatementInfo(contractNumber, fromDate, toDate, -1)

  const smeInfo = await losService.getSmeInfo(contractNumber)
  const fillData = {
    smeName: smeInfo?.smeName || '',
    registrationNumber: smeInfo?.registrationNumber || '',
    registrationDate: smeInfo?.registrationDate
      ? moment(smeInfo?.registrationDate).format(constant.DATE_FORMAT.DDMMYYYY)
      : '',
    mainAddress: smeInfo?.address || '',
    phoneNumber: smeInfo?.phoneNumber || '',
    prinAmountBeforeFromDate: common.getCashString(result.data.generalData.prinAmountBeforeFromDate) || '0',
    prinAmountBeforeToDate: common.getCashString(result.data.generalData.prinAmountBeforeToDate) || '0',
    nonAllocateFirst: common.getCashString(result.data.generalData.nonAllocateFirst) || '0',
    nonAllocateAfter: common.getCashString(result.data.generalData.nonAllocateAfter) || '0',
    fromDate: moment(fromDate).format(constant.DATE_FORMAT.DDMMYYYY),
    toDate: moment(toDate).format(constant.DATE_FORMAT.DDMMYYYY),
    statements: result.data.result.map((item, index) => {
      return {
        ...item,
        stt: index + 1,
        accountingDate: moment(item.accountingDate).format(constant.DATE_FORMAT.DDMMYYYY),
        prinAmount: common.getCashString(item.prinAmount),
        interestAmount: common.getCashString(item.interestAmount),
        penaltyAmount: common.getCashString(item.penaltyAmount),
        lpiAmount: common.getCashString(item.lpiAmount),
        feeAmount: common.getCashString(item.feeAmount),
        paymentAmount: common.getCashString(item.paymentAmount)
      }
    }),
    sumPayment: common.getCashString(result.data.generalData.sumPayment),
    sumDebit: common.getCashString(result.data.generalData.sumDebit)
  }
  const templatePath = path.join(__dirname, '../templates/sophu_misa_final.docx')

  const pdfFileBuffer = await common.fillDataAndCreatePdf(templatePath, fillData)
  if (!pdfFileBuffer) {
    return {
      statusCode: 400,
      message: 'ERROR CREATE PDF'
    }
  }
  bssEsignService.signStatementMisa(contractNumber, pdfFileBuffer)
  // const pdfFileSigned = await bssEsignService.signStatementMisa(contractNumber, pdfFileBuffer)

  // if (!pdfFileSigned) {
  //   return {
  //     statusCode: 400,
  //     message: 'ERROR SIGNED PDF'
  //   }
  // }
  return {
    statusCode: 200,
    message: 'EXPORT FILE SUCCESSFULLY',
    url: bssEsignService.getFileUrl(contractNumber)
  }
}
async function getLoanStatementInfo(loanAccObj, fromDate, toDate) {
  const [listPayment, listPaymentDetail] = await Promise.all([
    paymentRepo.getListPaymentFromDateToDate({ debtAckContractNumber: loanAccObj.debt_ack_contract_number }),
    paymentRepo.getListPaymentDetailByDebtAckContractNumber({
      debtAckContractNumber: loanAccObj.debt_ack_contract_number
    })
  ])
  const result = []
  const historyPaid = []
  let paymentBeforeFromDate = 0
  let paymentBeforeToDate = 0
  let nonAllocateFirst = 0
  let nonAllocateAfter = 0
  let remainPrinAmount = Number(loanAccObj.apr_limit_amt)
  let sumPayment = 0

  const startDate = moment(fromDate).format(constant.DATE_FORMAT.YYYYMMDD2)
  const endDate = moment(toDate).format(constant.DATE_FORMAT.YYYYMMDD2)

  for (const payment of listPayment) {
    if (!Number(payment.installment_amort)) {
      continue
    }
    const paymentDate = moment(payment.payment_date).format(constant.DATE_FORMAT.YYYYMMDD2)
    const filterPaymentDetail = listPaymentDetail.filter((item) => item.payment_id == payment.id)
    sumPayment += Number(payment.installment_amort)
    const paymentObj = {
      debtAckContractNumber: loanAccObj.debt_ack_contract_number,
      contractNumber: loanAccObj.contract_number,
      accountingDate: payment.payment_date,
      paymentAmount: Number(payment.installment_amort),
      description: 'Nộp tiền'
    }
    const debitObj = {
      debtAckContractNumber: loanAccObj.debt_ack_contract_number,
      contractNumber: loanAccObj.contract_number,
      description: 'Thu nợ',
      accountingDate: payment.payment_date,
      lpiAmount: 0,
      penaltyAmount: 0,
      feeAmount: 0,
      prinAmount: 0,
      interestAmount: 0
    }
    let allocatedFirst = 0
    let allocatedAfter = 0
    for (const paymentDetail of filterPaymentDetail) {
      const paymentDetailAmount = Number(paymentDetail.amount)
      const allocatedDate = moment(paymentDetail.created_date).format(constant.DATE_FORMAT.YYYYMMDD2)
      if (allocatedDate < startDate && paymentDate < startDate) {
        allocatedFirst += paymentDetailAmount
      }
      if (allocatedDate <= endDate && paymentDate <= endDate) {
        allocatedAfter += paymentDetailAmount
      }
      if (paymentDetail.type == constant.BILL_ON_DUE.TYPE.PRIN) {
        if (paymentDate < startDate) {
          paymentBeforeFromDate += paymentDetailAmount
        }
        if (paymentDate <= endDate) {
          paymentBeforeToDate += paymentDetailAmount
        }
        debitObj.prinAmount += paymentDetailAmount
        remainPrinAmount -= paymentDetailAmount
      }
      if (paymentDetail.type == constant.BILL_ON_DUE.TYPE.INT) {
        debitObj.interestAmount += paymentDetailAmount
      }
      if (
        paymentDetail.type == constant.BILL_ON_DUE.TYPE.LPI_INT ||
        paymentDetail.type == constant.BILL_ON_DUE.TYPE.LPI_PRIN
      ) {
        debitObj.lpiAmount += paymentDetailAmount
      }
      if (paymentDetail.type == constant.BILL_ON_DUE.TYPE.FEE) {
        if (paymentDetail.is_annex && paymentDetail.description == 'Phí phạt tất toán sớm') {
          debitObj.penaltyAmount += paymentDetailAmount
        } else {
          debitObj.feeAmount += paymentDetailAmount
        }
      }
    }
    if (paymentDate >= startDate && paymentDate <= endDate) {
      result.push(paymentObj)
      result.push(debitObj)
    }
    const historyPaidObj = {
      ..._.omit(debitObj, ['description', 'lpiAmount']),
      paymentAmount: paymentObj.paymentAmount,
      remainPrinAmount
    }
    nonAllocateFirst += paymentObj.paymentAmount - allocatedFirst
    nonAllocateAfter += paymentObj.paymentAmount - allocatedAfter
    historyPaid.push(historyPaidObj)
  }

  const prinAmountBeforeFromDate = Number(loanAccObj.apr_limit_amt) - paymentBeforeFromDate
  const prinAmountBeforeToDate = Number(loanAccObj.apr_limit_amt) - paymentBeforeToDate

  return {
    result,
    prinAmountBeforeFromDate,
    prinAmountBeforeToDate,
    historyPaid,
    sumPayment,
    nonAllocateFirst,
    nonAllocateAfter
  }
}
const getLoanContractDetailByContractNumber = async function (req, res) {
  try {
    const pl = req.query
    if (!pl.contractNumber) return res.status(400).json({ code: 2, message: 'input invalid' })
    const func = await Promise.all([
      loanAccountRepo.findByContractNumberAndStatus(global.poolRead, { contractNumber: pl.contractNumber }),
      merchantLimitRepo.findActiveByContractNumberAndCustId(global.poolRead, { contractNumber: pl.contractNumber })
    ])

    const loanAccList = func[1].rows[0] || []
    const merchantLimitList = func[0].rows || []

    const dataFinal = {
      contractNumber: loanAccList?.contract_number,
      approvalAmount: loanAccList?.apr_limit_amt,
      loanAmount: (loanAccList?.apr_limit_amt - loanAccList?.remain_limit_amt).toString(),
      availableAmount: loanAccList?.remain_limit_amt
    }
    const dataKunnFinal = merchantLimitList.map((kd) => {
      const dataKunn = {
        kunnNumber: kd?.debt_ack_contract_number,
        status: constant.DEBT_ACK_STATUS[kd?.status] || '',
        loanAmount: kd?.apr_limit_amt
      }
      return dataKunn
    })

    dataFinal.listDebtAckContract = dataKunnFinal || []
    return res.status(200).json({
      code: 0,
      message: 'success',
      data: dataFinal || {}
    })
  } catch (e) {
    console.log(e)
    res.status(500).json({ code: -1, message: e.message })
  }
}

async function getStatementInfoByKunnNumber(debtAckContractNumber) {
  const findLoanAcc = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)
  if (!debtAckContractNumber || !findLoanAcc.length) {
    return {
      statusCode: 400,
      code: 1,
      message: 'Loan account not found'
    }
  }
  const loanAcc = findLoanAcc[0]
  const result = await getLoanStatementInfo(loanAcc)
  return {
    statusCode: 200,
    code: 0,
    message: 'GET STATEMENT INFO BY KUNN SUCCESSFULLY',
    data: {
      historyPaid: result.historyPaid,
      sumPayment: result.sumPayment
    }
  }
}

async function refundMoney(payload) {
  try {
    console.log('Refund with payload ' + JSON.stringify(payload))
    const findLoanAccount = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(payload.debtAckcontractNumber)
    if (!findLoanAccount.length) {
      return {
        code: 1,
        statusCode: 400,
        message: 'Debt ack Contract number not found'
      }
    }
    const loanAccount = findLoanAccount[0]

    if (Number(loanAccount.to_collected) >= 0 || Number(loanAccount.non_allocation_amt) <= 0) {
      return {
        code: 1,
        statusCode: 200,
        message: 'Contract number has no redunt amt'
      }
    }
    if (Number(loanAccount.non_allocation_amt) != Number(payload.amt)) {
      return {
        code: 1,
        statusCode: 200,
        message: 'Money refund is not same as refund amount of account'
      }
    }
    console.log(loanAccount.payment_status)
    if (loanAccount.payment_status != constant.PAYMENT_STATUS.DONE) {
      return {
        code: 1,
        statusCode: 200,
        message: 'Debt ack contract number is not terminated'
      }
    }
    const listPaymentRedunt = await paymentRepo.getRemainAmortByContractNumberV2(payload.debtAckcontractNumber)
    const refundDate = new Date()
    const listPaymentDetail = []
    for (const payment of listPaymentRedunt) {
      listPaymentDetail.push([
        null,
        payment.id,
        Number(loanAccount.non_allocation_amt),
        payload.ownerId,
        global.isTesting,
        payload.createdUser,
        constant.PAYMENT_DETAIL_POST_AFFECT.REFUND,
        payload.debtAckcontractNumber
      ])

      const dataUpdatePayment = {
        payId: payment.id,
        nonAllocatedAmt: 0,
        comments: payment.comments,
        tranStatus: constant.PAYMENT.TRANS_STATUS.REFUND
      }
      paymentRepo.updateNonAllocatedAmtById(dataUpdatePayment)
    }
    paymentDetailRepo.insBatchPaymentDetail(global.poolWrite, listPaymentDetail)
    loanAccountV2Repo.updateLoanAccountAmount({
      debtAckContractNumber: loanAccount.debt_ack_contract_number,
      toCollect: Number(payload.amt),
      nonAllocationAmt: -1 * Number(payload.amt)
    })
    const dateNow = new Date()
    const plInsertTransLog = {
      contractNumber: loanAccount.contract_number,
      loanId: loanAccount.loan_id,
      tranType: 'BANK',
      refId: payload.partnerTranNo,
      amtNumber: Number(payload.amt),
      tranDate: dateNow,
      valueDate: dateNow,
      tranDesc: 'Refund for ' + loanAccount.debt_ack_contract_number,
      tranStatus: '200',
      createdUser: constant.config.createdBy,
      sessionId: dateNow.getTime(),
      ownerId: constant.config.ownerId
    }
    const rsInsertTransLog = await tranLogRepo.insTranLog(global.poolWrite, plInsertTransLog)

    disbursementRepo.insert(global.poolWrite, {
      contractNumber: loanAccount.contract_number,
      debtAckContractNumber: loanAccount.debt_ack_contract_number,
      loanId: loanAccount.loan_id,
      amt: Number(loanAccount.non_allocation_amt),
      ccycd: 'VND',
      tranId: rsInsertTransLog.rows[0].tran_id,
      tranDate: refundDate,
      valueDate: refundDate,
      partnerCode: payload.partnerCode,
      receiverName: payload.recieverName,
      partnerTranNo: payload.partnerTranNo,
      apiCode: payload.apiCode,
      tranDesc: payload.tranDesc,
      bankCode: payload.bankCode,
      bankName: payload.bankName,
      bankAccount: payload.bankAccount,
      createdUser: payload.createdUser,
      ownerId: payload.ownerId,
      sessionId: loanAccount.loan_id + '_' + moment().toDate().getTime().toString(),
      comments: 'REFUND',
      reference: common.makeId(8).toUpperCase(),
      tranStatus: constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
    })

    return {
      code: 0,
      statusCode: 200,
      message: 'Refund money successfully'
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 999,
      message: error.message
    }
  }
}

async function getLoanContractDataByContractNumber(contractNumber) {
  try {
    const listLoanAccount = await loanAccountV2Repo.findListContractByContractNumber(contractNumber)
    if (!listLoanAccount.length) {
      return {
        statusCode: 200,
        code: 0,
        message: 'Khong tim thay KUNN nao trong hop dong'
      }
    }

    const listResult = []

    for (const loanAccount of listLoanAccount) {
      const resultObj = await calObjContract(loanAccount)
      listResult.push(resultObj)
    }
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MCC] GET LOAN ACCOUNT  SUCCESSFULLY',
      data: listResult
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 99,
      message: '[LMS-MCC] GET LOAN ACCOUNT FAILED' + error.message
    }
  }
}
async function calObjContract(loanAccount) {
  try {
    const [listInstallment, listIrCharge] = await Promise.all([
      installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
        debtAckContractNumber: loanAccount.debt_ack_contract_number
      }),
      irChargeRepo.findByDebtAckContractNumberAndProductCodeV2(loanAccount.debt_ack_contract_number)
    ])
    let dpd = loanAccount.dpd
    const overDueIrCharge = listIrCharge.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_INTEREST)
    const overDuePrinCharge = listIrCharge.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_PRIN)
    const dataInstallment = {
      amount: 0,
      overDueIrRate: common.roundUp((Number(overDueIrCharge.ir_value) * 100) / constant.CALCUCFG.totalDayOfYear, 3),
      overDuePrinRate: common.roundUp((Number(overDuePrinCharge.ir_value) * 100) / constant.CALCUCFG.totalDayOfYear, 3)
    }
    dataInstallment.amount += Number(loanAccount.to_collect)
    let maxEndDate = loanAccount.end_date

    if (listInstallment.length) {
      const minEndDate = _.minBy(
        listInstallment.filter((item) => item.type == 2),
        'end_date'
      ).end_date
      maxEndDate = _.maxBy(
        listInstallment.filter((item) => item.type == 2),
        'end_date'
      ).end_date
      dataInstallment.endDate = moment(minEndDate).format(constant.DATE_FORMAT.YYYYMMDD2)
      if (dpd == 0) {
        const today = moment().format(constant.DATE_FORMAT.YYYYMMDD2)
        dpd = common.getDifferencesDays(today, minEndDate, false)
      }
      const listNearestInstallment = listInstallment.filter(
        (item) =>
          moment(item.end_date).format(constant.DATE_FORMAT.YYYYMMDD2) ==
          moment(minEndDate).format(constant.DATE_FORMAT.YYYYMMDD2)
      )
      for (const nearestInsm of listNearestInstallment) {
        if (Number(loanAccount.to_collect) <= 0) {
          dataInstallment.amount += Number(nearestInsm.amount)
        }
        if (nearestInsm.type == constant.INSTALLMENT.TYPE.FEE) {
          dataInstallment.feeAmt = Number(nearestInsm.amount)
        }
      }
    }
    dataInstallment.amount = Math.max(dataInstallment.amount, 0)
    let status = loanAccount.status == 0 ? 'SIG' : 'ACT'
    if (loanAccount.payment_status == 0 || loanAccount.status == 3) {
      status = 'TER'
    } else if (loanAccount.status == 2) {
      status = 'ANN'
    }
    return {
      contractNumber: loanAccount.contract_number,
      status,
      debtAckContractNumber: loanAccount.debt_ack_contract_number,
      loanAmount: Number(loanAccount.apr_limit_amt),
      tenor: loanAccount.tenor,
      dpd,
      endDateLoan: maxEndDate ? moment(maxEndDate).format(constant.DATE_FORMAT.YYYYMMDD2) : null,
      activeDate: loanAccount.active_date
        ? moment(loanAccount.active_date).format(constant.DATE_FORMAT.YYYYMMDD2)
        : null,
      contractType: loanAccount.contract_type,
      dataInstallment
    }
  } catch (error) {
    console.log('Error at calObjContract:', error.message)
    return {}
  }
}

async function getLoanAccountByKunnNumber(kunnNumber) {
  try {
    if (kunnNumber == null || kunnNumber == undefined) {
      return {
        statusCode: 200,
        code: 0,
        message: 'Input is invalid'
      }
    }
    const loanAccountInfo = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(kunnNumber)
    if (!loanAccountInfo.length) {
      return {
        statusCode: 200,
        code: 0,
        message: 'Khong tim thay KUNN nao trong hop dong'
      }
    }

    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MCC] GET LOAN ACCOUNT SUCCESSFULLY',
      data: loanAccountInfo[0] ?? {}
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 99,
      message: '[LMS-MCC] GET LOAN ACCOUNT FAILED' + error.message
    }
  }
}

async function getListKunnByCustId(payload) {
  try {
    const { custId, channel, type, orderBy = 'desc' } = payload
    if (!custId) {
      return {
        statusCode: 400,
        code: 1,
        message: 'custId is required'
      }
    }
    const generalData = {
      loanAmount: 0,
      totalDebtAmt: 0,
      dueFlag: null,
      dueDate: null
    }
    const dueFlag = {
      overDue: false,
      upcomingDue: false,
      normal: false
    }
    const listCustId = custId.split(',')
    const whereObj = {
      cust_id: In(listCustId),
      status: constant.LOAN_ACC_STATUS.ACT,
      payment_status: DifferentFrom(constant.PAYMENT_STATUS.DONE)
    }
    if (channel) {
      whereObj.partner_code = channel
    }
    const listLoanAccount = await LoanAccountRepository.findAll({
      where: whereObj,
      order: {
        loan_id: orderBy
      }
    })
    const listResult = await Promise.all(
      listLoanAccount.map(async (loanAccount) => {
        const rsDetailInfo = await getDetailKunnInfo(loanAccount.debt_ack_contract_number)
        const { data } = rsDetailInfo
        const kunnData = {
          custId: loanAccount.cust_id,
          loanId: loanAccount.loan_id,
          debtAckContractNumber: loanAccount.debt_ack_contract_number,
          loanAmount: Number(loanAccount.apr_limit_amt),
          tenor: loanAccount.tenor,
          interestRate: Number(data.loan_on_due_int_rate),
          // voucherCode: loanAccount.voucher_code,
          type,
          startDate: data.start_date,
          endDate: data.end_date,
          totalDebtAmt: data.total_debt_amt,
          dueFlag: data.due_flag,
          dueDay: data.due_day,
          dueDate: data.due_date,
          activeDate: data.activated_date
        }
        generalData.loanAmount += kunnData.loanAmount

        if (kunnData.dueFlag == -1 && kunnData.totalDebtAmt > 0) {
          dueFlag.overDue = true
        } else if (kunnData.dueFlag == 1 && kunnData.totalDebtAmt > 0) {
          dueFlag.upcomingDue = true
        } else {
          dueFlag.normal = true
        }
        return kunnData
      })
    )

    if (dueFlag.overDue) {
      const listKunnOverDue = listResult.filter((item) => item.dueFlag == -1)
      generalData.totalDebtAmt = _.sumBy(listKunnOverDue, 'totalDebtAmt')
      generalData.dueFlag = -1
      generalData.dueDate = _.minBy(listKunnOverDue, 'dueDate')?.dueDate
    } else if (dueFlag.upcomingDue) {
      const listKunnUpcomingDue = listResult.filter((item) => item.dueFlag == 1)
      generalData.totalDebtAmt = _.sumBy(listKunnUpcomingDue, 'totalDebtAmt')
      generalData.dueFlag = 1
      generalData.dueDate = _.minBy(listKunnUpcomingDue, 'dueDate')?.dueDate
    } else {
      const listKunnNormal = listResult.filter((item) => item.dueFlag == 0)
      generalData.totalDebtAmt = _.sumBy(listKunnNormal, 'totalDebtAmt')
      generalData.dueFlag = 0
      generalData.dueDate = _.minBy(listKunnNormal, 'dueDate')?.dueDate
    }

    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MCC] GET LOAN ACCOUNT SUCCESSFULLY',
      data: listResult,
      generalData
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 99,
      message: '[LMS-MCC] GET LOAN ACCOUNT FAILED' + error.message
    }
  }
}

async function getDetailKunnInfo(debtAckContractNumber) {
  try {
    if (!debtAckContractNumber) {
      return {
        statusCode: 200,
        code: 1,
        message: 'debtAckContractNumber is required'
      }
    }
    const loanAccountInfo = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)
    if (!loanAccountInfo.length) {
      return {
        statusCode: 200,
        code: 1,
        message: 'KUNN not found'
      }
    }
    const loanAccountObj = loanAccountInfo[0]

    const [
      listBillOnDue,
      listAllInstallment,
      listIrCharge,
      listActivedInstallmentDeduction,
      custObj,
      currentAmort,
      insurance
    ] = await Promise.all([
      billOnDueRepo.getBillOnDuePaymentNotCompleteV3(debtAckContractNumber, 'desc'),
      installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
        debtAckContractNumber,
        isNotCompleted: false
      }),
      irChargeRepo.findByDebtAckContractNumberAndProductCodeV2(debtAckContractNumber),
      installmentDeductionRepo.findListInstallmentDeduction(debtAckContractNumber),
      crmService.getCustomerInfo(
        global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccountObj.cust_id,
        {}
      ),
      loanAmortRepo.findCurrentAmort(debtAckContractNumber),
      insuranceRepo.getInsuranceActive(debtAckContractNumber)
    ])
    const listInstallment = listAllInstallment.filter((item) => item.closed == constant.INSTALLMENT.CLOSE.FALSE)

    let status
    if (loanAccountObj.payment_status == constant.PAYMENT_STATUS.DONE) {
      status = 'TER'
    } else if (loanAccountObj.status == constant.DEBT_ACK_STATUS.ACTIVE) {
      status = 'ACT'
    } else {
      status = 'SIG'
    }
    const currentPrinOverDueIrRate = listIrCharge.find(
      (item) => item.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_PRIN
    )?.ir_value
    const currentPrinOnDueIrRate = listIrCharge.find(
      (item) => item.ir_type == constant.IR_CHARGE_TYPE.ON_DUE_PRIN
    )?.ir_value

    const result = {
      contract_status: status,
      loan_tenor: loanAccountObj.tenor,
      product_name: loanAccountObj.product_code,
      debt_ack_contract_number: loanAccountObj.debt_ack_contract_number,
      cust_id: loanAccountObj.cust_id,
      activated_date: moment(loanAccountObj.active_date).format(constant.DATE_FORMAT.YYYYMMDD2),
      installment_list: [],
      loan_over_int_rate: `${common.roundV2((currentPrinOverDueIrRate * 100) / constant.CALCUCFG.totalDayOfYear, 2)}%/ ngày`,
      loan_on_due_int_rate: Number(currentPrinOnDueIrRate),
      terminated_date: moment(loanAccountObj.termination_date || loanAccountObj.end_date).format(
        constant.DATE_FORMAT.YYYYMMDD2
      ),
      loan_amount: Number(loanAccountObj.rls_amt) || Number(loanAccountObj.apr_limit_amt),
      total_outstanding_amt: Number(loanAccountObj.prin_amt),
      non_allocate_amt: Number(loanAccountObj.non_allocation_amt),
      to_collect: Number(loanAccountObj.to_collect),
      dpd: loanAccountObj.dpd,
      dpd_collection: loanAccountObj.dpd,
      total_debt_amt: 0,
      lpi: 0,
      pri_amt: 0,
      int_amt: 0,
      fee_amt: 0,
      princical_amt: 0,
      int_fee_amt: 0,
      debt_remain_amt: 0,
      customer_name: custObj.fullName,
      insurance_gcn: insurance?.[0]?.s3_url || '',
      insurance_gcn_type: insurance?.[0]?.s3_url?.split('.').pop() || ''
    }
    const currentDate = moment().format(constant.DATE_FORMAT.YYYYMMDD2)
    const nearestBill = listBillOnDue.length ? listBillOnDue[0] : null
    const formatDueDate = nearestBill?.on_due_date
      ? moment(nearestBill.on_due_date).format(constant.DATE_FORMAT.YYYYMMDD2)
      : ''
    let dpd = loanAccountObj.dpd || constant.DEFAULT_DPD
    if (listInstallment.length) {
      const minEndDate = _.minBy(
        listInstallment.filter((item) => item.type == 2),
        'end_date'
      ).end_date

      if (dpd == 0) {
        const today = moment().format(constant.DATE_FORMAT.YYYYMMDD2)
        dpd = common.getDifferencesDays(today, minEndDate, false)
      }
    }
    result.dpd_collection = dpd

    if (dpd >= 0) {
      result.due_flag = -1
      result.due_day = dpd
    } else if (dpd >= -5 && dpd < 0) {
      result.due_flag = 1
      result.due_day = Math.abs(dpd)
    } else {
      result.due_flag = 0
    }

    if (!listInstallment.length || currentDate == formatDueDate) {
      result.due_date = formatDueDate
      for (const bill of listBillOnDue) {
        const billRemainAmount = Number(bill.remain_amount)
        if (nearestBill.num_cycle == bill.num_cycle) {
          if (bill.type == constant.BILL_ON_DUE.TYPE.PRIN) {
            result.princical_amt += billRemainAmount
          }
          if ([constant.BILL_ON_DUE.TYPE.INT, constant.BILL_ON_DUE.TYPE.FEE].includes(bill.type)) {
            result.int_fee_amt += billRemainAmount
          }
        } else {
          result.debt_remain_amt += billRemainAmount
        }
        if ([constant.BILL_ON_DUE.TYPE.LPI_INT, constant.BILL_ON_DUE.TYPE.LPI_INT].includes(bill.type)) {
          result.lpi += billRemainAmount
        } else {
          result.total_debt_amt += billRemainAmount
        }
        if (bill.type === constant.BILL_ON_DUE.TYPE.PRIN) {
          result.pri_amt += billRemainAmount
        }
        if (bill.type === constant.BILL_ON_DUE.TYPE.INT) {
          result.int_amt += billRemainAmount
        }
        if (bill.type === constant.BILL_ON_DUE.TYPE.FEE) {
          result.fee_amt += billRemainAmount
        }
      }
    } else {
      const installmentObj = listInstallment[0]
      result.due_date = moment(installmentObj.due_date).format(constant.DATE_FORMAT.YYYYMMDD2)
      result.start_date = moment(installmentObj.start_date).format(constant.DATE_FORMAT.YYYYMMDD2)
      result.end_date = moment(installmentObj.end_date).format(constant.DATE_FORMAT.YYYYMMDD2)
      for (const installment of listInstallment) {
        const installmentRemainAmount = Number(installment.remain_amount)

        if (installment.num_cycle == installmentObj.num_cycle) {
          if (installment.type == constant.INSTALLMENT.TYPE.PRIN) {
            result.princical_amt += installmentRemainAmount
          }
          if ([constant.INSTALLMENT.TYPE.INT, constant.INSTALLMENT.TYPE.FEE].includes(installment.type)) {
            result.int_fee_amt += installmentRemainAmount
          }
        }
      }
      if (!listBillOnDue.length) {
        result.pri_amt += result.princical_amt
        result.int_amt += result.int_fee_amt
        result.total_debt_amt += result.princical_amt + result.int_fee_amt
      }
      for (const bill of listBillOnDue) {
        const billRemainAmount = Number(bill.remain_amount)
        result.debt_remain_amt += billRemainAmount
        if ([constant.BILL_ON_DUE.TYPE.LPI_INT, constant.BILL_ON_DUE.TYPE.LPI_INT].includes(bill.type)) {
          result.lpi += billRemainAmount
        } else {
          result.total_debt_amt += billRemainAmount
        }
        if (bill.type === constant.BILL_ON_DUE.TYPE.PRIN) {
          result.pri_amt += billRemainAmount
        }
        if (bill.type === constant.BILL_ON_DUE.TYPE.INT) {
          result.int_amt += billRemainAmount
        }
        if (bill.type === constant.BILL_ON_DUE.TYPE.FEE) {
          result.fee_amt += billRemainAmount
        }
      }
    }
    result.due_amount = result.princical_amt + result.int_fee_amt
    result.debt_remain_amt += result.lpi - result.non_allocate_amt
    result.total_debt_amt += result.lpi - result.non_allocate_amt

    result.debt_remain_amt = Math.max(result.debt_remain_amt, 0)
    result.total_debt_amt = Math.max(result.total_debt_amt, 0)
    if (listInstallment.length) {
      result.remaining_installment = loanAccountObj.tenor - listInstallment[0].num_cycle + 1
    } else {
      result.remaining_installment = 0
    }
    for (let numCycle = 1; numCycle <= (currentAmort.tenor || loanAccountObj.tenor); numCycle++) {
      const insmObj = {
        instal_num: numCycle,
        due_date: '',
        prin_amt_instal: 0,
        int_amt_instal: 0,
        original_int_amt_install: 0,
        fee_amt_instal: 0,
        total_amt: 0
      }
      const listInstallmentCycle = listAllInstallment.filter((item) => item.num_cycle == numCycle)
      for (const insm of listInstallmentCycle) {
        const installmentAmount = Number(insm.amount)
        if (insm.type == constant.INSTALLMENT.TYPE.PRIN) {
          insmObj.prin_amt_instal += installmentAmount
          insmObj.due_date = moment(insm.end_date).format(constant.DATE_FORMAT.YYYYMMDD2)
        }
        if (insm.type == constant.BILL_ON_DUE.TYPE.INT) {
          insmObj.int_amt_instal += installmentAmount
          const checkPromotionDeduction =
            listActivedInstallmentDeduction.length &&
            listActivedInstallmentDeduction.find((item) => item.installment_id == insm.id)
          if (checkPromotionDeduction) {
            insmObj.original_int_amt_install += Number(checkPromotionDeduction.original_amount)
          } else {
            insmObj.original_int_amt_install += installmentAmount
          }
        }
        if (insm.type == constant.BILL_ON_DUE.TYPE.FEE) {
          insmObj.fee_amt_instal += installmentAmount
        }
        insmObj.total_amt += installmentAmount
      }
      if (numCycle == 1) {
        result.loan_installment = insmObj.prin_amt_instal + insmObj.original_int_amt_install
      }
      result.installment_list.push(insmObj)
    }

    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MCC] GET LOAN ACCOUNT SUCCESSFULLY',
      data: result
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 99,
      message: '[LMS-MCC] GET LOAN ACCOUNT FAILED' + error.message
    }
  }
}
async function getLoanInfoToNotify(debtAckContractNumber) {
  try {
    const rsDetailinfo = await getDetailKunnInfo(debtAckContractNumber)
    if (rsDetailinfo.code != 0) {
      return rsDetailinfo
    }
    const { data } = rsDetailinfo
    if (!data.installment_list.length) {
      return { code: -1, message: '[LMS-MC] Installment is empty', statusCode: 200 }
    }

    if (data.dpd_collection < -5) {
      return { code: -1, message: '[LMS-MC] Dpd does not satisfy the condition.', statusCode: 200 }
    }
    if (data.total_debt_amt == 0) {
      return { code: -1, message: '[LMS-MC] Paid in full', statusCode: 200 }
    }
    const dueType = {
      NEAR_TO_DUE: 'NEAR_TO_DUE',
      IN_DUE: 'IN_DUE',
      OVER_DUE: 'OVER_DUE'
    }
    const requestBodyNotify = {
      money: data.total_debt_amt,
      dueDate: data.due_date,
      contractNumber: data.debt_ack_contract_number,
      custId: data.cust_id,
      dpd: data.dpd_collection,
      product_code: data.product_name
    }
    if (data.dpd_collection == 0) {
      requestBodyNotify.type = dueType.IN_DUE
    } else if (data.dpd_collection >= -5 && data.dpd_collection < 0) {
      requestBodyNotify.type = dueType.NEAR_TO_DUE
    } else if (data.dpd_collection >= 1) {
      requestBodyNotify.type = dueType.OVER_DUE
    }
    backendMobileService.callNotifyDpd(requestBodyNotify)
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MCC] GET LOAN ACCOUNT SUCCESSFULLY',
      data: requestBodyNotify
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 99,
      message: '[LMS-MCC] getLoanInfoToNotify FAILED' + error.message
    }
  }
}
async function getLoanContractSMA(input) {
  try {
    if (!input || !input.custId) {
      return {
        statusCode: 400,
        code: 2,
        message: 'Input custId is required'
      }
    }
    const listLoanAccount = await loanAccountV2Repo.findListContractByCustId(input.custId)
    if (!listLoanAccount.length) {
      return {
        statusCode: 200,
        code: 0,
        message: 'Not found contract'
      }
    }

    const listResult = []

    for (const loanAccount of listLoanAccount) {
      const resultObj = await calObjContract(loanAccount)
      listResult.push(resultObj)
    }
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MCC] GET LOAN ACCOUNT SUCCESSFULLY',
      data: listResult
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 99,
      message: '[LMS-MCC] GET LOAN ACCOUNT FAILED' + error.message
    }
  }
}
async function cancelLoanAccount(payload) {
  if (!payload.debtAckContractNumber) {
    return {
      statusCode: 400,
      code: 1,
      message: 'Input debtAckContractNumber is required'
    }
  }
  !payload.cancelDate && (payload.cancelDate = moment().format(constant.DATE_FORMAT.YYYYMMDD2))

  const checkLoanAccount = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(payload.debtAckContractNumber)

  if (!checkLoanAccount.length) {
    return {
      statusCode: 400,
      code: 2,
      message: 'KUNN not found'
    }
  }
  const loanAccountObj = checkLoanAccount[0]

  if (loanAccountObj.status == constant.DEBT_ACK_STATUS.CANCEL) {
    return {
      statusCode: 400,
      code: 2,
      message: 'KUNN is canceled'
    }
  }

  try {
    await Promise.all([
      loanAccountV2Repo.updateStatusLoanAccount(payload.debtAckContractNumber, constant.DEBT_ACK_STATUS.CANCEL),
      losService.callCancelKunn(loanAccountObj, payload.cancelDate),
      loanStatusHstService.insertNewAndUpdateOldRecord(payload.debtAckContractNumber, constant.LOAN_ACC_STATUS.ANN),
      installmentRepo.updateStatusActiveToCancel(payload.debtAckContractNumber)
    ])

    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Cancel successfully'
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 99,
      message: error.message
    }
  }
}

async function updateLoanAccountService(payload) {
  try {
    console.log('updateLoanAccount with payload ' + JSON.stringify(payload))
    if (payload?.debt_ack_contract_number != null && payload.channel != null) {
      const findLoanAccount = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(
        payload.debt_ack_contract_number
      )
      if (!findLoanAccount.length) {
        return {
          code: 1,
          statusCode: 400,
          message: 'Debt ack Contract number not found'
        }
      }
      loanAccountV2Repo.updateLoanAccount(global.poolWrite, payload)
      return {
        code: 0,
        statusCode: 200,
        message: 'Update loan account successfully'
      }
    } else {
      return {
        code: 1,
        statusCode: 400,
        message: 'Input is invalid'
      }
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 999,
      message: error.message
    }
  }
}
async function getAmortization(payload) {
  try {
    console.log('getAmortization with payload ' + JSON.stringify(payload))
    if (!payload?.debtAckContractNumber) {
      return {
        code: 2,
        statusCode: 400,
        message: 'Input is invalid'
      }
    }
    const amortInit = await loanAmortRepo.findInitAmort(payload?.debtAckContractNumber)

    if (!amortInit.debt_ack_contract_number) {
      return {
        code: 3,
        statusCode: 400,
        message: 'DebtAckContractNumber not found'
      }
    }

    const [rsDataInsmInit, rsLoanAcc, rsIrCharge] = await Promise.all([
      installmentRepo.findInstalByDebtAckAndAmortId(payload?.debtAckContractNumber, amortInit?.amort_id),
      loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, payload),
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, {
        debtAckContractNumber: payload?.debtAckContractNumber,
        irType: 1
      })
    ])
    const listDataCycles = installmentService.processListInstallment(
      rsDataInsmInit,
      rsIrCharge.rows?.[0]?.ir_value,
      isPrinEveryCycle(rsLoanAcc.rows?.[0])
    )

    return {
      code: 0,
      statusCode: 200,
      data: listDataCycles,
      message: 'Get Amortization successfully'
    }
  } catch (error) {
    // console.log(error)
    return {
      statusCode: 500,
      code: 999,
      message: error.message
    }
  }
}
module.exports = {
  getLoanContractSMA,
  getLoanContractDataByContractNumber,
  getCollectionInfo,
  getWelcomePackage,
  getLoanContract,
  getLoanDebtAckContract,
  getCollectionTotalList,
  getLoanInfomationCaseSummary,
  getAccountStatementInfo,
  getLoanContractDetailByContractNumber,
  exportAccountStamentInfo,
  getStatementInfoByKunnNumber,
  refundMoney,
  getLoanAccountByKunnNumber,
  getListKunnByCustId,
  getDetailKunnInfo,
  getLoanInfoToNotify,
  cancelLoanAccount,
  updateLoanAccountService,
  getAmortization
}
