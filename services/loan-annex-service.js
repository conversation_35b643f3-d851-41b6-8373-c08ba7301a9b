const common = require('../utils/common')
const helper = require('../utils/helper')
const moment = require('moment')
const loanAnnexRepo = require('../repositories/loan-annex-repo')
const installmentRepo = require('../repositories/installment-repo')
const loanAccountRepo = require('../repositories/debt-ack-contract-repo')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const irChargeRepo = require('../repositories/ir-charge-repo')
const HashMap = require('hashmap')
const constant = require('../utils/constant')
const paymentRepo = require('../repositories/payment-repo')
const irRepo = require('../repositories/ir-repo')
const loanAccountV2Repo = require('./../repositories/loan-account-repo')
const promotionRepo = require('./../repositories/promotion-repo')
const installmentDeductionRepo = require('./../repositories/promotion-installment-deduction-repo')
const repaymentService = require('./repayment-service')
const installmentService = require('./installment-service')
const backendMobileService = require('../other-services/backend-mobile-service')
const smsService = require('../other-services/sms-service')
const actionAuditService = require('../other-services/action-audit-service')
const lodash = require('lodash')
const loanAmortRepo = require('../repositories/loan-amort-repo')
const loanAnnexPendingRepo = require('../repositories/loan-annex-pending-repo')
const { LoanAnnexRepository, LoanAccountRepository } = require('../repositories-v2')
const { doCreateBillOnDue } = require('./bill-service')
const { doCalculatorDpdRiskGrp } = require('./contract-risk-grp-service')
const { doCalcuIrEveryDay } = require('./ir-service')
const createLoanAnnexMc = async function (req, res) {
  try {
    return res.status(200).json(
      (res.body = {
        code: 0,
        message: 'Function unvalible, please contact system administrator support'
      })
    )
  } catch (err) {
    console.log(err)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}
const cancelAnnex = async function (pl) {
  try {
    common.log('req body cancelAnnex: ' + JSON.stringify(pl))
    if (pl == undefined || pl.debtAckContractNumber == undefined || pl.scanDate == undefined) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Thieu du lieu dau vao'
      }
    }
    pl.isAnnex = 1
    const rsBill = await billOnDueRepo.getBillOnDuePaymentNotCompleteV3(
      pl.debtAckContractNumber,
      'asc',
      constant.BILL_ON_DUE.STATUS.NOT_ACTIVE
    )
    if (rsBill.rowCount == 0) {
      return {
        statusCode: 200,
        code: 1,
        message: '[LMS-MC] Khong tim thay HD co annex'
      }
    }
    let debtAckContractNumber, annexNumber
    for (const billObj of rsBill) {
      if (moment(billObj.on_due_date).toDate().getTime() <= moment(pl.scanDate).toDate().getTime()) {
        debtAckContractNumber = billObj.debt_ack_contract_number
        annexNumber = billObj.annex_number
        const plUpdate = {
          status: constant.BILL_ON_DUE.STATUS.CANCEL,
          billId: billObj.id
        }
        await billOnDueRepo.updateStatusBill(global.poolWrite, plUpdate)
      }
    }

    if (!debtAckContractNumber && !annexNumber) {
      return {
        statusCode: 200,
        code: 1,
        message: '[LMS-MC] Chua den ngay thuc hien annex'
      }
    }
    const plUpdateInsm = {
      newStatus: constant.INSTALLMENT.STATUS.ARCHIVE,
      debtAckContractNumber: debtAckContractNumber || '',
      status: constant.INSTALLMENT.STATUS.DEACTIVE,
      annexNumber
    }
    const plUpdateAnnex = {
      annexStatusNew: constant.ANNEX.STATUS.CANCEL,
      annexNumber: annexNumber || '',
      annexStatusOld: constant.ANNEX.STATUS.INIT
    }
    await installmentRepo.updateStatusByDebtAckContract(global.poolWrite, plUpdateInsm)
    await loanAnnexRepo.updateStatusLoanAnnex(global.poolWrite, plUpdateAnnex)

    backendMobileService.callNotiUpdateAnnexStatus({
      contract_number: debtAckContractNumber,
      annex_status: constant.ANNEX.STATUS_COMMON.CANCEL
    })
    await loanAmortRepo.updateAmortFlagActiveByAnnexNumber(annexNumber, constant.FLAG_NOT_ACTIVE)
    const rsLoanAcc = await loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, { debtAckContractNumber })
    const loanAccountObj = rsLoanAcc.rows[0]
    // thuc hien load bill neu cancel vao ngay ondue
    const plCreate = {
      debtAckContractNumber: pl.debtAckContractNumber,
      onDueDate: pl.scanDate
    }
    const rsBillOnDue = await billOnDueRepo.getBillByContractAndOnDueDate(global.poolWrite, plCreate)

    let toCollect = 0
    const result = {
      statusCode: 200,
      code: 0,
      data: {
        annexNumber: annexNumber || ''
      }
    }
    if (rsBillOnDue.rowCount > 0) {
      result.message = '[LMS-MC] Thuc hien cancel annex thanh cong. Bill cua ngay da duoc tao'
      return result
    }
    const plGetIns = {
      debtAckContractNumber: plCreate.debtAckContractNumber,
      contractNumber: plCreate.contractNumber,
      endDate: plCreate.onDueDate
    }

    let rsInsNotClosed
    if (common.isFactoringLoanChannel(loanAccountObj?.partner_code)) {
      rsInsNotClosed = await installmentRepo.getInsIsNotClosedFactoring(global.poolWrite, plGetIns)
    } else {
      rsInsNotClosed = await installmentRepo.getInsIsNotClosed(global.poolWrite, plGetIns)
    }
    if (rsInsNotClosed.rowCount == 0) {
      result.message = '[LMS-MC] Thuc hien cancel annex thanh cong.'
      return result
    }
    const listRecords = []
    const listIds = []
    let startDate, endDate, dueDate
    for (const obj of rsInsNotClosed.rows) {
      toCollect += Number(obj.remain_amount)
      startDate = common.convertDatetoString(obj.start_date, 'yyyy-mm-dd')
      endDate = common.convertDatetoString(obj.end_date, 'yyyy-mm-dd')
      dueDate = common.convertDatetoString(obj.due_date, 'yyyy-mm-dd')
      listIds.push(obj.id)

      let acceptPaymentDate = null
      let invoiced_date = null
      if (common.isFactoringLoanChannel(loanAccountObj?.partner_code) && obj.type === constant.INSTALLMENT.TYPE.PRIN) {
        acceptPaymentDate = common.formatDate({ date: plCreate.onDueDate })
        invoiced_date = common.formatDate({ date: obj.invoiced_date })
      }

      listRecords.push([
        obj.contract_number,
        obj.debt_ack_contract_number,
        obj.amount,
        obj.remain_amount,
        obj.type,
        obj.num_cycle,
        plCreate.onDueDate,
        obj.payment_status,
        startDate,
        endDate,
        dueDate,
        obj.owner_id || constant.config.ownerId,
        constant.config.createdBy,
        obj.id,
        obj.payment_priority,
        acceptPaymentDate,
        invoiced_date
      ])
    }
    billOnDueRepo.insBatchBillOnDue(listRecords)
    installmentRepo.updateInsClosed(global.poolWrite, listIds)
    // cap nhat to_collect
    loanAccountRepo.updateToCollect({
      toCollect,
      loanId: loanAccountObj.loan_id
    })

    result.message = '[LMS-MC] Thuc hien cancel annex thanh cong. Load bill thanh cong.'
    return result
  } catch (err) {
    console.error('Error at cancelAnnex: ', err.message)
    console.log(err)
    return { statusCode: 500, code: -1, message: err.message }
  }
}
const cancelAnnexDraft = async function (pl) {
  try {
    common.log('req body cancelAnnexDraft: ' + JSON.stringify(pl))
    if (pl == undefined || pl.debtAckContractNumber == undefined || pl.annexNumber == undefined) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Thieu du lieu dau vao'
      }
    }
    pl.isAnnex = 1

    const result = {
      statusCode: 200,
      code: 0,
      data: {
        annexNumber: pl.annexNumber || ''
      }
    }

    const plUpdateAnnex = {
      annexStatusNew: constant.ANNEX.STATUS.CANCEL,
      annexNumber: pl.annexNumber || '',
      annexStatusOld: constant.ANNEX.STATUS.CALCULATE
    }
    await loanAnnexRepo.updateStatusLoanAnnex(global.poolWrite, plUpdateAnnex)

    result.message = '[LMS-MC] Thuc hien cancel annex draft thanh cong. Load bill thanh cong.'
    return result
  } catch (err) {
    console.error('Error at cancelAnnexDraft: ', err.message)
    console.log(err)
    return { statusCode: 500, code: -1, message: err.message }
  }
}
const simulaLoanAnnexMc = async function (req, res) {
  try {
    const payload = req.query
    if (
      payload == undefined ||
      payload.debtAckContractNumber == undefined ||
      payload.effectStartDate == undefined ||
      payload.amountAnnex == undefined
    ) {
      res.status(400).json((res.body = { code: 1, message: '[LMS-MC] Thieu du lieu dau vao' }))
      return
    }
    if (payload.amountAnnex < constant.CALCUCFG.minAnnex) {
      return res.status(400).json((res.body = { code: 1, message: '[LMS-MC] So tien annex qua nho' }))
    }
    const response = await processSimulationAnnex(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (err) {
    console.log(err)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}

const detailLoanAnnexMc = async function (req, res) {
  try {
    const payload = req.query
    if (
        payload == undefined ||
        payload.annexNumber == undefined
    ) {
      res.status(400).json((res.body = { code: 1, message: '[LMS-MC] Thieu du lieu dau vao' }))
      return
    }

    const loanAnnex = await loanAnnexRepo.findLoanAnnexByAnnexNumber(payload.annexNumber)

    let response = {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien thanh cong',
      data: {
        ...loanAnnex,
        annex_status: constant.ANNEX.STATUS.codeToStatus[loanAnnex.annex_status],
      }
    }
    res.status(200).json(response)
  } catch (err) {
    console.log(err)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}

async function simulationLoanAnnexFull({
   debtAckContractNumber,
   effectStartDate,
   createdBy,
   isCreated = false
}) {
  const listAllInsm = await installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
      debtAckContractNumber,
      isNotCompleted: false
    })
  const listInsm = listAllInsm.filter(
      (item) =>
          item.closed == constant.INSTALLMENT.CLOSE.FALSE &&
          moment(item.end_date).format(constant.DATE_FORMAT.YYYYMMDD2) > effectStartDate
  )
  const totalPrinRemain = lodash.sumBy(listInsm, function (insm) {
    if (insm.type == constant.INSTALLMENT.TYPE.PRIN) {
      return Number(insm.remain_amount)
    }
  })
  return simulationLoanAnnexV2 ({
    debtAckContractNumber,
    effectStartDate,
    amountAnnex: totalPrinRemain,
    createdBy,
    isCreated
  })
}

async function simulationLoanAnnexV2({
  debtAckContractNumber,
  effectStartDate,
  amountAnnex,
  createdBy, draftAnnexNumber,
  isCreated = false,
  isDrafted = false,
}) {
  if (!debtAckContractNumber || !effectStartDate || !amountAnnex) {
    return {
      code: 1,
      message: '[LMS-MC] Thieu du lieu dau vao',
      statusCode: 400
    }
  }
  const rsLoanAccount = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)

  if (!rsLoanAccount.length) {
    return {
      code: 1,
      message: '[LMS-MC] Khong tim thay KUNN',
      statusCode: 400
    }
  }
  const loanAccount = rsLoanAccount[0]

  if (
    loanAccount.status == constant.DEBT_ACK_STATUS.TERMINATED ||
    loanAccount.payment_status == constant.PAYMENT_STATUS.DONE
  ) {
    return { code: 1, message: '[LMS-MC] KUNN đã tất toán', statusCode: 400 }
  }
  const dateRunJob = common.formatDate({ date: effectStartDate })
  let checkTerminateFactoring = await repaymentService.checkTerminateFactoring({
    debtAckContractNumber,
    valueDate: dateRunJob
  })

  if (checkTerminateFactoring) {
    let result = await doCreateBillOnDue({ debtAckContractNumber, onDueDate: dateRunJob })
    if (result.code == 0) {
      await repaymentService.doRepayment({
        debtAckContractNumber,
        paymentDate: dateRunJob,
        isTerminateFactoring: true,
      })
    }
  }

  await Promise.all([
    doCalcuIrEveryDay({ debtAckContractNumber, irDate: dateRunJob }),
    doCreateBillOnDue({ debtAckContractNumber, onDueDate: dateRunJob }),
    doCalculatorDpdRiskGrp({
      loanAccObj: loanAccount,
      payload: {
        debtAckContractNumber,
        calDate: dateRunJob
      },
      dpdTriggerType: constant.DPD_TRIGGER_TYPE.CRON_JOB
    })
  ])

  const [
    listBill,
    listBillAnnex,
    listAllInsm,
    listIrCharge,
    listPayment,
    listIr,
    promotion,
    listActivedInstallmentDeduction,
    currentAmort
  ] = await Promise.all([
    billOnDueRepo.getBillOnDuePaymentNotCompleteV3(debtAckContractNumber),
    billOnDueRepo.getBillOnDuePaymentNotCompleteV3(
      debtAckContractNumber,
      'asc',
      constant.BILL_ON_DUE.STATUS.NOT_ACTIVE
    ),
    installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
      debtAckContractNumber,
      isNotCompleted: false
    }),
    irChargeRepo.findByDebtAckContractNumberAndProductCodeV2(debtAckContractNumber),
    paymentRepo.getRemainAmortByContractNumberV2(debtAckContractNumber),
    irRepo.findListIrByDebtAckContractNumber(debtAckContractNumber),
    promotionRepo.findPromotionByDebtAckContractNumber(debtAckContractNumber),
    installmentDeductionRepo.findListInstallmentDeduction(debtAckContractNumber),
    loanAmortRepo.findCurrentAmort(debtAckContractNumber)
  ])
  const listInsm = listAllInsm.filter(
    (item) =>
      item.closed == constant.INSTALLMENT.CLOSE.FALSE &&
      moment(item.end_date).format(constant.DATE_FORMAT.YYYYMMDD2) > effectStartDate
  )
  if (!listInsm.length) {
    return {
      code: 1,
      message: '[LMS-MC] KUNN da duoc thanh toan het hoac da len due ki cuoi',
      statusCode: 200
    }
  }
  const insmIrObj = listInsm.find((item) => item.type == constant.INSTALLMENT.TYPE.INT)
  if (
    insmIrObj &&
    (moment(effectStartDate).toDate() < moment(insmIrObj.start_date).toDate() ||
      moment(effectStartDate).toDate() >= moment(insmIrObj.end_date).toDate())
  ) {
    return {
      code: 2,
      message: '[LMS-MC] Chi co the thuc hien annex tai ki due hien tai',
      statusCode: 400
    }
  }
  const totalNonAllAmt =
    -1 *
    lodash.sumBy(listPayment, function (payment) {
      return Number(payment.non_allocated_amt)
    })
  if (listBillAnnex.length) {
    const checkAnnexed = await loanAnnexRepo.findAnnexInitByDebtAckContractNumber(debtAckContractNumber)
    if (!checkAnnexed.length) {
      return {
        code: 1,
        message: '[LMS-MC] Annex not found',
        statusCode: 400
      }
    }
    const annexObj = checkAnnexed[0]
    if (moment(annexObj.termination_date) < moment(effectStartDate)) {
      cancelAnnex({
        debtAckContractNumber,
        scanDate: moment(annexObj.termination_date).format(constant.DATE_FORMAT.YYYYMMDD2)
      })
    } else {
      return {
        code: 0,
        message: '[LMS-MC] Thuc hien thanh cong',
        statusCode: 200,
        data: {
          debtAckContractNumber,
          activeDate: loanAccount.active_date
            ? moment(loanAccount.active_date).format(constant.DATE_FORMAT.YYYYMMDD2)
            : null,
          annexNumber: annexObj.annex_number,
          terminationDate: annexObj.termination_date,
          totalBillOnDueAmt: +annexObj.total_bill_amt,
          lpiAmt: +annexObj.lpi_amt,
          lpiPrin: +annexObj.lpi_capital_amt,
          lpiInterest: +annexObj.lpi_interest_amt,
          penalitiesAmt: +annexObj.penalities_amt,
          amountAnnex: +annexObj.prin_amt,
          annexType: annexObj.annex_type,
          dueCapital: +annexObj.due_capital_amt,
          dueInterest: +annexObj.due_interest_amt,
          dueFee: +annexObj.due_fee_amt,

          penaltyFeeAmt: +annexObj.penalty_fee_amt,

          onDueCapital: +annexObj.on_due_capital,
          onDueInterest: +annexObj.on_due_interest,
          onDueFee: +annexObj.on_due_fee,
          overDueCapital: +annexObj.over_due_capital,
          overDueInterest: +annexObj.over_due_interest,
          overDueFee: +annexObj.over_due_fee,

          alreadyDue: +annexObj.already_due_amt,
          installmentAdjustment: +annexObj.instal_adjust_amt || 0,
          feeAnnex: +annexObj.cod_fee_amt,
          totalNonAllAmt,
          totalPayment: +annexObj.total_amt + totalNonAllAmt,
          TOTAL_AMOUNT: +annexObj.total_amt,
          penaltyRate: +annexObj.penalty_rate * 100,
          irToDayAnnex: +annexObj.instal_adjust_amt || 0
        }
      }
    }
  }
  const totalPrinRemain = lodash.sumBy(listInsm, function (insm) {
    if (insm.type == constant.INSTALLMENT.TYPE.PRIN) {
      return Number(insm.remain_amount)
    }
  })
  const totalPromotionAmount = promotion?.total_promotion_amount || 0
  const deductionClosedInstallmentAmount = lodash.sumBy(
    listActivedInstallmentDeduction.filter((item) => item.num_cycle < insmIrObj.num_cycle),
    function (insm) {
      return Number(insm.deduction_amount)
    }
  )
  const remainPromotionAmount = totalPromotionAmount - deductionClosedInstallmentAmount

  if (
    helper.isCashLoanPartnerCode(loanAccount.partner_code) &&
    amountAnnex < Number(global.config.data.voucher.minRateTer) * totalPrinRemain
  ) {
    return {
      code: 1,
      message: `[LMS-MC] So tien annex it nhat ${Number(global.config.data.voucher.minRateTer) * 100}% so tien goc con lai cua KUNN `,
      statusCode: 400
    }
  }

  if (global.config.data.termination.minRateTerByPartnerCode) {
    let minRateTerByPartnerCode = JSON.parse(global.config.data.termination.minRateTerByPartnerCode)

    if (minRateTerByPartnerCode[loanAccount.partner_code]) {
      const minAnnexRate = minRateTerByPartnerCode[loanAccount.partner_code]
      if (amountAnnex < minAnnexRate * totalPrinRemain) {
        return {
          code: 1,
          message: `[LMS-MC] So tien annex it nhat ${minAnnexRate * 100}% so tien giai ngan cua KUNN `,
          statusCode: 400
        }
      }
    }
  }

  if (!helper.isCashLoanPartnerCode(loanAccount.partner_code)
  && ![constant.PARTNER_CODE.VUIAPP, constant.PARTNER_CODE.FINV].includes(loanAccount.partner_code)) {
    if (amountAnnex % 1000000 != 0) {
      return {
        code: 1,
        message: '[LMS-MC] So tien annex phai la boi so cua 1000000',
        statusCode: 400
      }
    }
    const minAnnexRate = Number(global.config.data.termination.minRateTer) || constant.CALCUCFG.minAnnexRate
    if (amountAnnex < minAnnexRate * Number(loanAccount.apr_limit_amt)) {
      return {
        code: 1,
        message: `[LMS-MC] So tien annex it nhat ${minAnnexRate * 100}% so tien giai ngan cua KUNN `,
        statusCode: 400
      }
    }
  }

  if (draftAnnexNumber) {
    const draftAnnexed = await loanAnnexRepo.findAnnexByDebtAckContractNumberAndAnnexNumberAndAnnexStatus(
        debtAckContractNumber,
        draftAnnexNumber,
        constant.ANNEX.STATUS.CALCULATE
    )

    if (!draftAnnexed) {
      return {
        code: 1,
        message: `[LMS-MC] Số annex draft ${draftAnnexed} không hợp lệ `,
        statusCode: 400
      }
    }
  }

  const inputData = {
    debtAckContractNumber,
    effectStartDate,
    draftAnnexNumber,
    amountAnnex: Math.min(totalPrinRemain, amountAnnex),
    rawAmountAnnex: amountAnnex,
    createdBy: createdBy || 'system'
  }
  const irChargeObj = listIrCharge.find(
    (item) =>
      item.ir_type == constant.IR_CHARGE_TYPE.ON_DUE_PRIN && item.ir_name != constant.EARLY_TERMINATION_RATE_TYPE
  )
  const payloadData = {
    listBill,
    listAllInsm,
    listInsm,
    listIrCharge,
    listPayment,
    listIr,
    listActivedInstallmentDeduction,
    loanAccount,
    insmIrObj,
    irChargeObj,
    totalPrinRemain,
    numInstallment: insmIrObj.num_cycle,
    remainPromotionAmount,
    promotion,
    currentAmort
  }
  const resultData = {
    lpiAmt: 0,
    dueFee: 0,
    dueInterest: 0,
    dueCapital: 0,
    overDueFee: 0,
    overDueInterest: 0,
    overDueCapital: 0,
    onDueFee: 0,
    onDueInterest: 0,
    onDueCapital: 0,
    lpiPrin: 0,
    lpiInterest: 0,
    currentLpiDue: 0,
    alreadyDue: 0,
    totalAmount: 0,
    totalBillAmt: 0,
    penalitiesAmt: 0,
    penaltyRate: 0,
    codFeeAnnex: 0,
    totalNonAllAmt,
    annexType: '',
    annexNumber: draftAnnexNumber ? draftAnnexNumber : common.convertDatetoString(new Date(), 'yyyymmddhhmmss') + '_' + common.makeId(3),
    listInstallmentNew: [],
    listAllInstallment: [],
    recordsInsAnnex: [],
    recordsBillAnnex: [],
    recordsPromotionDeductionAnnex: [],
    recordsInsmAnnexClosed: [],
    recordsInsmAnnexNotClosed: []
  }

  calculateBillAndSimulationLpi(resultData, payloadData, inputData)

  if (inputData.amountAnnex >= totalPrinRemain) {
    resultData.annexType = constant.ANNEX.TYPE.FULL_EARLY_TERMINATION
  } else {
    resultData.annexType = constant.ANNEX.TYPE.EARLY_TERMINATION
  }

  calculatePenaltyAndCodFee(resultData, payloadData, inputData)

  if (inputData.amountAnnex >= totalPrinRemain) {
    terminationLoanFET(resultData, payloadData, inputData)
  } else {
    if (helper.isCashLoanPartnerCode(loanAccount.partner_code)) {
      terminationLoanVoucherET(resultData, payloadData, inputData)
    } else {
      terminationLoanET(resultData, payloadData, inputData)
    }
  }
  insertDataPenFee(resultData, payloadData, inputData)
  if (isCreated) {
    await insertDataAnnex(resultData, payloadData, inputData)
  } else if (isDrafted) {
    await insertDraftDataAnnex(resultData, payloadData, inputData)
  }
  return {
    code: 0,
    message: '[LMS-MC] Thuc hien thanh cong',
    statusCode: 200,
    data: {
      debtAckContractNumber,
      activeDate: loanAccount.active_date ? common.formatDate({ date: loanAccount.active_date }) : null,
      annexNumber: resultData.annexNumber,
      terminationDate: effectStartDate,
      totalBillOnDueAmt: resultData.totalBillAmt,
      lpiAmt: resultData.lpiAmt,
      lpiPrin: resultData.lpiPrin,
      lpiInterest: resultData.lpiInterest,
      penalitiesAmt: resultData.penalitiesAmt,
      penaltyFeeAmt: resultData.penaltyFeeAmt,
      penaltyRate: common.round(resultData.penaltyRate * 100, 3),
      amountAnnex: inputData.amountAnnex,
      annexType: resultData.annexType,
      dueCapital: resultData.dueCapital,
      dueInterest: resultData.dueInterest,
      dueFee: resultData.dueFee,
      onDueCapital: resultData.onDueCapital,
      onDueInterest: resultData.onDueInterest,
      onDueFee: resultData.onDueFee,
      overDueCapital: resultData.overDueCapital,
      overDueInterest: resultData.overDueInterest,
      overDueFee: resultData.overDueFee,
      alreadyDue: resultData.alreadyDue,
      installmentAdjustment: resultData.installmentAdjustment || 0,
      irToDayAnnex: resultData.installmentAdjustment || 0,
      irFromStartDateToEffectDate: resultData.irFromStartDateToEffectDate || 0,
      feeAnnex: resultData.codFeeAnnex,
      totalNonAllAmt: resultData.totalNonAllAmt,
      totalPayment: resultData.totalAmount + resultData.totalNonAllAmt,
      TOTAL_AMOUNT: resultData.totalAmount,
      numInstallment: insmIrObj.num_cycle,
      newInstallments: resultData.listInstallmentNew,
      listAllInstallment: resultData.listAllInstallment
    }
  }
}

const getAnnexHistory = async function (debtAckContractNumber) {
  try {
    if (!debtAckContractNumber) {
      return {
        code: 1,
        message: 'input invalid',
        statusCode: 400
      }
    }
    const findLoanAcc = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)

    if (!findLoanAcc.length) {
      return {
        code: 1,
        message: 'Debt ack contract number not found',
        statusCode: 400
      }
    }
    const loanAcc = findLoanAcc[0]

    const [annexHistoryList, listPayment] = await Promise.all([
      loanAnnexRepo.getAnnexHistory(global.poolRead, debtAckContractNumber),
      paymentRepo.getRemainAmortByContractNumberV2(debtAckContractNumber)
    ])
    const totalNonAllAmt =
      -1 *
      lodash.sumBy(listPayment, function (o) {
        return Number(o.non_allocated_amt)
      })

    const list = annexHistoryList?.rows.map((item) => {
      const requestStatus = item.account_request_status || loanAcc.status
      let totalPayment = 0
      let nonAllocateAmount = 0
      if ([constant.ANNEX.STATUS.CALCULATE, constant.ANNEX.STATUS.INIT].includes(Number(item.annex_status))) {
        totalPayment = Number(item.total_amt) + totalNonAllAmt
        nonAllocateAmount = totalNonAllAmt
      }
      return {
        annexNumber: item.annex_number,
        annexType: item.annex_type,
        annexStatus: constant.ANNEX.STATUS.codeToStatus[item.annex_status],
        annexAmount: Number(item.prin_amt),
        penaltiesAmount: Number(item.penalities_amt),
        penaltiesRate: Number(item.penalty_rate),
        annexCodFeeAmount: Number(item.cod_fee_amt),
        terminationDate: item.termination_date,
        requestStatus: constant.DEBT_ACK_STATUS.codeToStatus[requestStatus],
        totalAmount: Number(item.total_amt),
        lpiAmount: Number(item.lpi_amt),
        installAdjustment: Number(item.instal_adjust_amt),
        alreadyDue: Number(item.already_due_amt),
        dueFeeAmount: Number(item.due_fee_amt),
        dueInterestAmount: Number(item.due_interest_amt),
        dueCapitalAmount: Number(item.due_capital_amt),
        nonAllocateAmount,
        totalPayment,
        penaltyRate: Number(item.penalty_rate * 100)
      }
    })
    return {
      code: 0,
      statusCode: 200,
      message: '[LMS-MC] Thuc hien thanh cong',
      data: { list }
    }
  } catch (err) {
    console.log(err)
    return { statusCode: 500, code: 99, message: err.message }
  }
}

const getNewAnnexFilter = async function (debtAckContractNumber, date = new Date()) {
  try {
    const rsLoanAccount = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)

    if (!rsLoanAccount.length) {
      return {
        code: 1,
        message: '[LMS-MC] Khong tim thay KUNN',
        statusCode: 400
      }
    }

    const loanAccount = rsLoanAccount[0]

    const listInsm = await installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
      debtAckContractNumber,
      closed: constant.INSTALLMENT.CLOSE.FALSE
    })

    if (!listInsm.length) {
      return {
        code: 1,
        message: '[LMS-MC] KUNN da load het bill. Khong duoc tat toan',
        statusCode: 400
      }
    }

    const insmIrObj = listInsm.find((item) => item.type == constant.INSTALLMENT.TYPE.INT)
    console.log(insmIrObj)
    const startDate = new Date(date) < insmIrObj.start_date ? insmIrObj.start_date : new Date(date)
    const endDate = insmIrObj.end_date
    const listDate = []
    const tempStartDate = startDate
    tempStartDate.setHours(0, 0, 0, 0)
    while (tempStartDate < endDate) {
      listDate.push(moment(tempStartDate).format(constant.DATE_FORMAT.YYYYMMDD2))
      tempStartDate.setDate(tempStartDate.getDate() + 1)
    }
    return {
      code: 0,
      statusCode: 200,
      message: '[LMS-MC] Success',
      data: {
        listDate,
        requestNumber: debtAckContractNumber,
        requestStatus: constant.DEBT_ACK_STATUS.codeToStatus[loanAccount.status]
      }
    }
  } catch (error) {
    return {
      code: 1,
      statusCode: 500,
      message: '[LMS-MC] ERROR'
    }
  }
}
const getPrepaymentFee = async function (req, res) {
  try {
    const inputData = req.query
    const installmentDate = new Date(inputData.installmentDate)
    const loanAccountObj = await loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, inputData)

    if (loanAccountObj == undefined || loanAccountObj.rowCount == 0) {
      return res.status(200).json(
        (res.body = {
          code: 1,
          message: '[MC-LMS] KUNN khong ton tai'
        })
      )
    }

    // Tính số tiền chịu phí trả trước hạn
    let remain_amt
    if (loanAccountObj.rows[0].prin_paid != null) {
      remain_amt = loanAccountObj.rows[0].prin_amt - loanAccountObj.rows[0].prin_paid
    } else {
      remain_amt = loanAccountObj.rows[0].prin_amt
    }

    let amountCharged
    if (inputData.pre_amt > remain_amt) {
      amountCharged = remain_amt
    } else {
      amountCharged = inputData.pre_amt
    }

    // Lấy thông tin về kỳ hạn vay và kỳ trả nợ hiện tại
    const tenor = loanAccountObj.rows[0].tenor
    const installments = await installmentRepo.findByDebtAckContractNumberAndPaymentStatusNotCompleted(inputData)
    if (installments == undefined || installments.rowCount == 0) {
      return res.status(200).json(
        (res.body = {
          code: 1,
          message: '[MC-LMS] Khong ton tai ban ghi trong bang Installment'
        })
      )
    }

    let installmentPeriod
    for (const i in installments.rows) {
      if (installments.rows[i].start_date <= installmentDate && installments.rows[i].end_date >= installmentDate) {
        installmentPeriod = installments.rows[i].ir_num_cycle
        break
      }
    }

    // Lấy thông tin tỉ lệ phí trả nợ trước hạn
    const url = global.config?.basic?.product?.[global.env] + '/product/v1/productInfo/?prdctCode=MCBAS_STANDARD'
    try {
      common.getAPI(url, {}).then((result) => {
        if (result == undefined || result.data == undefined) {
          return res.status(400).json(
            (res.body = {
              code: 2,
              message: '[LMS-MC] Khong lay duoc thong tin ti le phi tra no truoc han'
            })
          )
        }

        let rateValue
        for (const i in result.data.rate) {
          if (
            result.data.rate[i].rateType == 'EARLY_TERMINATION_RATE' &&
            result.data.rate[i].tenorFrom <= tenor &&
            result.data.rate[i].tenorTo >= tenor &&
            result.data.rate[i].installmentFrom <= installmentPeriod &&
            result.data.rate[i].installmentTo >= installmentPeriod
          ) {
            rateValue = result.data.rate[i].intRateVal
            break
          }
        }

        res.status(200).json(
          (res.body = {
            code: 0,
            message: '[LMS-MC] Thuc hien thanh cong',
            data: {
              rateValue,
              prepaymentFee: (amountCharged * rateValue) / 100
            }
          })
        )
      })
    } catch (error) {
      console.log(error)
      res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
    }
  } catch (error) {
    console.log(error)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}

const terminationAnnex = async function (req, res) {
  try {
    const pl = req.query
    if (!pl.debtAckContractNumber || !pl.effectStartDate || !pl.amountAnnex) {
      return res.status(400).json((res.body = { code: 1, message: '[LMS-MC] Thieu du lieu dau vao' }))
    }
    const loanAcc = await LoanAccountRepository.findOne({
      where: { debt_ack_contract_number: pl.debtAckContractNumber }
    })
    if (!loanAcc) {
      return res.status(200).json(
        (res.body = {
          code: 2,
          message: '[LMS-MC] debtAckContractNumber not found'
        })
      )
    }
    if (pl.annexId) {
      const annexObj = await LoanAnnexRepository.findOne({ where: { annex_id: pl.annexId } })
      if (!annexObj) return res.status(200).json({ statusCode: 200, code: 1, message: '[LMS-MC] AnnexId not found' })
      const responseData = {
        outStandingAmt: Number(annexObj.prin_amt),
        totalPenaties: Number(annexObj.penalities_amt),
        priAmtOverDueCol: Number(annexObj.due_capital_amt),
        intAmtOverDueCol: Number(annexObj.due_interest_amt),
        feeAmtOverDueCol: Number(annexObj.due_fee_amt),
        priAmtNextDueCol: 0,
        intAmtNextDueCol: Number(annexObj.instal_adjust_amt),
        feeAmtNextDueCol: Number(annexObj.cod_fee_amt),
        lpiAmt: Number(annexObj.lpi_amt),
        nonAllocateAmt: 0,
        total: Number(annexObj.total_amt),
        total_debt_amt: 0,
        totalDebtAmt: 0,
        penaltyRate: Number(annexObj.penalty_rate)
      }
      if (annexObj.annex_status == constant.ANNEX.STATUS.INIT) {
        responseData.total_debt_amt = responseData.total - loanAcc.non_allocation_amt
        responseData.totalDebtAmt = responseData.total - loanAcc.non_allocation_amt
        responseData.nonAllocateAmt = loanAcc.non_allocation_amt
      }
      return res.status(200).json({
        statusCode: 200,
        code: 0,
        message: '[LMS-MC] Thuc hien thanh cong',
        data: responseData
      })
    }

    const payload = {
      debtAckContractNumber: req.query.debtAckContractNumber,
      effectStartDate: req.query.effectStartDate,
      amountAnnex: req.query.amountAnnex
    }
    console.log('MC-TerminationAnnex: ' + JSON.stringify(payload))
    const result = await simulationLoanAnnexV2(payload)
    const data = result.data
    const mappingData = {
      outStandingAmt: data?.amountAnnex || 0,
      totalPenaties: data?.penalitiesAmt || 0,
      priAmtOverDueCol: data?.dueCapital || 0,
      intAmtOverDueCol: data?.dueInterest || 0,
      feeAmtOverDueCol: data?.dueFee || 0,
      priAmtNextDueCol: 0,
      intAmtNextDueCol: data?.installmentAdjustment || 0,
      feeAmtNextDueCol: data?.feeAnnex || 0,
      lpiAmt: data?.lpiAmt || 0,
      nonAllocateAmt: data?.totalNonAllAmt || 0,
      total: data?.TOTAL_AMOUNT,
      total_debt_amt: data?.totalPayment || 0,
      totalDebtAmt: data?.totalPayment || 0,
      penaltyRate: data?.penaltyRate || 0
    }
    return res.status(200).json({
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien thanh cong',
      data: mappingData
    })
  } catch (error) {
    console.log(error)
    res.status(500).json((res.body = { code: 99, message: error.message }))
  }
}

const processSimulationAnnex = async function (payload) {
  try {
    const listResult = await Promise.all([
      billOnDueRepo.getBillOnDuePaymentNotComplete(global.poolRead, payload),
      installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompleted(global.poolRead, payload),
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, {
        debtAckContractNumber: payload.debtAckContractNumber
      }),
      paymentRepo.getRemainAmortByContractNumber({
        debtAckContractNumber: payload.debtAckContractNumber
      })
    ])
    const rsBill = listResult[0]
    const rsInsm = listResult[1]
    const rsIrCharge = listResult[2]
    const listPayment = listResult[3]
    // tinh tien thua
    let totalNonAllAmt = 0
    for (const payment of listPayment) {
      totalNonAllAmt = totalNonAllAmt + Number(payment.non_allocated_amt)
    }
    // tinh tong so tien con thieu de tao annex
    let totalBillAmt = 0
    let isAnnex = false
    for (const i in rsBill.rows) {
      if (rsBill.rows[i].is_annex != 1) {
        totalBillAmt += Number(rsBill.rows[i].remain_amount)
      } else {
        isAnnex = true
      }
    }
    if (isAnnex) {
      return {
        statusCode: 200,
        code: 1,
        message: '[LMS-MC] Khe uoc da duoc khoi tao annex'
      }
    }
    if (rsInsm.rowCount == 0) {
      return {
        statusCode: 200,
        code: 1,
        message: '[LMS-MC] KUNN da duoc thanh toan het'
      }
    }
    const recordsBillAnnex = []
    let totalPrinRemain = 0
    let totalCycleRemain = 0
    let irChargeObj = {}
    const annexNumber = common.convertDatetoString(new Date(), 'yyyymmddhhmmss') + '_' + common.makeId(3)
    // lay ir charge de tinh lai theo goc moi
    for (const i in rsIrCharge.rows) {
      if (rsIrCharge.rows[i].ir_type == 1) {
        irChargeObj = rsIrCharge.rows[i]
        break
      }
    }

    const hmCyclePrin = new HashMap()
    for (const i in rsInsm.rows) {
      if (rsInsm.rows[i].type == 1) {
        totalPrinRemain += Number(rsInsm.rows[i].remain_amount)
        totalCycleRemain++
        hmCyclePrin.set(rsInsm.rows[i].num_cycle, rsInsm.rows[i])
      }
    }
    // tinh xem khoan annex co phai nop phi ko?
    let penalitiesAmt = 0
    if (global.calcuCfg.penalitiesRateAnnex > 0) {
      penalitiesAmt = common.roundUp(
        (payload.amountAnnex * global.calcuCfg.penalitiesRateAnnex) / 100,
        global.calcuCfg.scale
      )
      recordsBillAnnex.push([
        null,
        payload.debtAckContractNumber,
        penalitiesAmt,
        penalitiesAmt,
        5,
        null,
        payload.effectStartDate,
        1,
        payload.effectStartDate,
        payload.effectStartDate,
        payload.effectStartDate,
        constant.config.ownerId,
        constant.config.createdBy,
        null,
        1,
        annexNumber
      ])
    }
    // so tien moi ky dc giam khi thanh toan
    if (totalCycleRemain == 0) totalCycleRemain = 1
    const avgPrinEveryCycle = common.roundUp(payload.amountAnnex / totalCycleRemain, global.calcuCfg.scale)
    // let numCyclePrinRemain = 0

    let insmIrObj
    for (const i in rsInsm.rows) {
      if (rsInsm.rows[i].type == constant.INSTALLMENT.TYPE.INT) {
        insmIrObj = rsInsm.rows[i]
        break
      }
    }
    if (moment(payload.effectStartDate).toDate() > moment(insmIrObj.end_date).toDate()) {
      return {
        statusCode: 400,
        code: 2,
        message: '[LMS-MC] Chi co the thuc hien annex den ngay due gan nhat'
      }
    }
    const listInstallmentNew = []
    if (payload.amountAnnex >= totalPrinRemain) {
      listInstallmentNew.push({
        contractNumber: insmIrObj.contract_number,
        debtAckContractNumber: insmIrObj.debt_ack_contract_number,
        numCycle: insmIrObj.num_cycle,
        amount: totalPrinRemain,
        remainAmount: totalPrinRemain,
        type: 1,
        startDate: payload.effectStartDate,
        endDate: payload.effectStartDate,
        dueDate: payload.effectStartDate
      })
      const effectLongTime = new Date(payload.effectStartDate).getTime()
      let irBackDate = 0
      for (const i in rsInsm.rows) {
        const insmObj = rsInsm.rows[i]
        if (insmObj.type == constant.INSTALLMENT.TYPE.INT) {
          let beginDate = insmObj.start_date
          let afterDate = insmObj.end_date

          if (insmObj.is_annex == 1) {
            beginDate = insmObj.ir_from_date
            afterDate = insmObj.ir_to_date
          }
          const strBeginDate = common.convertDatetoString(beginDate, 'yyyy-mm-dd')
          const strAfterDate = common.convertDatetoString(afterDate, 'yyyy-mm-dd')
          if (new Date(strAfterDate).getTime() < effectLongTime) {
            irBackDate += Number(insmObj.remain_amount)
            listInstallmentNew.push({
              contractNumber: insmObj.contract_number,
              debtAckContractNumber: insmObj.debt_ack_contract_number,
              numCycle: insmObj.num_cycle,
              amount: Number(insmObj.amount),
              remainAmount: Number(insmObj.remain_amount),
              type: insmObj.type,
              startDate: common.convertDatetoString(insmObj.start_date, 'yyyy-mm-dd'),
              endDate: common.convertDatetoString(insmObj.end_date, 'yyyy-mm-dd'),
              dueDate: common.convertDatetoString(insmObj.due_date, 'yyyy-mm-dd'),
              irNumCycle: insmObj.ir_num_cycle,
              irFromDate: common.convertDatetoString(insmObj.ir_from_date, 'yyyy-mm-dd'),
              irToDate: common.convertDatetoString(insmObj.ir_to_date, 'yyyy-mm-dd')
            })
          } else if (
            effectLongTime > new Date(strBeginDate).getTime() &&
            effectLongTime < new Date(strAfterDate).getTime()
          ) {
            insmIrObj = rsInsm.rows[i]
          }
        }
      }

      let beginDate = insmIrObj.start_date

      if (insmIrObj.is_annex == 1) {
        beginDate = insmIrObj.ir_from_date
      }
      const strBeginDate = common.convertDatetoString(beginDate, 'yyyy-mm-dd')
      const numDayToEffect = common.dateDiff(new Date(payload.effectStartDate), new Date(strBeginDate)).days()
      const irFromStartDateToEffectDate = common.roundUp(
        (totalPrinRemain / global.calcuCfg.totalDayOfYear) * numDayToEffect * irChargeObj.ir_value,
        global.calcuCfg.scale
      )
      listInstallmentNew.push({
        contractNumber: insmIrObj.contract_number,
        debtAckContractNumber: insmIrObj.debt_ack_contract_number,
        numCycle: insmIrObj.num_cycle,
        amount: irFromStartDateToEffectDate,
        remainAmount: irFromStartDateToEffectDate,
        type: insmIrObj.type,
        startDate: common.convertDatetoString(insmIrObj.start_date, 'yyyy-mm-dd'),
        endDate: payload.effectStartDate,
        dueDate: payload.effectStartDate,
        // irNumCycle: insmIrObj.ir_num_cycle,
        irFromDate: common.convertDatetoString(beginDate, 'yyyy-mm-dd'),
        irToDate: payload.effectStartDate
      })
      listInstallmentNew.push({
        contractNumber: insmIrObj.contract_number,
        debtAckContractNumber: insmIrObj.debt_ack_contract_number,
        numCycle: insmIrObj.num_cycle,
        amount: global.calcuCfg.defaultFee,
        remainAmount: global.calcuCfg.defaultFee,
        type: 5,
        startDate: payload.effectStartDate,
        endDate: payload.effectStartDate,
        dueDate: payload.effectStartDate
      })
      return {
        statusCode: 200,
        code: 0,
        message: '[LMS-MC] Thuc hien thanh cong',
        data: {
          totalBillAmt,
          penalitiesAmt,
          amountAnnex: totalPrinRemain,
          irToDayAnnex: irFromStartDateToEffectDate + irBackDate,
          feeAnnex: global.calcuCfg.defaultFee,
          totalNonAllAmt: -1 * totalNonAllAmt,
          TOTAL_AMOUNT:
            totalBillAmt +
            penalitiesAmt +
            totalPrinRemain +
            irBackDate +
            irFromStartDateToEffectDate +
            global.calcuCfg.defaultFee -
            totalNonAllAmt,
          newInstallments: listInstallmentNew
        }
      }
    }
    for (const i in rsInsm.rows) {
      const insmObj = rsInsm.rows[i]
      const startDate = common.convertDatetoString(insmObj.start_date, 'yyyy-mm-dd')
      const endDate = common.convertDatetoString(insmObj.end_date, 'yyyy-mm-dd')
      const dueDate = common.convertDatetoString(insmObj.due_date, 'yyyy-mm-dd')
      if (insmObj.type == constant.INSTALLMENT.TYPE.PRIN) {
        listInstallmentNew.push({
          contractNumber: insmObj.contract_number,
          debtAckContractNumber: insmObj.debt_ack_contract_number,
          numCycle: insmObj.num_cycle,
          amount: insmObj.amount - avgPrinEveryCycle,
          remainAmount: insmObj.remain_amount - avgPrinEveryCycle,
          type: insmObj.type,
          startDate,
          endDate,
          dueDate,
          irNumCycle: insmObj.ir_num_cycle
        })
      } else if (insmObj.type == constant.INSTALLMENT.TYPE.INT) {
        const effectLongTime = new Date(payload.effectStartDate).getTime()

        let beginDate = insmObj.start_date
        let afterDate = insmObj.end_date

        if (insmObj.is_annex == constant.INSTALLMENT.IS_ANNEX.TRUE) {
          beginDate = insmObj.ir_from_date
          afterDate = insmObj.ir_to_date
        }
        const strBeginDate = common.convertDatetoString(beginDate, 'yyyy-mm-dd')
        const strAfterDate = common.convertDatetoString(afterDate, 'yyyy-mm-dd')
        const numDayInCycle = common.dateDiff(afterDate, beginDate).days()
        if (afterDate.getTime() <= effectLongTime) {
          listInstallmentNew.push({
            contractNumber: insmObj.contract_number,
            debtAckContractNumber: insmObj.debt_ack_contract_number,
            numCycle: insmObj.num_cycle,
            amount: insmObj.amount,
            remainAmount: insmObj.remain_amount,
            type: insmObj.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: insmObj.ir_num_cycle,
            irFromDate: common.convertDatetoString(insmObj.ir_from_date, 'yyyy-mm-dd'),
            irToDate: common.convertDatetoString(insmObj.ir_to_date, 'yyyy-mm-dd')
          })
          // to do nothing
        } else if (effectLongTime > beginDate.getTime() && effectLongTime < afterDate.getTime()) {
          // console.log()
          const numDayToEffect = common.dateDiff(new Date(payload.effectStartDate), new Date(strBeginDate)).days()
          const irFromStartDateToEffectDate = common.roundUp(
            (insmObj.remain_amount * numDayToEffect) / numDayInCycle,
            global.calcuCfg.scale
          )
          listInstallmentNew.push({
            contractNumber: insmObj.contract_number,
            debtAckContractNumber: insmObj.debt_ack_contract_number,
            numCycle: insmObj.num_cycle,
            amount: irFromStartDateToEffectDate,
            remainAmount: irFromStartDateToEffectDate,
            type: insmObj.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: insmObj.ir_num_cycle,
            irFromDate: strBeginDate,
            irToDate: payload.effectStartDate
          })
          const irFromEffectDateToEndDate = common.roundUp(
            ((totalPrinRemain - totalCycleRemain * avgPrinEveryCycle) / global.calcuCfg.totalDayOfYear) *
              (numDayInCycle - numDayToEffect) *
              irChargeObj.ir_value,
            global.calcuCfg.scale
          )
          listInstallmentNew.push({
            contractNumber: insmObj.contract_number,
            debtAckContractNumber: insmObj.debt_ack_contract_number,
            numCycle: insmObj.num_cycle,
            amount: irFromEffectDateToEndDate,
            remainAmount: irFromEffectDateToEndDate,
            type: insmObj.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: insmObj.ir_num_cycle,
            irFromDate: payload.effectStartDate,
            irToDate: strAfterDate
          })
        } else {
          // to do nothing
          const irNew = common.roundUp(
            ((totalPrinRemain - totalCycleRemain * avgPrinEveryCycle) / global.calcuCfg.totalDayOfYear) *
              numDayInCycle *
              irChargeObj.ir_value,
            global.calcuCfg.scale
          )
          listInstallmentNew.push({
            contractNumber: insmObj.contract_number,
            debtAckContractNumber: insmObj.debt_ack_contract_number,
            numCycle: insmObj.num_cycle,
            amount: irNew,
            remainAmount: irNew,
            type: insmObj.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: insmObj.ir_num_cycle,
            irFromDate: startDate,
            irToDate: endDate
          })
        }
      } else if (insmObj.type == constant.INSTALLMENT.TYPE.FEE) {
        listInstallmentNew.push({
          contractNumber: insmObj.contract_number,
          debtAckContractNumber: insmObj.debt_ack_contract_number,
          numCycle: insmObj.num_cycle,
          amount: Number(insmObj.amount),
          remainAmount: Number(insmObj.remain_amount),
          type: insmObj.type,
          startDate,
          endDate,
          dueDate,
          irNumCycle: insmObj.ir_num_cycle,
          irFromDate: startDate,
          irToDate: endDate
        })
      }
    }
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien thanh cong',
      data: {
        totalBillAmt,
        penalitiesAmt,
        amountAnnex: Number(payload.amountAnnex),
        feeAnnex: global.calcuCfg.defaultFee,
        totalNonAllAmt: -1 * totalNonAllAmt,
        TOTAL_AMOUNT:
          totalBillAmt + penalitiesAmt + Number(payload.amountAnnex) + global.calcuCfg.defaultFee - totalNonAllAmt,
        newInstallments: listInstallmentNew
      }
    }
  } catch (e) {
    console.log(e)
    return { statusCode: 500, code: -1, message: e.message }
  }
}

function calculateBillAndSimulationLpi(resultData, payloadData, inputData) {
  const { listIrCharge, listBill, listAllInsm, listIr, loanAccount } = payloadData
  const { effectStartDate } = inputData

  const irRatePrinOverdue = listIrCharge.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_PRIN)
  const irRateIrOverdue = listIrCharge.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_INTEREST)
  const lastLpiData = {}
  const lpiPrinLast = {}
  const lpiInterestLast = {}
  for (const bill of listBill) {
    const remainAmount = Number(bill.remain_amount)
    if ([constant.BILL_ON_DUE.TYPE.LPI_INT, constant.BILL_ON_DUE.TYPE.LPI_PRIN].includes(bill.type)) {
      const findInsmObj = listAllInsm.find((item) => item.id == bill.installment_id)
      const findInsmLpi = lodash.maxBy(
        listAllInsm.filter((item) => item.ir_on_prin == findInsmObj.ir_on_prin),
        function (insm) {
          return insm.ir_to_date
        }
      )
      if (findInsmLpi.id == bill.installment_id) {
        lastLpiData[findInsmLpi.ir_on_prin] = Number(findInsmLpi.origin_amt)
        bill.type == constant.BILL_ON_DUE.TYPE.LPI_PRIN &&
          (lpiPrinLast[findInsmLpi.ir_on_prin] = Number(findInsmLpi.origin_amt))
        bill.type == constant.BILL_ON_DUE.TYPE.LPI_INT &&
          (lpiInterestLast[findInsmLpi.ir_on_prin] = Number(findInsmLpi.origin_amt))
        continue
      }
      if (bill.type == constant.BILL_ON_DUE.TYPE.LPI_PRIN) {
        resultData.lpiPrin += remainAmount
        resultData.currentLpiDue += remainAmount
      } else if (bill.type == constant.BILL_ON_DUE.TYPE.LPI_INT) {
        resultData.lpiInterest += remainAmount
        resultData.currentLpiDue += remainAmount
      }
    } else {
      resultData.totalBillAmt += remainAmount

      let startDateCalLpi = new Date(bill.due_date)
      if (bill.due_date > bill.on_due_date) {
        startDateCalLpi.setDate(startDateCalLpi.getDate() - 1)
      }
      const findIr = listIr.find((item) => item.installment_id == bill.installment_id)
      if (findIr) {
        startDateCalLpi = new Date(findIr.ir_date)
      }
      let startDateCalLpiString = common.convertDatetoString(startDateCalLpi, 'yyyy-mm-dd')

      if (bill.type == constant.BILL_ON_DUE.TYPE.FEE) {
        resultData.dueFee += remainAmount
        resultData.overDueFee += remainAmount
      } else {
        const numDayCalLpi = common.getDifferencesDays(startDateCalLpi, effectStartDate)
        const newIrPrinAmtExpire =
          (irRatePrinOverdue.ir_value / constant.CALCUCFG.totalDayOfYear) * remainAmount * numDayCalLpi
        const newIrInterestAmtExpire =
          (irRateIrOverdue.ir_value / constant.CALCUCFG.totalDayOfYear) * remainAmount * numDayCalLpi

        if (bill.type == constant.BILL_ON_DUE.TYPE.PRIN) {
          resultData.dueCapital += remainAmount
          resultData.overDueCapital += remainAmount

          if (new Date(effectStartDate) > new Date(startDateCalLpiString)) {
            lastLpiData[bill.installment_id] && (lastLpiData[bill.installment_id] += newIrPrinAmtExpire)
            lastLpiData[bill.installment_id] && (lpiPrinLast[bill.installment_id] += newIrPrinAmtExpire)
            !lastLpiData[bill.installment_id] && (resultData.lpiPrin += newIrPrinAmtExpire)
          }
        } else if (bill.type == constant.BILL_ON_DUE.TYPE.INT) {
          resultData.dueInterest += remainAmount
          resultData.overDueInterest += remainAmount

          if (new Date(effectStartDate) > new Date(startDateCalLpiString)) {
            lastLpiData[bill.installment_id] && (lastLpiData[bill.installment_id] += newIrInterestAmtExpire)
            lastLpiData[bill.installment_id] && (lpiInterestLast[bill.installment_id] += newIrInterestAmtExpire)
            !lastLpiData[bill.installment_id] && (resultData.lpiInterest += newIrInterestAmtExpire)
          }
        }
      }
    }
  }
  resultData.lpiPrin = common.roundUp(resultData.lpiPrin, constant.CALCUCFG.scale, loanAccount.partner_code)
  resultData.lpiInterest = common.roundUp(resultData.lpiInterest, constant.CALCUCFG.scale, loanAccount.partner_code)
  resultData.lpiAmt = resultData.lpiPrin + resultData.lpiInterest
  for (const lpiVal of Object.values(lastLpiData)) {
    resultData.lpiAmt += common.roundUp(lpiVal, constant.CALCUCFG.scale, loanAccount.partner_code)
  }
  // add lpi last cycle
  for (const lpiPrinVal of Object.values(lpiPrinLast)) {
    resultData.lpiPrin += common.roundUp(lpiPrinVal, constant.CALCUCFG.scale, loanAccount.partner_code)
  }
  for (const lpiIntVal of Object.values(lpiInterestLast)) {
    resultData.lpiInterest += common.roundUp(lpiIntVal, constant.CALCUCFG.scale, loanAccount.partner_code)
  }
  resultData.alreadyDue = resultData.dueCapital + resultData.dueInterest + resultData.dueFee
  resultData.totalBillAmt += resultData.lpiAmt
}
function calculatePenaltyAndCodFee(resultData, payloadData, inputData) {
  const { loanAccount, insmIrObj, listIrCharge, numInstallment, irChargeObj, listInsm } = payloadData
  const { effectStartDate, amountAnnex } = inputData
  const listPenaltiesRate = listIrCharge.filter((item) => item.ir_name == constant.EARLY_TERMINATION_RATE_TYPE)

  if (listPenaltiesRate.length) {
    resultData.penaltyRate = helper.getPenaltiesRateByDate(numInstallment, loanAccount, listPenaltiesRate)
  } else if (helper.checkHaveEarlyTerminationFee(loanAccount, numInstallment)) {
    resultData.penaltyRate =
      (common.getDifferencesDays(effectStartDate, insmIrObj.end_date) * irChargeObj.ir_value) /
      constant.CALCUCFG.totalDayOfYear
    // bo sung them phi phat ky dau cua san pham evc neu co
    const installmentList = listInsm.filter((item) => item.num_cycle == 1 && item.type == constant.INSTALLMENT.TYPE.FEE)
    resultData.penaltyFeeAmt = installmentList.reduce(
      (accumulator, currentValue) => accumulator + Number(currentValue.amount || 0),
      0
    )
    if (loanAccount.contract_type == constant.CONTRACT_TYPE.CREDITLINE) {
      resultData.penaltyRate =
        ((30 - common.getDifferencesDays(insmIrObj.start_date, effectStartDate)) * irChargeObj.ir_value) /
        constant.CALCUCFG.totalDayOfYear
    }
  }

  if (resultData.penaltyRate) {
    resultData.penalitiesAmt = common.roundV2(amountAnnex * resultData.penaltyRate, constant.CALCUCFG.scale)
  }

  // phí phạt với DNSE
  if (loanAccount.partner_code in global.listETFeeConfig) {
    let deltaDay = common.getDifferencesDays(loanAccount.start_date, effectStartDate)
    if (deltaDay in global.listETFeeConfig[loanAccount.partner_code]) {
      resultData.penalitiesAmt = common.roundV2(global.listETFeeConfig[loanAccount.partner_code][deltaDay], constant.CALCUCFG.scale)
    }
  }

  if (resultData.annexType === constant.ANNEX.TYPE.FULL_EARLY_TERMINATION) {
    if (global.config.data.haveCodeFeeAnnexOnFet.listPartnerCode.split(',').includes(loanAccount.partner_code)) {
      resultData.codFeeAnnex = constant.CALCUCFG.defaultFee
    }
  }
}

function terminationLoanET(resultData, payloadData, inputData) {
  const { totalPrinRemain, loanAccount, listInsm, irChargeObj, numInstallment } = payloadData
  const { effectStartDate, amountAnnex } = inputData
  const { listInstallmentNew, recordsBillAnnex, recordsInsAnnex, annexNumber } = resultData
  let numCycle
  let sumAmountAnnex = amountAnnex
  for (const installment of listInsm) {
    if (numCycle && numCycle != installment.num_cycle && loanAccount.contract_type == constant.CONTRACT_TYPE.CASHLOAN) {
      break
    }
    if (sumAmountAnnex <= 0) {
      numCycle = installment.num_cycle
    }

    const startDate = common.convertDatetoString(installment.start_date, 'yyyy-mm-dd')
    const endDate = common.convertDatetoString(installment.end_date, 'yyyy-mm-dd')
    const dueDate = common.convertDatetoString(installment.due_date, 'yyyy-mm-dd')
    const cycleDate = common.convertDatetoString(installment.cycle_date, 'yyyy-mm-dd')

    const oldRemainAmount = Number(installment.remain_amount)

    if (installment.type == constant.INSTALLMENT.TYPE.PRIN) {
      const delta = Math.min(sumAmountAnnex, oldRemainAmount)
      const remainPrinAmount = oldRemainAmount - delta
      sumAmountAnnex = sumAmountAnnex - delta

      recordsBillAnnex.push({
        contract_number: installment.contract_number,
        debt_ack_contract_number: installment.debt_ack_contract_number,
        amount: delta,
        remain_amount: delta,
        type: installment.type,
        num_cycle:
          loanAccount.contract_type == constant.CONTRACT_TYPE.CASHLOAN ? installment.num_cycle : numInstallment,
        on_due_date: effectStartDate,
        payment_status: installment.payment_status,
        start_date: effectStartDate,
        end_date: effectStartDate,
        due_date: effectStartDate,
        owner_id: loanAccount.owner_id || constant.config.ownerId,
        created_by: constant.config.createdBy,
        installment_id: installment.id,
        is_annex: constant.INSTALLMENT.IS_ANNEX.TRUE,
        annex_number: annexNumber,
        status: constant.BILL_ON_DUE.STATUS.NOT_ACTIVE
      })
      listInstallmentNew.push({
        contractNumber: installment.contract_number,
        debtAckContractNumber: installment.debt_ack_contract_number,
        numCycle: installment.num_cycle,
        amount: Number(installment.amount),
        remainAmount: Number(remainPrinAmount),
        type: installment.type,
        startDate,
        endDate,
        dueDate
      })
    }
    if (installment.type == constant.INSTALLMENT.TYPE.INT) {
      const effectLongTime = new Date(effectStartDate).getTime()

      const beginDate = installment.ir_from_date || installment.start_date
      const afterDate = installment.ir_to_date || installment.end_date
      const strBeginDate = common.convertDatetoString(beginDate, 'yyyy-mm-dd')
      const strAfterDate = common.convertDatetoString(afterDate, 'yyyy-mm-dd')
      const numDayInCycle = common.dateDiff(afterDate, beginDate).days()
      const dataInsertInstallment = {
        debt_ack_contract_id: installment.debt_ack_contract_id,
        num_cycle: installment.num_cycle,
        amount: installment.amount,
        remain_amount: installment.remain_amount,
        cycle_date: cycleDate,
        type: installment.type,
        payment_status: installment.payment_status,
        owner_id: loanAccount.owner_id || constant.config.ownerId,
        is_testing: constant.config.isTesting,
        created_by: constant.config.createdBy,
        contract_number: installment.contract_number,
        debt_ack_contract_number: installment.debt_ack_contract_number,
        start_date: startDate,
        end_date: endDate,
        due_date: dueDate,
        ir_from_date: null,
        ir_to_date: null,
        ir_on_prin: null,
        outstanding_prin: null,
        description: null,
        ir_num_cycle: installment.ir_num_cycle,
        is_annex: constant.INSTALLMENT.IS_ANNEX.TRUE,
        status: constant.INSTALLMENT.STATUS.DEACTIVE,
        closed: constant.INSTALLMENT.CLOSE.FALSE,
        ir_rate: installment.ir_rate
      }

      if (afterDate.getTime() <= effectLongTime) {
        recordsInsAnnex.push({
          ...dataInsertInstallment,
          ir_from_date: common.convertDatetoString(installment.ir_from_date, 'yyyy-mm-dd'),
          ir_to_date: common.convertDatetoString(installment.ir_to_date, 'yyyy-mm-dd'),
          ir_on_prin: installment.ir_on_prin,
          outstanding_prin: Number(installment.outstanding_prin) || totalPrinRemain,
          description: installment.description
        })

        listInstallmentNew.push({
          contractNumber: installment.contract_number,
          debtAckContractNumber: installment.debt_ack_contract_number,
          numCycle: installment.num_cycle,
          amount: Number(installment.amount),
          remainAmount: Number(installment.remain_amount),
          outStandingAmount: Number(installment.outstanding_prin) || totalPrinRemain,
          type: installment.type,
          startDate,
          endDate,
          dueDate,
          irNumCycle: installment.ir_num_cycle,
          irFromDate: common.convertDatetoString(installment.ir_from_date, 'yyyy-mm-dd'),
          irToDate: common.convertDatetoString(installment.ir_to_date, 'yyyy-mm-dd')
        })
      } else if (effectLongTime > beginDate.getTime() && effectLongTime < afterDate.getTime()) {
        const numDayToEffectFirst = common.dateDiff(new Date(effectStartDate), new Date(strBeginDate)).days()
        const irFromStartDateToEffectDate = common.roundUp(
          (totalPrinRemain / constant.CALCUCFG.totalDayOfYear) * numDayToEffectFirst * irChargeObj.ir_value,
          constant.CALCUCFG.scale,
          loanAccount.partner_code
        )
        if (irFromStartDateToEffectDate > 0) {
          recordsInsAnnex.push({
            ...dataInsertInstallment,
            amount: irFromStartDateToEffectDate,
            remain_amount: irFromStartDateToEffectDate,
            ir_from_date: strBeginDate,
            ir_to_date: effectStartDate,
            outstanding_prin: Number(installment.outstanding_prin),
            description: installment.description,
            ir_rate: irChargeObj.ir_value
          })
          listInstallmentNew.push({
            contractNumber: installment.contract_number,
            debtAckContractNumber: installment.debt_ack_contract_number,
            numCycle: installment.num_cycle,
            amount: irFromStartDateToEffectDate,
            remainAmount: irFromStartDateToEffectDate,
            outStandingAmount: Number(installment.outstanding_prin) || totalPrinRemain,
            type: installment.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: installment.ir_num_cycle,
            irFromDate: strBeginDate,
            irToDate: effectStartDate
          })
        }
        const numDayToEffectSecond = common.dateDiff(new Date(strAfterDate), new Date(effectStartDate)).days()
        const irFromEffectDateToEndDate = common.roundUp(
          ((totalPrinRemain - amountAnnex) / constant.CALCUCFG.totalDayOfYear) *
            numDayToEffectSecond *
            irChargeObj.ir_value,
          constant.CALCUCFG.scale,
          loanAccount.partner_code
        )
        if (irFromEffectDateToEndDate > 0) {
          recordsInsAnnex.push({
            ...dataInsertInstallment,
            amount: irFromEffectDateToEndDate,
            remain_amount: irFromEffectDateToEndDate,
            ir_from_date: effectStartDate,
            ir_to_date: strAfterDate,
            outstanding_prin: totalPrinRemain - amountAnnex,
            description: installment.description,
            ir_rate: irChargeObj.ir_value
          })
          listInstallmentNew.push({
            contractNumber: installment.contract_number,
            debtAckContractNumber: installment.debt_ack_contract_number,
            numCycle: installment.num_cycle,
            amount: irFromEffectDateToEndDate,
            remainAmount: irFromEffectDateToEndDate,
            outStandingAmount: totalPrinRemain - amountAnnex,
            type: installment.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: installment.ir_num_cycle,
            irFromDate: effectStartDate,
            irToDate: strAfterDate
          })
        }
      } else {
        const irNew = common.roundUp(
          ((totalPrinRemain - amountAnnex) / constant.CALCUCFG.totalDayOfYear) * numDayInCycle * irChargeObj.ir_value,
          constant.CALCUCFG.scale,
          loanAccount.partner_code
        )
        recordsInsAnnex.push({
          ...dataInsertInstallment,
          amount: irNew,
          remain_amount: irNew,
          ir_from_date: startDate,
          ir_to_date: endDate,
          outstanding_prin: totalPrinRemain - amountAnnex,
          description: installment.description
        })
        listInstallmentNew.push({
          contractNumber: installment.contract_number,
          debtAckContractNumber: installment.debt_ack_contract_number,
          numCycle: installment.num_cycle,
          amount: irNew,
          remainAmount: irNew,
          outStandingAmount: totalPrinRemain - amountAnnex,
          type: installment.type,
          startDate,
          endDate,
          dueDate,
          irNumCycle: installment.ir_num_cycle,
          irFromDate: startDate,
          irToDate: endDate
        })
      }
    }
  }
  resultData.totalAmount = resultData.totalBillAmt + Number(amountAnnex) + resultData.codFeeAnnex
}

function terminationLoanFET(resultData, payloadData, inputData) {
  const { totalPrinRemain, loanAccount, listInsm, irChargeObj, insmIrObj, listActivedInstallmentDeduction, promotion } =
    payloadData
  const { effectStartDate } = inputData
  const { listInstallmentNew, recordsInsmAnnexClosed, annexNumber } = resultData
  listInstallmentNew.push({
    contractNumber: loanAccount.contract_number,
    debtAckContractNumber: loanAccount.debt_ack_contract_number,
    numCycle: insmIrObj.num_cycle,
    amount: totalPrinRemain,
    remainAmount: totalPrinRemain,
    type: constant.INSTALLMENT.TYPE.PRIN,
    startDate: common.convertDatetoString(insmIrObj.start_date, 'yyyy-mm-dd'),
    endDate: effectStartDate,
    dueDate: effectStartDate
  })
  const dataInsertInsmPrin = {
    debtAckContractId: insmIrObj.debt_ack_contract_id,
    amount: totalPrinRemain,
    remainAmount: totalPrinRemain,
    type: constant.INSTALLMENT.TYPE.PRIN,
    paymentStatus: constant.PAYMENT_STATUS.INIT,
    ownerId: loanAccount.owner_id || constant.config.ownerId,
    isTesting: constant.config.isTesting,
    createdBy: constant.config.createdBy,
    contractNumber: insmIrObj.contract_number,
    debtAckContractNumber: insmIrObj.debt_ack_contract_number,
    startDate: common.convertDatetoString(insmIrObj.start_date, 'yyyy-mm-dd'),
    endDate: effectStartDate,
    dueDate: effectStartDate,
    isAnnex: constant.INSTALLMENT.IS_ANNEX.TRUE,
    status: constant.INSTALLMENT.STATUS.DEACTIVE,
    closed: constant.INSTALLMENT.CLOSE.TRUE,
    numCycle: insmIrObj.num_cycle,
    cycleDate: insmIrObj.cycle_date,
    ofNumCycle: insmIrObj.of_num_cycle,
    irNumCycle: insmIrObj.ir_num_cycle,
    outstandingPrin: totalPrinRemain,
    description: 'Gốc tất toán toàn phần'
  }
  const dataInsertBillPrin = {
    contract_number: insmIrObj.contract_number,
    debt_ack_contract_number: insmIrObj.debt_ack_contract_number,
    amount: totalPrinRemain,
    remain_amount: totalPrinRemain,
    type: constant.INSTALLMENT.TYPE.PRIN,
    num_cycle: insmIrObj.num_cycle,
    on_due_date: effectStartDate,
    payment_status: constant.PAYMENT_STATUS.INIT,
    start_date: common.convertDatetoString(loanAccount.start_date, 'yyyy-mm-dd'),
    end_date: effectStartDate,
    due_date: effectStartDate,
    owner_id: loanAccount.owner_id || constant.config.ownerId,
    created_by: constant.config.createdBy,
    is_annex: constant.INSTALLMENT.IS_ANNEX.TRUE,
    annex_number: annexNumber
  }
  recordsInsmAnnexClosed.push({
    installment: dataInsertInsmPrin,
    billOnDue: dataInsertBillPrin
  })
  let irBackDate = 0
  let irFromStartDateToEffectDate = 0
  let irFromStartDateToEndDate = 0
  const effectLongTime = new Date(effectStartDate).getTime()
  const listIntInstallment = listInsm.filter((item) => item.type == constant.INSTALLMENT.TYPE.INT)
  for (const installment of listIntInstallment) {
    const beginDate = installment.ir_from_date || installment.start_date
    const afterDate = installment.ir_to_date || installment.end_date
    const strBeginDate = common.convertDatetoString(beginDate, 'yyyy-mm-dd')
    const strAfterDate = common.convertDatetoString(afterDate, 'yyyy-mm-dd')
    if (new Date(strAfterDate).getTime() < effectLongTime) {
      irBackDate += Number(installment.remain_amount)

      listInstallmentNew.push({
        contractNumber: installment.contract_number,
        debtAckContractNumber: installment.debt_ack_contract_number,
        numCycle: installment.num_cycle,
        amount: Number(installment.amount),
        remainAmount: Number(installment.remain_amount),
        type: installment.type,
        startDate: strBeginDate,
        endDate: effectStartDate,
        dueDate: effectStartDate,
        irNumCycle: installment.ir_num_cycle,
        irFromDate: strAfterDate,
        irToDate: strAfterDate
      })
      const dataInsertInsmInt = {
        ...dataInsertInsmPrin,
        amount: Number(installment.amount),
        remainAmount: Number(installment.remain_amount),
        type: constant.INSTALLMENT.TYPE.INT,
        outstandingPrin: Number(installment.outstanding_prin),
        irFromDate: strBeginDate,
        irToDate: strAfterDate,
        description: installment.description,
        irNumCycle: installment.ir_num_cycle,
        irRate: installment.ir_rate
      }
      const dataInsertBillInt = {
        contract_number: installment.contract_number,
        debt_ack_contract_number: installment.debt_ack_contract_number,
        amount: Number(installment.amount),
        remain_amount: Number(installment.remain_amount),
        type: constant.INSTALLMENT.TYPE.INT,
        num_cycle: installment.num_cycle,
        on_due_date: effectStartDate,
        payment_status: constant.PAYMENT_STATUS.INIT,
        start_date: strBeginDate,
        end_date: strAfterDate,
        due_date: effectStartDate,
        owner_id: loanAccount.owner_id || constant.config.ownerId,
        created_by: constant.config.createdBy,
        is_annex: constant.INSTALLMENT.IS_ANNEX.TRUE,
        annex_number: annexNumber
      }
      const objInsmAnnexClosed = {
        installment: dataInsertInsmInt,
        billOnDue: dataInsertBillInt
      }
      const checkPromotionDeduction =
        listActivedInstallmentDeduction.length &&
        listActivedInstallmentDeduction.find((item) => item.installment_id == installment.id)

      if (checkPromotionDeduction) {
        objInsmAnnexClosed.installmentDeduction = {
          promotion_code: checkPromotionDeduction.promotion_code,
          debt_ack_contract_number: checkPromotionDeduction.debt_ack_contract_number,
          original_amount: Number(checkPromotionDeduction.original_amount),
          num_cycle: installment.num_cycle,
          deduction_amount: Number(checkPromotionDeduction.deduction_amount),
          flag_active: constant.FLAG_ACTIVE_INIT
        }
      }
      recordsInsmAnnexClosed.push(objInsmAnnexClosed)
    } else if (effectLongTime > new Date(strBeginDate).getTime() && effectLongTime <= new Date(strAfterDate).getTime()) {
      // sửa từ beginDate => strBeginDate để tránh lỗi phụ thuộc vào múi giờ server và db
      // đổi >= thành > vì khi effectLongTime = strBeginDate => irFromStartDateToEndDate = 0
      const strBeginDate = common.convertDatetoString(beginDate, 'yyyy-mm-dd')
      const numDayToEffect = common.dateDiff(new Date(effectStartDate), new Date(strBeginDate)).days()
      irFromStartDateToEffectDate = common.roundUp(
        (totalPrinRemain / constant.CALCUCFG.totalDayOfYear) * numDayToEffect * irChargeObj.ir_value,
        constant.CALCUCFG.scale
      )
      irFromStartDateToEndDate = Number(installment.remain_amount)
      irFromStartDateToEffectDate = Math.min(irFromStartDateToEffectDate, irFromStartDateToEndDate)
      if (irFromStartDateToEffectDate > 0) {
        const { newInsmAmount, insmAmount, deductionAmount } = handleDeductPromotion(
          payloadData,
          irFromStartDateToEffectDate
        )
        irFromStartDateToEffectDate = newInsmAmount

        listInstallmentNew.push({
          contractNumber: installment.contract_number,
          debtAckContractNumber: installment.debt_ack_contract_number,
          numCycle: installment.num_cycle,
          amount: irFromStartDateToEffectDate,
          remainAmount: irFromStartDateToEffectDate,
          deductionAmount,
          type: installment.type,
          startDate: common.convertDatetoString(installment.start_date, 'yyyy-mm-dd'),
          endDate: effectStartDate,
          dueDate: effectStartDate,
          irFromDate: strBeginDate,
          irToDate: effectStartDate
        })
        const dataInsertInsmIr = {
          ...dataInsertInsmPrin,
          amount: irFromStartDateToEffectDate,
          remainAmount: irFromStartDateToEffectDate,
          numCycle: installment.num_cycle,
          type: installment.type,
          startDate: common.convertDatetoString(installment.start_date, 'yyyy-mm-dd'),
          irFromDate: strBeginDate,
          irToDate: effectStartDate,
          irNumCycle: installment.ir_num_cycle,
          description: 'Lãi tất toán toàn phần'
        }
        const dataInsertBillIr = {
          contract_number: installment.contract_number,
          debt_ack_contract_number: installment.debt_ack_contract_number,
          amount: irFromStartDateToEffectDate,
          remain_amount: irFromStartDateToEffectDate,
          type: constant.INSTALLMENT.TYPE.INT,
          num_cycle: installment.num_cycle,
          on_due_date: effectStartDate,
          payment_status: constant.PAYMENT_STATUS.INIT,
          start_date: common.convertDatetoString(installment.start_date, 'yyyy-mm-dd'),
          end_date: effectStartDate,
          due_date: effectStartDate,
          owner_id: loanAccount.owner_id || constant.config.ownerId,
          created_by: constant.config.createdBy,
          is_annex: constant.INSTALLMENT.IS_ANNEX.TRUE,
          annex_number: annexNumber
        }
        const objInsmAnnexClosed = {
          installment: dataInsertInsmIr,
          billOnDue: dataInsertBillIr
        }
        if (deductionAmount) {
          objInsmAnnexClosed.installmentDeduction = {
            promotion_code: promotion.promotion_code,
            debt_ack_contract_number: loanAccount.debt_ack_contract_number,
            original_amount: insmAmount,
            num_cycle: installment.num_cycle,
            deduction_amount: deductionAmount,
            flag_active: constant.FLAG_ACTIVE_INIT
          }
        }
        recordsInsmAnnexClosed.push(objInsmAnnexClosed)
      }
    }
  }
  resultData.irFromStartDateToEffectDate = irFromStartDateToEffectDate
  resultData.irFromStartDateToEndDate = irFromStartDateToEndDate
  resultData.installmentAdjustment = irFromStartDateToEffectDate + irBackDate
  resultData.onDueInterest = irFromStartDateToEffectDate + irBackDate

  resultData.totalAmount =
    resultData.totalBillAmt + totalPrinRemain + irBackDate + irFromStartDateToEffectDate + resultData.codFeeAnnex
}

async function insertDataAnnex(resultData, payloadData, inputData) {
  const { loanAccount, currentAmort, irChargeObj, numInstallment } = payloadData
  const { effectStartDate, debtAckContractNumber, amountAnnex, createdBy, draftAnnexNumber } = inputData
  const {
    recordsInsmAnnexClosed,
    recordsInsmAnnexNotClosed,
    recordsBillAnnex,
    recordsInsAnnex,
    recordsPromotionDeductionAnnex
  } = resultData

  const payloadInsertAmort = {
    debtAckContractNumber: loanAccount.debt_ack_contract_number,
    amtAmort: Number(loanAccount.apr_limit_amt),
    tenor: resultData.annexType == constant.ANNEX.TYPE.FULL_EARLY_TERMINATION ? numInstallment : loanAccount.tenor,
    startDate: moment(currentAmort?.start_date || loanAccount.start_date).format(constant.DATE_FORMAT.YYYYMMDD2),
    endDate:
      resultData.annexType == constant.ANNEX.TYPE.FULL_EARLY_TERMINATION
        ? effectStartDate
        : moment(currentAmort?.end_date || loanAccount.end_date).format(constant.DATE_FORMAT.YYYYMMDD2),
    intRate: Number(irChargeObj.ir_value),
    prevAmortId: currentAmort?.amort_id,
    annexNumber: resultData.annexNumber
  }
  const amortId = await loanAmortRepo.insertLoanAmort(payloadInsertAmort, constant.FLAG_ACTIVE_INIT)

  await Promise.all(
    recordsInsmAnnexClosed.map(async (record) => {
      record.installment.amortId = amortId
      const installmentRs = await installmentRepo.insertInsmAnnexV2(record.installment)
      if (record.installmentDeduction && installmentRs) {
        record.installmentDeduction.amort_id = amortId
        const promotionDeductionInsert = {
          ...record.installmentDeduction,
          installment_id: installmentRs.id
        }
        recordsPromotionDeductionAnnex.push(promotionDeductionInsert)
      }
      if (record.billOnDue && installmentRs) {
        const billInsert = {
          ...record.billOnDue,
          installment_id: installmentRs.id,
          status: constant.BILL_ON_DUE.STATUS.NOT_ACTIVE
        }
        recordsBillAnnex.push(billInsert)
      }
    })
  )
  await Promise.all(
    recordsInsmAnnexNotClosed.map(async (record) => {
      record.installment.amortId = amortId
      const installmentRs = await installmentRepo.insertInsmAnnexV2(record.installment)
      if (record.installmentDeduction && installmentRs) {
        const promotionDeductionInsert = {
          ...record.installmentDeduction,
          installment_id: installmentRs.id
        }
        recordsPromotionDeductionAnnex.push(promotionDeductionInsert)
      }
    })
  )
  const payloadInsAnnex = {
    annexNumber: resultData.annexNumber,
    effectStartDate,
    terminationDate: effectStartDate,
    prinAmt: amountAnnex,
    penalitiesAmt: resultData.penalitiesAmt,
    lpiAmt: resultData.lpiAmt,
    createdBy: createdBy || constant.config.createdBy,
    ownerId: loanAccount.owner_id || constant.config.ownerId,
    annexStatus: constant.ANNEX.STATUS.INIT,
    debtAckContractNumber,
    annexType: resultData.annexType,
    penaltyRate: resultData.penaltyRate,
    totalAmount: resultData.totalAmount,
    totalBillAmount: resultData.totalBillAmt,
    accountRequestStatus: loanAccount.status,
    installmentAdjustment: resultData.installmentAdjustment,
    alreadyDue: resultData.alreadyDue,
    dueCapital: resultData.dueCapital,
    dueInterest: resultData.dueInterest,
    dueFee: resultData.dueFee,
    totalNonAllAmt: resultData.totalNonAllAmt,
    codFeeAmt: resultData.codFeeAnnex,
    lpiPrin: resultData.lpiPrin,
    lpiInterest: resultData.lpiInterest,
    penaltyFeeAmt: resultData.penaltyFeeAmt,
    onDueCapital: resultData.onDueCapital,
    onDueInterest: resultData.onDueInterest,
    onDueFee: resultData.onDueFee,
    overDueCapital: resultData.overDueCapital,
    overDueInterest: resultData.overDueInterest,
    overDueFee: resultData.overDueFee,
  }

  await Promise.all([
    installmentRepo.insertBatchInsmAnnexV2(recordsInsAnnex),
    billOnDueRepo.insBatchBillAnnexV2(recordsBillAnnex),
    installmentDeductionRepo.insertManyDeductionInstallment(recordsPromotionDeductionAnnex)
  ])

  if (draftAnnexNumber) {
    const draftAnnexed = await loanAnnexRepo.findAnnexByDebtAckContractNumberAndAnnexNumberAndAnnexStatus(
        debtAckContractNumber,
        draftAnnexNumber,
        constant.ANNEX.STATUS.CALCULATE
    )

    if (draftAnnexed) {
      await loanAnnexRepo.updateLoanAnnexByDraftAnnexNumber(draftAnnexNumber, payloadInsAnnex)
    } else {
      await loanAnnexRepo.insertLoanAnnex(payloadInsAnnex)
    }
  } else {
    await loanAnnexRepo.insertLoanAnnex(payloadInsAnnex)
  }

  actionAuditService.saveActionAudit(
    debtAckContractNumber,
    { actionAuditType: 'TERMINATION', actionCodeType: 'ACCEPT_NEW_CLIENT_REQUEST' },
    { title: 'Create annex', createdUser: createdBy }
  )
  backendMobileService.callNotiUpdateAnnexStatus({
    contract_number: debtAckContractNumber,
    annex_status: constant.ANNEX.STATUS_COMMON.INIT,
    termination_date: common.formatDate({ date: effectStartDate }),
    total_payment: resultData.totalAmount + resultData.totalNonAllAmt
  })

  if (helper.isSendSmsPartnerCode(loanAccount.partner_code)) {
    let smsContent = global.config?.data?.sms?.smsAcceptAnnex?.replace('${contractNumber}', loanAccount.debt_ack_contract_number)
        .replace('${totalAnnex}', resultData.totalAmount + resultData.totalNonAllAmt)
        .replace('${terminationDate}', common.formatDate({ date: effectStartDate }))

    smsService.sendSMS({
      phoneNumber: loanAccount.phone_number,
      content: smsContent,
    })
  }

  if (resultData.totalAmount + resultData.totalNonAllAmt <= 0) {
    const valueDate = common.formatDate({
      date: moment(effectStartDate).endOf('day'),
      format: constant.DATE_FORMAT.YYYYMMDD_HHmmss
    })
    repaymentService.doRepayment({
      debtAckContractNumber: loanAccount.debt_ack_contract_number,
      paymentDate: effectStartDate,
      valueDate
    })
  }
}
async function insertDraftDataAnnex(resultData, payloadData, inputData) {
  const { loanAccount } = payloadData
  const { effectStartDate, debtAckContractNumber, amountAnnex, createdBy } = inputData

  const payloadInsAnnex = {
    annexNumber: resultData.annexNumber,
    effectStartDate,
    terminationDate: effectStartDate,
    prinAmt: amountAnnex,
    penalitiesAmt: resultData.penalitiesAmt,
    lpiAmt: resultData.lpiAmt,
    createdBy: createdBy || constant.config.createdBy,
    ownerId: loanAccount.owner_id || constant.config.ownerId,
    annexStatus: constant.ANNEX.STATUS.CALCULATE,
    debtAckContractNumber,
    annexType: resultData.annexType,
    penaltyRate: resultData.penaltyRate,
    totalAmount: resultData.totalAmount,
    totalBillAmount: resultData.totalBillAmt,
    accountRequestStatus: loanAccount.status,
    installmentAdjustment: resultData.installmentAdjustment,
    alreadyDue: resultData.alreadyDue,
    dueCapital: resultData.dueCapital,
    dueInterest: resultData.dueInterest,
    dueFee: resultData.dueFee,
    totalNonAllAmt: resultData.totalNonAllAmt,
    codFeeAmt: resultData.codFeeAnnex,
    lpiPrin: resultData.lpiPrin,
    lpiInterest: resultData.lpiInterest,
    penaltyFeeAmt: resultData.penaltyFeeAmt,
    onDueCapital: resultData.onDueCapital,
    onDueInterest: resultData.onDueInterest,
    onDueFee: resultData.onDueFee,
    overDueCapital: resultData.overDueCapital,
    overDueInterest: resultData.overDueInterest,
    overDueFee: resultData.overDueFee,
  }
  await Promise.all([
    loanAnnexRepo.insertLoanAnnex(payloadInsAnnex),
  ])

  actionAuditService.saveActionAudit(
    debtAckContractNumber,
    { actionAuditType: 'TERMINATION', actionCodeType: 'CALCULATE_NEW_CLIENT_REQUEST' },
    { title: 'Create annex', createdUser: createdBy }
  )
}
function terminationLoanVoucherET(resultData, payloadData, inputData) {
  const {
    totalPrinRemain,
    loanAccount,
    listInsm,
    listAllInsm,
    irChargeObj,
    numInstallment,
    insmIrObj,
    remainPromotionAmount,
    listActivedInstallmentDeduction,
    promotion
  } = payloadData
  const { effectStartDate, amountAnnex } = inputData
  const {
    listInstallmentNew,
    recordsBillAnnex,
    recordsInsmAnnexNotClosed,
    recordsInsmAnnexClosed,
    annexNumber,
    listAllInstallment
  } = resultData
  const maxNumCycle = lodash.maxBy(listInsm, function (insm) {
    return insm.num_cycle
  }).num_cycle
  const remainNumCycle = maxNumCycle - numInstallment + 1
  const remainPrinAfterAnnex = totalPrinRemain - amountAnnex

  listInstallmentNew.push({
    contractNumber: loanAccount.contract_number,
    debtAckContractNumber: loanAccount.debt_ack_contract_number,
    numCycle: insmIrObj.num_cycle - 1,
    amount: amountAnnex,
    remainAmount: amountAnnex,
    type: constant.INSTALLMENT.TYPE.PRIN,
    startDate: common.convertDatetoString(insmIrObj.start_date, 'yyyy-mm-dd'),
    endDate: effectStartDate,
    dueDate: effectStartDate
  })
  const dataInsertInsmPrin = {
    debtAckContractId: insmIrObj.debt_ack_contract_id,
    amount: amountAnnex,
    remainAmount: amountAnnex,
    type: constant.INSTALLMENT.TYPE.PRIN,
    paymentStatus: constant.PAYMENT_STATUS.INIT,
    ownerId: loanAccount.owner_id || constant.config.ownerId,
    isTesting: constant.config.isTesting,
    createdBy: constant.config.createdBy,
    contractNumber: insmIrObj.contract_number,
    debtAckContractNumber: insmIrObj.debt_ack_contract_number,
    startDate: effectStartDate,
    endDate: effectStartDate,
    dueDate: effectStartDate,
    isAnnex: constant.INSTALLMENT.IS_ANNEX.TRUE,
    status: constant.INSTALLMENT.STATUS.DEACTIVE,
    closed: constant.INSTALLMENT.CLOSE.TRUE,
    numCycle: insmIrObj.num_cycle - 1,
    cycleDate: insmIrObj.cycle_date,
    ofNumCycle: insmIrObj.of_num_cycle,
    irNumCycle: insmIrObj.ir_num_cycle,
    outstandingPrin: totalPrinRemain,
    description: 'Gốc tất toán một phần'
  }
  const dataInsertBillPrin = {
    contract_number: insmIrObj.contract_number,
    debt_ack_contract_number: insmIrObj.debt_ack_contract_number,
    amount: amountAnnex,
    remain_amount: amountAnnex,
    type: constant.INSTALLMENT.TYPE.PRIN,
    num_cycle: insmIrObj.num_cycle - 1,
    on_due_date: effectStartDate,
    payment_status: constant.PAYMENT_STATUS.INIT,
    start_date: effectStartDate,
    end_date: effectStartDate,
    due_date: effectStartDate,
    owner_id: loanAccount.owner_id || constant.config.ownerId,
    created_by: constant.config.createdBy,
    is_annex: constant.INSTALLMENT.IS_ANNEX.TRUE,
    annex_number: annexNumber
  }
  recordsInsmAnnexClosed.push({
    installment: dataInsertInsmPrin,
    billOnDue: dataInsertBillPrin
  })
  const { data } = installmentService.getEmiSimulation({
    startDate: insmIrObj.start_date,
    tenor: remainNumCycle,
    interestRate: Number(irChargeObj.ir_value),
    periodicity: Number(loanAccount.periodicity),
    graceDayNumber: Number(loanAccount.grace_day_number),
    aprLimitAmt: remainPrinAfterAnnex,
    promotionAmount: remainPromotionAmount,
    isAnnex: true
  })
  for (let numCycle = 1; numCycle <= loanAccount.tenor; numCycle++) {
    if (numCycle < numInstallment) {
      const listOldInsm = listAllInsm.filter((item) => item.num_cycle == numCycle)

      const oldPeriodObj = {
        startDate: '',
        endDate: '',
        dueDate: '',
        numCycle,
        originalIrAmount: 0,
        deductionAmount: 0,
        irAmount: 0,
        irRate: 0,
        prinAmount: 0,
        sumRemainPrinAmt: 0
      }
      for (const insm of listOldInsm) {
        if (insm.type == constant.INSTALLMENT.TYPE.PRIN) {
          oldPeriodObj.startDate = moment(insm.start_date).format(constant.DATE_FORMAT.YYYYMMDD2)
          oldPeriodObj.endDate = moment(insm.end_date).format(constant.DATE_FORMAT.YYYYMMDD2)
          oldPeriodObj.dueDate = moment(insm.due_date).format(constant.DATE_FORMAT.YYYYMMDD2)
          oldPeriodObj.numDayOfTenor = moment(insm.end_date).diff(moment(insm.start_date), 'day')
          oldPeriodObj.prinAmount += Number(insm.amount)
        } else if (insm.type == constant.INSTALLMENT.TYPE.INT) {
          oldPeriodObj.irAmount += Number(insm.amount)
          oldPeriodObj.irRate = Number(insm.ir_rate)
          oldPeriodObj.sumRemainPrinAmt = Number(insm.outstanding_prin)
          const checkPromotion = listActivedInstallmentDeduction.find((item) => item.installment_id == insm.id)
          !checkPromotion && (oldPeriodObj.originalIrAmount += Number(insm.amount))
          checkPromotion &&
            (oldPeriodObj.originalIrAmount += +checkPromotion.original_amount) &&
            (oldPeriodObj.deductionAmount += +checkPromotion.deduction_amount)
        }
      }
      if (numCycle == numInstallment - 1) {
        oldPeriodObj.prinAmount += amountAnnex
      }
      listAllInstallment.push(oldPeriodObj)
    } else {
      const newPeriodObj = data.listPeriod.find((item) => item.numCycle == numCycle - numInstallment + 1)
      listAllInstallment.push({
        ...newPeriodObj,
        numCycle
      })
    }
  }
  for (const installment of listInsm) {
    const startDate = common.convertDatetoString(installment.start_date, 'yyyy-mm-dd')
    const endDate = common.convertDatetoString(installment.end_date, 'yyyy-mm-dd')
    const dueDate = common.convertDatetoString(installment.due_date, 'yyyy-mm-dd')
    const cycleDate = common.convertDatetoString(installment.cycle_date, 'yyyy-mm-dd')
    const newPeriodObj = data.listPeriod.find((item) => item.numCycle == installment.num_cycle - numInstallment + 1)
    const remainPrinAmount = newPeriodObj?.prinAmount
    if (installment.type == constant.INSTALLMENT.TYPE.PRIN) {
      const objInsmAnnexNotClosed = {
        installment: {
          debt_ack_contract_id: installment.debt_ack_contract_id,
          num_cycle: installment.num_cycle,
          amount: remainPrinAmount,
          remain_amount: remainPrinAmount,
          cycle_date: cycleDate,
          type: installment.type,
          payment_status: installment.payment_status,
          owner_id: loanAccount.owner_id || constant.config.ownerId,
          is_testing: constant.config.isTesting,
          created_by: constant.config.createdBy,
          contract_number: installment.contract_number,
          debt_ack_contract_number: installment.debt_ack_contract_number,
          start_date: startDate,
          end_date: endDate,
          due_date: dueDate,
          ir_from_date: null,
          ir_to_date: null,
          ir_on_prin: null,
          outstanding_prin: null,
          description: null,
          ir_num_cycle: installment.ir_num_cycle,
          is_annex: constant.INSTALLMENT.IS_ANNEX.FALSE,
          status: constant.INSTALLMENT.STATUS.DEACTIVE,
          closed: constant.INSTALLMENT.CLOSE.FALSE,
          ir_rate: installment.ir_rate
        }
      }
      recordsInsmAnnexNotClosed.push(objInsmAnnexNotClosed)
      listInstallmentNew.push({
        contractNumber: installment.contract_number,
        debtAckContractNumber: installment.debt_ack_contract_number,
        numCycle: installment.num_cycle,
        amount: remainPrinAmount,
        remainAmount: remainPrinAmount,
        type: installment.type,
        startDate,
        endDate,
        dueDate
      })
    }
    if (installment.type == constant.INSTALLMENT.TYPE.INT) {
      const checkPromotionDeduction =
        listActivedInstallmentDeduction.length &&
        listActivedInstallmentDeduction.find((item) => item.installment_id == installment.id)
      const effectLongTime = new Date(effectStartDate).getTime()

      const beginDate = installment.ir_from_date || installment.start_date
      const afterDate = installment.ir_to_date || installment.end_date
      const strBeginDate = common.convertDatetoString(beginDate, 'yyyy-mm-dd')
      const strAfterDate = common.convertDatetoString(afterDate, 'yyyy-mm-dd')
      const dataInsertInstallment = {
        debt_ack_contract_id: installment.debt_ack_contract_id,
        num_cycle: installment.num_cycle,
        amount: installment.amount,
        remain_amount: installment.remain_amount,
        cycle_date: cycleDate,
        type: installment.type,
        payment_status: installment.payment_status,
        owner_id: loanAccount.owner_id || constant.config.ownerId,
        is_testing: constant.config.isTesting,
        created_by: constant.config.createdBy,
        contract_number: installment.contract_number,
        debt_ack_contract_number: installment.debt_ack_contract_number,
        start_date: startDate,
        end_date: endDate,
        due_date: dueDate,
        ir_from_date: null,
        ir_to_date: null,
        ir_on_prin: null,
        outstanding_prin: null,
        description: null,
        ir_num_cycle: installment.ir_num_cycle,
        is_annex: constant.INSTALLMENT.IS_ANNEX.TRUE,
        status: constant.INSTALLMENT.STATUS.DEACTIVE,
        closed: constant.INSTALLMENT.CLOSE.FALSE,
        ir_rate: installment.ir_rate
      }

      if (afterDate.getTime() <= effectLongTime) {
        const dataInsertInsm = {
          ...dataInsertInstallment,
          ir_from_date: common.convertDatetoString(installment.ir_from_date, 'yyyy-mm-dd'),
          ir_to_date: common.convertDatetoString(installment.ir_to_date, 'yyyy-mm-dd'),
          ir_on_prin: installment.ir_on_prin,
          outstanding_prin: Number(installment.outstanding_prin) || totalPrinRemain,
          description: installment.description
        }
        // recordsInsAnnex.push({
        //   ...dataInsertInstallment,
        //   ir_from_date: common.convertDatetoString(installment.ir_from_date, 'yyyy-mm-dd'),
        //   ir_to_date: common.convertDatetoString(installment.ir_to_date, 'yyyy-mm-dd'),
        //   ir_on_prin: installment.ir_on_prin,
        //   outstanding_prin: Number(installment.outstanding_prin) || totalPrinRemain,
        //   description: installment.description
        // })

        listInstallmentNew.push({
          contractNumber: installment.contract_number,
          debtAckContractNumber: installment.debt_ack_contract_number,
          numCycle: installment.num_cycle,
          amount: Number(installment.amount),
          remainAmount: Number(installment.remain_amount),
          outStandingAmount: Number(installment.outstanding_prin) || totalPrinRemain,
          type: installment.type,
          startDate,
          endDate,
          dueDate,
          irNumCycle: installment.ir_num_cycle,
          irFromDate: common.convertDatetoString(installment.ir_from_date, 'yyyy-mm-dd'),
          irToDate: common.convertDatetoString(installment.ir_to_date, 'yyyy-mm-dd')
        })
        const objInsmAnnexNotClosed = {
          installment: dataInsertInsm
        }
        if (checkPromotionDeduction) {
          objInsmAnnexNotClosed.installmentDeduction = {
            promotion_code: checkPromotionDeduction.promotion_code,
            debt_ack_contract_number: checkPromotionDeduction.debt_ack_contract_number,
            original_amount: Number(checkPromotionDeduction.original_amount),
            num_cycle: installment.num_cycle,
            deduction_amount: Number(checkPromotionDeduction.deduction_amount),
            flag_active: constant.FLAG_ACTIVE_INIT
          }
        }
        recordsInsmAnnexNotClosed.push(objInsmAnnexNotClosed)
      } else if (effectLongTime > beginDate.getTime() && effectLongTime < afterDate.getTime()) {
        const numDayToEffectFirst = common.dateDiff(new Date(effectStartDate), new Date(strBeginDate)).days()
        let irFromStartDateToEffectDate = common.roundUp(
          (totalPrinRemain / constant.CALCUCFG.totalDayOfYear) * numDayToEffectFirst * irChargeObj.ir_value,
          constant.CALCUCFG.scale,
          loanAccount.partner_code
        )
        if (irFromStartDateToEffectDate > 0) {
          const { newInsmAmount, insmAmount, deductionAmount } = handleDeductPromotion(
            payloadData,
            irFromStartDateToEffectDate
          )
          irFromStartDateToEffectDate = newInsmAmount

          const dataInsertInsm = {
            ...dataInsertInstallment,
            amount: irFromStartDateToEffectDate,
            remain_amount: irFromStartDateToEffectDate,
            ir_from_date: strBeginDate,
            ir_to_date: effectStartDate,
            outstanding_prin: Number(installment.outstanding_prin),
            description: installment.description,
            ir_rate: irChargeObj.ir_value
          }
          // recordsInsAnnex.push({
          //   ...dataInsertInstallment,
          //   amount: irFromStartDateToEffectDate,
          //   remain_amount: irFromStartDateToEffectDate,
          //   ir_from_date: strBeginDate,
          //   ir_to_date: effectStartDate,
          //   outstanding_prin: Number(installment.outstanding_prin),
          //   description: installment.description,
          //   ir_rate: irChargeObj.ir_value
          // })
          listInstallmentNew.push({
            contractNumber: installment.contract_number,
            debtAckContractNumber: installment.debt_ack_contract_number,
            numCycle: installment.num_cycle,
            amount: irFromStartDateToEffectDate,
            remainAmount: irFromStartDateToEffectDate,
            outStandingAmount: Number(installment.outstanding_prin) || totalPrinRemain,
            type: installment.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: installment.ir_num_cycle,
            irFromDate: strBeginDate,
            irToDate: effectStartDate
          })
          const objInsmAnnexNotClosed = {
            installment: dataInsertInsm
          }
          if (deductionAmount) {
            objInsmAnnexNotClosed.installmentDeduction = {
              promotion_code: promotion.promotion_code,
              debt_ack_contract_number: promotion.debt_ack_contract_number,
              original_amount: insmAmount,
              num_cycle: installment.num_cycle,
              deduction_amount: deductionAmount,
              flag_active: constant.FLAG_ACTIVE_INIT
            }
          }
          recordsInsmAnnexNotClosed.push(objInsmAnnexNotClosed)
        }
        const numDayToEffectSecond = common.dateDiff(new Date(strAfterDate), new Date(effectStartDate)).days()
        let irFromEffectDateToEndDate = common.roundUp(
          (remainPrinAfterAnnex / constant.CALCUCFG.totalDayOfYear) * numDayToEffectSecond * irChargeObj.ir_value,
          constant.CALCUCFG.scale,
          loanAccount.partner_code
        )
        if (irFromEffectDateToEndDate > 0) {
          const { newInsmAmount, insmAmount, deductionAmount } = handleDeductPromotion(
            payloadData,
            irFromEffectDateToEndDate
          )
          irFromEffectDateToEndDate = newInsmAmount

          const dataInsertInsm = {
            ...dataInsertInstallment,
            amount: irFromEffectDateToEndDate,
            remain_amount: irFromEffectDateToEndDate,
            ir_from_date: effectStartDate,
            ir_to_date: strAfterDate,
            outstanding_prin: remainPrinAfterAnnex,
            description: installment.description,
            ir_rate: irChargeObj.ir_value
          }
          listInstallmentNew.push({
            contractNumber: installment.contract_number,
            debtAckContractNumber: installment.debt_ack_contract_number,
            numCycle: installment.num_cycle,
            amount: irFromEffectDateToEndDate,
            remainAmount: irFromEffectDateToEndDate,
            outStandingAmount: remainPrinAfterAnnex,
            type: installment.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: installment.ir_num_cycle,
            irFromDate: effectStartDate,
            irToDate: strAfterDate
          })
          const objInsmAnnexNotClosed = {
            installment: dataInsertInsm
          }
          if (deductionAmount) {
            objInsmAnnexNotClosed.installmentDeduction = {
              promotion_code: promotion.promotion_code,
              debt_ack_contract_number: promotion.debt_ack_contract_number,
              original_amount: insmAmount,
              num_cycle: installment.num_cycle,
              deduction_amount: deductionAmount,
              flag_active: constant.FLAG_ACTIVE_INIT
            }
          }
          recordsInsmAnnexNotClosed.push(objInsmAnnexNotClosed)
        }
      } else {
        let irNew = newPeriodObj.originalIrAmount
        if (irNew > 0) {
          const { newInsmAmount, insmAmount, deductionAmount } = handleDeductPromotion(payloadData, irNew)
          irNew = newInsmAmount
          const dataInsertInsm = {
            ...dataInsertInstallment,
            amount: irNew,
            remain_amount: irNew,
            ir_from_date: startDate,
            ir_to_date: endDate,
            outstanding_prin: newPeriodObj.sumRemainPrinAmt,
            description: installment.description
          }
          // recordsInsAnnex.push({
          //   ...dataInsertInstallment,
          //   amount: irNew,
          //   remain_amount: irNew,
          //   ir_from_date: startDate,
          //   ir_to_date: endDate,
          //   outstanding_prin: newPeriodObj.sumRemainPrinAmt,
          //   description: installment.description
          // })
          const objInsmAnnexNotClosed = {
            installment: dataInsertInsm
          }
          if (deductionAmount) {
            objInsmAnnexNotClosed.installmentDeduction = {
              promotion_code: promotion.promotion_code,
              debt_ack_contract_number: promotion.debt_ack_contract_number,
              original_amount: insmAmount,
              num_cycle: installment.num_cycle,
              deduction_amount: deductionAmount,
              flag_active: constant.FLAG_ACTIVE_INIT
            }
          }
          recordsInsmAnnexNotClosed.push(objInsmAnnexNotClosed)
          listInstallmentNew.push({
            contractNumber: installment.contract_number,
            debtAckContractNumber: installment.debt_ack_contract_number,
            numCycle: installment.num_cycle,
            amount: irNew,
            remainAmount: irNew,
            outStandingAmount: newPeriodObj.sumRemainPrinAmt,
            type: installment.type,
            startDate,
            endDate,
            dueDate,
            irNumCycle: installment.ir_num_cycle,
            irFromDate: startDate,
            irToDate: endDate
          })
        }
      }
    }
  }
  resultData.totalAmount = resultData.totalBillAmt + amountAnnex + resultData.codFeeAnnex
}

async function insertDataPenFee(resultData, payloadData, inputData) {
  const { loanAccount, insmIrObj, promotion, listInsm } = payloadData
  const { effectStartDate } = inputData
  const {
    listInstallmentNew,
    annexNumber,
    recordsInsmAnnexClosed,
    irFromStartDateToEndDate,
    irFromStartDateToEffectDate,
    annexType
  } = resultData
  const newInstallment = {
    contractNumber: loanAccount.contract_number,
    debtAckContractNumber: loanAccount.debt_ack_contract_number,
    numCycle: insmIrObj.num_cycle,
    amount: resultData.codFeeAnnex,
    remainAmount: resultData.codFeeAnnex,
    type: constant.INSTALLMENT.TYPE.FEE,
    startDate: effectStartDate,
    endDate: effectStartDate,
    dueDate: effectStartDate
  }
  const dataInsertInsmFee = {
    debtAckContractId: loanAccount.loan_id,
    amount: resultData.codFeeAnnex,
    remainAmount: resultData.codFeeAnnex,
    type: constant.INSTALLMENT.TYPE.FEE,
    paymentStatus: constant.PAYMENT_STATUS.INIT,
    ownerId: loanAccount.owner_id || constant.config.ownerId,
    isTesting: constant.config.isTesting,
    createdBy: constant.config.createdBy,
    contractNumber: loanAccount.contract_number,
    debtAckContractNumber: loanAccount.debt_ack_contract_number,
    startDate: effectStartDate,
    endDate: effectStartDate,
    dueDate: effectStartDate,
    isAnnex: constant.INSTALLMENT.IS_ANNEX.TRUE,
    status: constant.INSTALLMENT.STATUS.DEACTIVE,
    closed: constant.INSTALLMENT.CLOSE.TRUE,
    numCycle: insmIrObj.num_cycle,
    cycleDate: insmIrObj.cycle_date,
    ofNumCycle: insmIrObj.of_num_cycle,
    irNumCycle: insmIrObj.ir_num_cycle,
    description: 'Phí thu hộ khi tất toán'
  }
  const dataInsertBillAnnex = {
    contract_number: insmIrObj.contract_number,
    debt_ack_contract_number: insmIrObj.debt_ack_contract_number,
    amount: resultData.codFeeAnnex,
    remain_amount: resultData.codFeeAnnex,
    type: constant.INSTALLMENT.TYPE.FEE,
    num_cycle: insmIrObj.num_cycle,
    on_due_date: effectStartDate,
    payment_status: constant.PAYMENT_STATUS.INIT,
    start_date: effectStartDate,
    end_date: effectStartDate,
    due_date: effectStartDate,
    owner_id: loanAccount.owner_id || constant.config.ownerId,
    created_by: constant.config.createdBy,
    is_annex: constant.INSTALLMENT.IS_ANNEX.TRUE,
    annex_number: annexNumber
  }
  if (resultData.codFeeAnnex) {
    listInstallmentNew.push(newInstallment)
    recordsInsmAnnexClosed.push({
      installment: {
        ...dataInsertInsmFee,
        amount: resultData.codFeeAnnex,
        remainAmount: resultData.codFeeAnnex
      },
      billOnDue: {
        ...dataInsertBillAnnex,
        amount: resultData.codFeeAnnex,
        remain_amount: resultData.codFeeAnnex
      }
    })
  }
  if (resultData.penalitiesAmt) {
    if (
      irFromStartDateToEndDate &&
      annexType == constant.ANNEX.TYPE.FULL_EARLY_TERMINATION &&
      helper.isCashLoanPartnerCode(loanAccount.partner_code)
    ) {
      resultData.penalitiesAmt = irFromStartDateToEndDate - irFromStartDateToEffectDate
    }
    const { newInsmAmount, insmAmount, deductionAmount } = handleDeductPromotion(payloadData, resultData.penalitiesAmt)
    resultData.penalitiesAmt = newInsmAmount

    listInstallmentNew.push({
      ...newInstallment,
      amount: resultData.penalitiesAmt,
      remainAmount: resultData.penalitiesAmt,
      deductionAmount,
      type: constant.INSTALLMENT.TYPE.FEE
    })
    const dataInsertInsmPenFee = {
      ...dataInsertInsmFee,
      amount: resultData.penalitiesAmt,
      remainAmount: resultData.penalitiesAmt,
      description: 'Phí phạt tất toán sớm',
      irRate: resultData.penaltyRate,
      type: constant.INSTALLMENT.TYPE.FEE
    }
    const dataInsertBillPenFee = {
      ...dataInsertBillAnnex,
      amount: resultData.penalitiesAmt,
      remain_amount: resultData.penalitiesAmt,
      type: constant.INSTALLMENT.TYPE.FEE
    }
    const objInsmAnnexClosed = {
      installment: dataInsertInsmPenFee,
      billOnDue: dataInsertBillPenFee
    }
    if (deductionAmount) {
      objInsmAnnexClosed.installmentDeduction = {
        promotion_code: promotion.promotion_code,
        debt_ack_contract_number: promotion.debt_ack_contract_number,
        original_amount: insmAmount,
        num_cycle: insmIrObj.num_cycle,
        deduction_amount: deductionAmount,
        flag_active: constant.FLAG_ACTIVE_INIT
      }
    }
    recordsInsmAnnexClosed.push(objInsmAnnexClosed)
    resultData.totalAmount += resultData.penalitiesAmt
  }
  if (resultData.penaltyFeeAmt) {
    const listInsmFeeFirstCycle = listInsm.filter((e) => e.num_cycle == 1 && e.type == constant.INSTALLMENT.TYPE.FEE)
    for (const insmCycle of listInsmFeeFirstCycle) {
      listInstallmentNew.push({
        ...newInstallment,
        amount: insmCycle.amount,
        remainAmount: insmCycle.remain_amount,
        type: constant.INSTALLMENT.TYPE.FEE
      })
      const dataInsertInsmPenFee = {
        ...dataInsertInsmFee,
        amount: insmCycle.amount,
        remainAmount: insmCycle.remain_amount,
        description: insmCycle.description,
        irRate: insmCycle.ir_rate,
        type: constant.INSTALLMENT.TYPE.FEE,
        paymentPriority: insmCycle.payment_priority
      }
      const dataInsertBillPenFee = {
        ...dataInsertBillAnnex,
        amount: insmCycle.amount,
        remain_amount: insmCycle.remain_amount,
        type: constant.INSTALLMENT.TYPE.FEE,
        payment_priority: insmCycle.payment_priority
      }
      const objInsmAnnexClosed = {
        installment: dataInsertInsmPenFee,
        billOnDue: dataInsertBillPenFee
      }
      recordsInsmAnnexClosed.push(objInsmAnnexClosed)
    }
    resultData.totalAmount += resultData.penaltyFeeAmt
  }
}
async function handleCalculatedAnnex(loanAccount, totalNonAllocateAmt, paymentDate) {
  const listInstallment = await installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
    debtAckContractNumber: loanAccount.debt_ack_contract_number,
    closed: constant.INSTALLMENT.CLOSE.FALSE,
    isNotCompleted: false
  })
  const checkAnnexedExisted = await loanAnnexRepo.findAnnexInitByDebtAckContractNumber(
    loanAccount.debt_ack_contract_number,
    constant.ANNEX.TYPE.FULL_EARLY_TERMINATION
  )
  const totalAmountAnnex = lodash.sumBy(listInstallment, function (insm) {
    return Number(insm.remain_amount)
  })

  if (
    !listInstallment.length ||
    checkAnnexedExisted.length ||
    loanAccount.tenor > 1 ||
    totalNonAllocateAmt < totalAmountAnnex
  ) {
    return
  }

  const payloadCreateAnnex = {
    debtAckContractNumber: loanAccount.debt_ack_contract_number,
    effectStartDate: moment(paymentDate).format(constant.DATE_FORMAT.YYYYMMDD2),
    amountAnnex: totalAmountAnnex,
    isCreated: true
  }
  await simulationLoanAnnexV2(payloadCreateAnnex)
}

function handleDeductPromotion(payload, insmAmount) {
  let newInsmAmount = insmAmount
  let deductionAmount = 0
  if (payload.remainPromotionAmount && insmAmount) {
    deductionAmount = Math.min(insmAmount, payload.remainPromotionAmount)
    newInsmAmount = newInsmAmount - deductionAmount
    payload.remainPromotionAmount -= deductionAmount
  }
  return { newInsmAmount, insmAmount, deductionAmount }
}

const confirmAnnexPending = async function (payload) {
  try {
    if (!payload || !payload.debtAckContractNumber || !payload.requestId || payload.isConfirm == null) {
      return { statusCode: 400, code: 1, message: 'Missing params' }
    }
    const checkLoanAccount = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(payload.debtAckContractNumber)
    if (!checkLoanAccount.length || checkLoanAccount[0].status != constant.DEBT_ACK_STATUS.ACTIVE) {
      return {
        statusCode: 400,
        code: 1,
        message: 'KUNN not found or KUNN is not active'
      }
    }

    const loanAccount = checkLoanAccount[0]
    if (Number(loanAccount.non_allocation_amt) == 0) {
      return { statusCode: 400, code: 1, message: 'KUNN ko con tien thua' }
    }
    const checkAnnexPendingExist = await loanAnnexPendingRepo.findAnnexPendingByKunnAndRequestId(
      payload.debtAckContractNumber,
      payload.requestId
    )

    if (!checkAnnexPendingExist.length) {
      return { statusCode: 400, code: 1, message: 'Request confirm Not found' }
    }
    const annexPendingObj = checkAnnexPendingExist[0]

    if (annexPendingObj.status != constant.ANNEX_PENDING_STATUS.NOTIFIED) {
      return {
        statusCode: 400,
        code: 1,
        message: 'Request was confirmed or not notified'
      }
    }
    let status = constant.ANNEX_PENDING_STATUS.CANCEL

    if (payload.isConfirm) {
      status = constant.ANNEX_PENDING_STATUS.PENDING
    }
    loanAnnexPendingRepo.updateStatusAnnexPending({
      id: annexPendingObj.id,
      status
    })

    return { statusCode: 200, code: 0, message: 'Success createAnnexPending' }
  } catch (e) {
    console.log(e)
    return { statusCode: 500, code: 99, message: e.message }
  }
}
const activePendingAnnex = async function (payload) {
  try {
    const checkReduntAmt = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(payload.debt_ack_contract_number)
    if (!checkReduntAmt.length || Number(checkReduntAmt[0].non_allocation_amt) < Number(payload.annex_amt)) {
      loanAnnexPendingRepo.updateStatusAnnexPending({
        id: payload.id,
        status: constant.ANNEX_PENDING_STATUS.CANCEL
      })
      return { statusCode: 400, code: 1, message: 'Redunt amt not enough' }
    }
    const payloadCreateAnnex = {
      debtAckContractNumber: payload.debt_ack_contract_number,
      effectStartDate: moment(payload.annex_date).format(constant.DATE_FORMAT.YYYYMMDD2),
      amountAnnex: Number(payload.annex_amt),
      isCreated: true
    }
    await simulationLoanAnnexV2(payloadCreateAnnex)
    loanAnnexPendingRepo.updateStatusAnnexPending({
      id: payload.id,
      status: constant.ANNEX_PENDING_STATUS.ACTIVE
    })
    return { statusCode: 200, code: 0, message: 'Success activePendingAnnex' }
  } catch (e) {
    console.log(e)
    return { statusCode: 500, code: 99, message: e.message }
  }
}
const activePendingAnnexBatchJob = async function (scanDate) {
  try {
    common.log('STARTING activePendingAnnexBatchJob ' + new Date())
    if (!scanDate) {
      scanDate = moment().format(constant.DATE_FORMAT.YYYYMMDD2)
    }
    const listInitAnnex = await loanAnnexPendingRepo.findListAnnexPendingByDate({
      scanDate,
      status: constant.ANNEX_PENDING_STATUS.INIT
    })
    for (const annexInit of listInitAnnex) {
      loanAnnexPendingRepo.updateStatusAnnexPending({
        id: annexInit.id,
        status: constant.ANNEX_PENDING_STATUS.CANCEL
      })
    }
    const listNotifiedAnnex = await loanAnnexPendingRepo.findListAnnexPendingByDate({
      scanDate,
      status: constant.ANNEX_PENDING_STATUS.NOTIFIED
    })
    for (const annexNotifed of listNotifiedAnnex) {
      loanAnnexPendingRepo.updateStatusAnnexPending({
        id: annexNotifed.id,
        status: constant.ANNEX_PENDING_STATUS.CANCEL
      })
    }
    const listPendingAnnex = await loanAnnexPendingRepo.findListAnnexPendingByDate({ scanDate })
    const listChunk = lodash.chunk(listPendingAnnex, 100)
    for (const [index, lst] of listChunk.entries()) {
      common.log(`START STEP ${index + 1} INDEX from ${index * 100} to ${(index + 1) * 100}`)
      await Promise.all(
        lst.map(async (obj) => {
          await activePendingAnnex(obj)
        })
      )
      common.log(`END STEP ${index + 1} INDEX from ${index * 100} to ${(index + 1) * 100}`)
    }
    common.log('END activePendingAnnexBatchJob ' + new Date())

    return {
      statusCode: 200,
      code: 0,
      message: 'Success activePendingAnnexBatchJob'
    }
  } catch (e) {
    console.error('Error at activePendingAnnexBatchJob:', e.message)
    return { statusCode: 500, code: 99, message: e.message }
  }
}

const nofityInitAnnexBatchJob = async function () {
  try {
    common.log('STARTING nofityInitAnnexBatchJob ' + new Date())

    const listInitAnnex = await loanAnnexPendingRepo.findListAnnexPendingByDate({
      status: constant.ANNEX_PENDING_STATUS.INIT
    })

    for (const annexInit of listInitAnnex) {
      const [checkAnnexedExisted, rsLoanAccount] = await Promise.all([
        loanAnnexRepo.findAnnexInitByDebtAckContractNumber(
          annexInit.debt_ack_contract_number,
          constant.ANNEX.TYPE.FULL_EARLY_TERMINATION
        ),
        loanAccountV2Repo.findLoanAccByDebtAckContractNumber(annexInit.debt_ack_contract_number)
      ])
      if (checkAnnexedExisted.length) {
        return
      }
      const loanAccount = rsLoanAccount[0]
      // query loan annex
      const isNotiConfig = global.config.data.voucher.isNoti
      const bodyConfirmAnnex = {
        username: loanAccount.phone_number,
        contractNumber: annexInit.debt_ack_contract_number,
        amount: Number(annexInit.annex_amt),
        nextDue: moment(annexInit.annex_date).format(constant.DATE_FORMAT.YYYYMMDD2),
        requestId: annexInit.request_id
      }
      if (isNotiConfig) {
        loanAnnexPendingRepo.updateStatusAnnexPending({
          id: annexInit.id,
          status: constant.ANNEX_PENDING_STATUS.NOTIFIED
        })
        backendMobileService.callConfirmAnnex(bodyConfirmAnnex)
      }
    }
    return {
      statusCode: 200,
      code: 0,
      message: 'Success nofityInitAnnexBatchJob'
    }
  } catch (e) {
    console.error('Error at nofityInitAnnexBatchJob:', e.message)
    return { statusCode: 500, code: 99, message: e.message }
  }
}
module.exports = {
  createLoanAnnexMc,
  cancelAnnex,
  cancelAnnexDraft,
  detailLoanAnnexMc,
  simulaLoanAnnexMc,
  getAnnexHistory,
  getPrepaymentFee,
  simulationLoanAnnexV2,
  simulationLoanAnnexFull,
  terminationAnnex,
  getNewAnnexFilter,
  handleCalculatedAnnex,
  confirmAnnexPending,
  activePendingAnnexBatchJob,
  activePendingAnnex,
  nofityInitAnnexBatchJob
}
