// const stompit = require('stompit')
// const common = require('../utils/common')

// const sendMessage = (data, queueName) => {
//   const connectOptions = {
//     host: global.config.data?.queue?.host,
//     port: global.config.data?.queue?.port,
//     resetDisconnect: true,
//     connectHeaders: {
//       host: '/',
//       login: global.config.data?.queue?.login,
//       passcode: global.config.data?.queue?.passcode,
//       'heart-beat': global.config.data?.queue?.heart_beat
//     },
//     ssl: true
//   }
//   const connectOptions2 = {
//     host: global.config.data?.queue?.host2,
//     port: global.config.data?.queue?.port,
//     resetDisconnect: true,
//     connectHeaders: {
//       host: '/',
//       login: global.config.data?.queue?.login,
//       passcode: global.config.data?.queue?.passcode,
//       'heart-beat': global.config.data?.queue?.heart_beat
//     },
//     ssl: true
//   }
//   const connectOptionsArr = [connectOptions, connectOptions2]
//   const reconnectOptions = { maxReconnects: 10 }
//   // console.log('servers queue', JSON.stringify(connectOptionsArr))
//   const manager = new stompit.ConnectFailover(connectOptionsArr, reconnectOptions)
//   manager.connect(function (error, client, reconnect) {
//     if (error) {
//       // terminal error, given up reconnecting
//       console.log('Connect Queue error ' + error)
//       return
//     }
//     client.on('error', function (error) {
//       reconnect()
//       console.log(JSON.stringify(error))
//     })
//     const sendHeaders = {
//       destination: '/queue/' + queueName,
//       'content-type': 'text/plain'
//     }
//     const dataQueue = JSON.stringify(data)
//     const replaceEmptyToNull = dataQueue.replaceAll('""', 'null')
//     const frame = client.send(sendHeaders)
//     frame.write(replaceEmptyToNull)
//     console.log('Done send message', queueName, data?.debtAckContractNumber)
//     frame.end()
//     client.disconnect()
//   })
// }

// const sendMessageV2 = (data, queueName) => {
//   try {
//     syncEasyCollection(data, queueName)

//     const client = global.activeMqClient
//     const sendHeaders = {
//       destination: '/queue/' + queueName,
//       'content-type': 'text/plain'
//     }

//     const dataQueue = JSON.stringify(data)
//     const replaceEmptyToNull = dataQueue.replace(/""/g, 'null')

//     const frame = client.send(sendHeaders)
//     frame.write(replaceEmptyToNull, (error) => {
//       if (error) {
//         console.log(`[MC-ACTIVEMQ] Send message queue ${queueName} failed: ${error?.message}`)
//       } else {
//         console.log(`[MC-ACTIVEMQ] Done send message queue ${queueName} with KUNN ${data?.debtAckContractNumber}`)
//       }
//     })
//     frame.end()
//   } catch (e) {
//     console.log(`[MC-ACTIVEMQ] An error has occured while send message queue ${queueName}: ${e?.message}`)
//   }
// }

// const syncEasyCollection = async function (data, queueName) {
//   try {
//     const config = global.config
//     const host = config.basic.easyCollectionV2[global.hostEnv]
//     const url = host + '/easycollection/v2/queue/' + queueName
//     const result = await common.postAPI(url, data, undefined, { 'Content-Type': 'application/json' })
//     console.log(`[LMS-MC] syncEasyCollection | body: ${JSON.stringify(result)} | result: ${JSON.stringify(data)}`)
//   } catch (e) {
//     console.log(`[LMS-MC] syncEasyCollection error: ${e?.message}`)
//   }
// }

// const sendMessageDpdRg = (data, queueName) => {
//   stompit.connect(global.connectMQueue, function (error, client) {
//     if (error) {
//       console.log('connect error ' + error)
//       return
//     }

//     // console.log('connectted')

//     const sendHeaders = {
//       destination: '/queue/' + queueName,
//       'content-type': 'text/plain'
//     }
//     const frame = client.send(sendHeaders)
//     frame.write(JSON.stringify(data))
//     console.log('Done send message', data?.debtAckContractNumber)
//     frame.end()
//     client.disconnect()
//   })
// }

//     const sendHeaders = {
//       destination: '/queue/' + queueName,
//       'content-type': 'text/plain'
//     }
//     const frame = client.send(sendHeaders)
//     frame.write(JSON.stringify(data))
//     console.log('Done send message', data?.debtAckContractNumber)
//     frame.end()
//     client.disconnect()
//   })
// }

// const convertTimeLoanAccountGMT7 = function (data) {
//   data.approval_date = common.formatDateYYYYMMDDHHmmss(data.approval_date)
//   data.signing_in_progress_date = common.formatDateYYYYMMDDHHmmss(data.signing_in_progress_date)
//   data.signature_date = common.formatDateYYYYMMDDHHmmss(data.signature_date)
//   data.activation_date = common.formatDateYYYYMMDDHHmmss(data.activation_date)
//   data.rls_date = common.formatDateYYYYMMDDHHmmss(data.rls_date)
//   data.created_date = common.formatDateYYYYMMDDHHmmss(data.created_date)
//   data.updated_date = common.formatDateYYYYMMDDHHmmss(data.updated_date)
//   data.start_date = common.formatDateYYYYMMDDHHmmss(data.start_date)
//   data.end_date = common.formatDateYYYYMMDDHHmmss(data.end_date)
//   data.active_date = common.formatDateYYYYMMDDHHmmss(data.active_date)
//   return data
// }

// const convertTimeRiskGroupGMT7 = function (data) {
//   data.start_date = common.formatDateYYYYMMDDHHmmss(data.start_date)
//   data.day_over_due = common.formatDateYYYYMMDDHHmmss(data.day_over_due)
//   data.user_update_date = common.formatDateYYYYMMDDHHmmss(data.user_update_date)
//   data.created_date = common.formatDateYYYYMMDDHHmmss(data.created_date)
//   data.updated_date = common.formatDateYYYYMMDDHHmmss(data.updated_date)
//   data.cal_dpd_date = common.formatDateYYYYMMDDHHmmss(data.cal_dpd_date)
//   data.observation_time = common.formatDateYYYYMMDDHHmmss(data.observation_time)
//   return data
// }

// const convertTimeBillOnDueGMT7 = function (data) {
//   data.on_due_date = common.formatDateYYYYMMDDHHmmss(data.on_due_date)
//   data.start_date = common.formatDateYYYYMMDDHHmmss(data.start_date)
//   data.end_date = common.formatDateYYYYMMDDHHmmss(data.end_date)
//   data.created_date = common.formatDateYYYYMMDDHHmmss(data.created_date)
//   data.updated_date = common.formatDateYYYYMMDDHHmmss(data.updated_date)
//   data.due_date = common.formatDateYYYYMMDDHHmmss(data.due_date)
//   return data
// }

// const convertTimeInstallmentGMT7 = function (data) {
//   data.cycle_date = common.formatDateYYYYMMDDHHmmss(data.cycle_date)
//   data.created_date = common.formatDateYYYYMMDDHHmmss(data.created_date)
//   data.updated_date = common.formatDateYYYYMMDDHHmmss(data.updated_date)
//   data.start_date = common.formatDateYYYYMMDDHHmmss(data.start_date)
//   data.end_date = common.formatDateYYYYMMDDHHmmss(data.end_date)
//   data.due_date = common.formatDateYYYYMMDDHHmmss(data.due_date)
//   data.ir_from_date = common.formatDateYYYYMMDDHHmmss(data.ir_from_date)
//   data.ir_to_date = common.formatDateYYYYMMDDHHmmss(data.ir_to_date)
//   return data
// }

// const convertTimeFeesGMT7 = function (data) {
//   data.created_date = common.formatDateYYYYMMDDHHmmss(data.created_date)
//   data.updated_date = common.formatDateYYYYMMDDHHmmss(data.updated_date)
//   return data
// }

// module.exports = {
// sendMessage, sendMessageV2, sendMessageDpdRg,
// convertTimeRiskGroupGMT7, convertTimePaymentGMT7, convertTimeBillOnDueGMT7, convertTimeFeesGMT7, convertTimeInstallmentGMT7, convertTimeLoanAccountGMT7 }
