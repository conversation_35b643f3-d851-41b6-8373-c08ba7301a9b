const common = require('../utils/common')
const loanAccountRepo = require('../repositories/debt-ack-contract-repo')
const intallmentRepo = require('../repositories/installment-repo')
const contractRiskGrpRepo = require('../repositories/contract-risk-grp-repo')
const userRiskGrpRepo = require('../repositories/user-risk-grp-repo')
const moment = require('moment')
const camelcaseKeys = require('camelcase-keys')
const cicRiskGrpImportRepo = require('../repositories/cic-risk-grp-repo')
const constant = require('../utils/constant')
const loanRgRepo = require('../repositories/loan-risk-grp-repo')
const custRgRepo = require('../repositories/cust-risk-grp-repo')
const collDpdHisRepo = require('../repositories/coll-dpd-hist-repo')
const loanAccRepo = require('../repositories/loan-account-repo')
const loanAnnexRepo = require('../repositories/loan-annex-repo')
const batchProcessDetailRepo = require('../repositories/batch-process-detail-repo')
const cicLogRepo = require('../repositories/cic-log-repo')
const lodash = require('lodash')
const excelToJson = require('convert-excel-to-json')
const loanAccountV2Repo = require("../repositories/loan-account-repo");

const calDpbRiskGrp = async function (payload) {
  try {
    if (payload == undefined || payload.debtAckContractNumber == undefined || payload.calDate == undefined) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Ban phai nhap truong thong tin '
      }
    }
    // const rsLoanAcc = await loanAccountRepo.findLoanAccByPaymentStatusNotCompleted(global.poolRead, payload)
    const [rsLoanAcc, rsCheckContractRiskGroup] = await Promise.all([
      loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, payload),
      contractRiskGrpRepo.findContractRiskGrp(global.poolRead, {
        debtAckContractNumber: payload.debtAckContractNumber
      })
    ])
    const loanAccObj = rsLoanAcc.rows[0]
    if (
      loanAccObj &&
      loanAccObj.payment_status == 0 &&
      rsCheckContractRiskGroup.rowCount > 0 &&
      rsCheckContractRiskGroup.rows[0]?.final_risk_grp <= 1
    ) {
      return {
        statusCode: 400,
        code: 2,
        message: '[LMS-MC] Hop dong da duoc thanh toan het'
      }
    }
    await doCalculatorDpdRiskGrp({ 
      loanAccObj, 
      payload, 
      dpdTriggerType: payload.dpdTriggerType || constant.DPD_TRIGGER_TYPE.CRON_JOB 
    })

    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien nhay DPD Risk Group thanh cong'
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { statusCode: 500, code: -1, message: error.message }
  }
}

async function doCalculatorDpdRiskGrp({
   loanAccObj, 
   payload, 
   dpdTriggerType = constant.DPD_TRIGGER_TYPE.OTHER, 
   isInsertCicRg = false}) {
  if (common.isFactoringLoanChannel(loanAccObj?.partner_code)) {
    return calculatorDpdRiskGrpFactoring({loanAccObj, payload, dpdTriggerType, isInsertCicRg})
  } else {
    return calculatorDpdRiskGrp({loanAccObj, payload, dpdTriggerType, isInsertCicRg})
  }
}

async function calculatorDpdRiskGrp({
  loanAccObj,
  payload,
  dpdTriggerType = constant.DPD_TRIGGER_TYPE.OTHER,
  isInsertCicRg = false
}) {
  console.log('Start calculator dpd ', JSON.stringify(payload))
  const plContractRiskGrp = {
    contractNumber: loanAccObj.contract_number,
    debtAckContractNumber: loanAccObj.debt_ack_contract_number
  }
  const [rsContractRiskGroup, rsContractRiskGroupByCustId, rsInstallment, rsFindCicImport, checkColDpdHist] =
    await Promise.all([
      contractRiskGrpRepo.findContractRiskGrp(global.poolRead, plContractRiskGrp),
      contractRiskGrpRepo.findContractRiskGrp(global.poolRead, { custId: loanAccObj.cust_id }),
      intallmentRepo.findInstallmentToCalDpdByDebtAckContractNumber({
        debtAckContractNumber: loanAccObj.debt_ack_contract_number
      }),
      cicRiskGrpImportRepo.findCicRiskGrp(),
      collDpdHisRepo.findDpdByDebtAckContractNumberAndValueDate(loanAccObj.debt_ack_contract_number, payload.calDate)
    ])

  if (checkColDpdHist.length && dpdTriggerType == constant.DPD_TRIGGER_TYPE.CRON_JOB) {
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Job DPD da duoc thuc hien'
    }
  }
  const contractRiskGroupObj = rsContractRiskGroup.rows[0]
  const listContractRiskGroupByCustId = rsContractRiskGroupByCustId.rows
  let totalRemain = 0
  let dpdRiskGrp = 0
  let obserRiskGrp = 0
  if (rsInstallment.rowCount == 0) {
    rsInstallment.rowCount = 1
    rsInstallment.rows.push({
      due_date: moment(payload.calDate).toDate(),
      end_date: moment(payload.calDate).toDate(),
      num_cycle: 1,
      remain_amount: 0
    })
  }
  if (rsInstallment.rowCount > 0) {
    const insObj = rsInstallment.rows[0]
    const numDayDpd = moment(payload.calDate).diff(moment(insObj.end_date), 'days')

    const countMonth31Day = common.countMonth31Day(insObj.end_date, moment(payload.calDate).toDate())
    // tinh so tien con lai cua
    for (const j in rsInstallment.rows) {
      if (rsInstallment.rows[j].num_cycle == insObj.num_cycle) {
        totalRemain = totalRemain + Number(rsInstallment.rows[j].remain_amount)
      }
    }
    // tinh dpd risk group
    dpdRiskGrp = common.getDpdRiskGroupByDpd(numDayDpd, totalRemain)

    // neu co su kien doi risk group thi  thuc hien goi opcode
    const isDiffDpdRg = dpdRiskGrp != contractRiskGroupObj?.dpd_risk_grp
    const isDiffDpd = numDayDpd != contractRiskGroupObj?.dpd_num_day

    let observationTime
    if (contractRiskGroupObj == undefined) {
      obserRiskGrp = dpdRiskGrp
    } else {
      observationTime = contractRiskGroupObj.observation_time
      if (dpdRiskGrp > contractRiskGroupObj.dpd_risk_grp) {
        if (dpdRiskGrp >= contractRiskGroupObj.obs_risk_grp) {
          obserRiskGrp = dpdRiskGrp
        } else {
          obserRiskGrp = contractRiskGroupObj.obs_risk_grp
        }
      } else if (dpdRiskGrp == contractRiskGroupObj.dpd_risk_grp) {
        obserRiskGrp = contractRiskGroupObj.obs_risk_grp
      } else {
        obserRiskGrp = contractRiskGroupObj.obs_risk_grp
        observationTime = payload.calDate
      }
      if (observationTime && contractRiskGroupObj.dpd_risk_grp < contractRiskGroupObj.obs_risk_grp) {
        const diffObs = common.dateDiff(moment(payload.calDate).toDate(), observationTime).days()
        if ((loanAccObj.tenor <= 12 && diffObs >= 30) || (loanAccObj.tenor > 12 && diffObs >= 90)) {
          obserRiskGrp = dpdRiskGrp
        }
      }
    }
    // user risk group nguoi dung tu nhap vao
    const userRiskGrp = !contractRiskGroupObj ? 0 : contractRiskGroupObj.user_risk_grp

    // tinh contracts risk group
    let contractRiskGrp = 0
    if (userRiskGrp == 0) {
      contractRiskGrp = Math.max(dpdRiskGrp, contractRiskGrp)
      contractRiskGrp = Math.max(obserRiskGrp, contractRiskGrp)
      
      if (common.isShareRiskGroupCustIdChannel(loanAccObj?.partner_code)) {
        for (const k in listContractRiskGroupByCustId) {
          if (listContractRiskGroupByCustId[k].debt_ack_contract_number == loanAccObj.debt_ack_contract_number) continue
          contractRiskGrp = Math.max(listContractRiskGroupByCustId[k].dpd_risk_grp, contractRiskGrp)
          contractRiskGrp = Math.max(listContractRiskGroupByCustId[k].obs_risk_grp, contractRiskGrp)
        }
      }
    } else if (userRiskGrp > 0) {
      contractRiskGrp = userRiskGrp > contractRiskGrp ? userRiskGrp : contractRiskGrp
      if (common.isShareRiskGroupCustIdChannel(loanAccObj?.partner_code)) {
        for (const k in listContractRiskGroupByCustId) {
          if (listContractRiskGroupByCustId[k].debt_ack_contract_number == loanAccObj.debt_ack_contract_number) continue
          contractRiskGrp = Math.max(listContractRiskGroupByCustId[k].user_risk_grp, contractRiskGrp)
        }
      }
    }

    // tinh Individual Risk Group – INDI RG
    let individualRiskGrp = 0
    for (const k in listContractRiskGroupByCustId) {
      if (listContractRiskGroupByCustId[k].debt_ack_contract_number == loanAccObj.debt_ack_contract_number) continue
      individualRiskGrp = Math.max(listContractRiskGroupByCustId[k].obs_risk_grp, individualRiskGrp)
      individualRiskGrp = Math.max(listContractRiskGroupByCustId[k].cont_risk_grp, individualRiskGrp)
    }
    individualRiskGrp = Math.max(contractRiskGrp, individualRiskGrp)

    // tinh Final Risk Group – Final RG
    let finalRiskGrp = 0
    let cicRiskGrp = 0
    let isDiffCicRg = false
    if (contractRiskGroupObj) {
      if (
        rsFindCicImport.length &&
        contractRiskGroupObj.cic_import_date &&
        moment(rsFindCicImport[0].upload_date).format(constant.DATE_FORMAT.YYYYMMDD2) ==
          moment(contractRiskGroupObj.cic_import_date).format(constant.DATE_FORMAT.YYYYMMDD2) &&
        rsFindCicImport[0].id == contractRiskGroupObj.cic_risk_grp_import_id
      ) {
        cicRiskGrp = contractRiskGroupObj.cic_risk_grp
        isDiffCicRg = true
      } else if (contractRiskGroupObj.cic_risk_grp > 1) {
        cicRiskGrp = 1
        isDiffCicRg = true
      }
      finalRiskGrp = Math.max(individualRiskGrp, cicRiskGrp)
    } else {
      finalRiskGrp = individualRiskGrp
    }
    const dpd = numDayDpd > 0 ? numDayDpd : 0
    const dpdStrategy = numDayDpd > 0 ? numDayDpd - countMonth31Day : 0

    // thuc hien insert or update riskgroup
    const plInsUpdate = {
      contractNumber: loanAccObj.contract_number,
      custId: loanAccObj.cust_id,
      dpdRiskGrp,
      startDate: loanAccObj.start_date,
      dayOverdue: common.addDays(loanAccObj.end_date, loanAccObj.grace_day_number),
      obsRiskGrp: obserRiskGrp,
      userUpdateDate: moment().format(),
      contRiskGrp: contractRiskGrp,
      individualRiskGrp,
      cicRiskGrp,
      finalRiskGrp,
      createdUser: global.createdUser,
      ownerId: constant.config.ownerId,
      debtAckContractNumber: loanAccObj.debt_ack_contract_number,
      numDayDpd: dpd,
      calDate: payload.calDate,
      userRiskGrp,
      dpdStrategy,
      observationTime
    }
    const plUpdateLoanAcc = {
      debt_ack_contract_number: loanAccObj.debt_ack_contract_number,
      dpd,
      dpd_strategy: dpdStrategy
    }
    // them nghiep vu cap nhat bang loan account va trang thai is_contract_breach khi hd dat dpd >= 181 ngay
    if (numDayDpd >= 181 && loanAccObj.is_contract_breach != 1) {
      plUpdateLoanAcc.is_contract_breach = 1
    }
    if (contractRiskGroupObj != undefined) {
      await Promise.all([
        contractRiskGrpRepo.updateRiskGrpByContractNumberAndDebtAckContract(global.poolWrite, plInsUpdate),
        loanAccRepo.updateLoanAccount(global.poolWrite, plUpdateLoanAcc)
      ])
    } else {
      await Promise.all([
        contractRiskGrpRepo.insContractRiskGrp(global.poolWrite, plInsUpdate),
        loanAccRepo.updateLoanAccount(global.poolWrite, plUpdateLoanAcc)
      ])
    }
    // insert loan risk group and cust risk group and dpd hist
    if (isDiffDpd || dpdTriggerType == constant.DPD_TRIGGER_TYPE.CRON_JOB) {
      const dataInsertCollDpdHist = {
        contractNumber: loanAccObj.contract_number,
        debtAckContractNumber: loanAccObj.debt_ack_contract_number,
        dpd,
        dpdStrategy,
        valueDate: payload.calDate,
        ownerId: loanAccObj.owner_id
      }
      collDpdHisRepo.insertCollDpdHis(dataInsertCollDpdHist)
    }

    const isDiffObsRg = obserRiskGrp != contractRiskGroupObj?.obs_risk_grp

    if (isDiffDpdRg || isDiffObsRg || dpdTriggerType == constant.DPD_TRIGGER_TYPE.OTHER) {
      const dataInsertContractRg = {
        contractNumber: loanAccObj.contract_number,
        debtAckContractNumber: loanAccObj.debt_ack_contract_number,
        custId: loanAccObj.cust_id,
        dpd,
        dpdRgValue: dpdRiskGrp,
        obsRgValue: obserRiskGrp,
        conRgValue: contractRiskGrp,
        userRgValue: userRiskGrp,
        riskDate: payload.calDate,
        ownerId: loanAccObj.owner_id
      }
      const dataInsertCustRg = {
        custId: loanAccObj.cust_id,
        dpd,
        indRgValue: individualRiskGrp,
        finRgValue: finalRiskGrp,
        riskDate: payload.calDate,
        ownerId: loanAccObj.owner_id
      }
      await Promise.all([
        loanRgRepo.insertManyContractRg(dataInsertContractRg),
        custRgRepo.insertIndAndCicAndFinalRg(dataInsertCustRg)
      ])
    }

    if (isDiffCicRg && dpdTriggerType == constant.DPD_TRIGGER_TYPE.UPDATE_CIC && isInsertCicRg == true) {
      const dataInsertCustRg = {
        custId: loanAccObj.cust_id,
        dpd,
        finRgValue: finalRiskGrp,
        cicRgValue: cicRiskGrp,
        riskDate: payload.calDate
      }
      custRgRepo.insertIndAndCicAndFinalRg(dataInsertCustRg)
    }
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien nhay DPD Risk Group thanh cong',
      dpd
    }
  }
}

async function calculatorDpdRiskGrpFactoring({
                                      loanAccObj,
                                      payload,
                                      dpdTriggerType = constant.DPD_TRIGGER_TYPE.OTHER,
                                      isInsertCicRg = false
                                    }) {
  console.log('Start calculator dpd factoring ', JSON.stringify(payload))
  const plContractRiskGrp = {
    contractNumber: loanAccObj.contract_number,
    debtAckContractNumber: loanAccObj.debt_ack_contract_number
  }
  const [rsContractRiskGroup, rsContractRiskGroupByCustId, rsInstallment, rsFindCicImport, checkColDpdHist] =
      await Promise.all([
        contractRiskGrpRepo.findContractRiskGrp(global.poolRead, plContractRiskGrp),
        contractRiskGrpRepo.findContractRiskGrp(global.poolRead, { custId: loanAccObj.cust_id }),
        intallmentRepo.findInstallmentToCalDpdByDebtAckContractNumberFactoring({
          debtAckContractNumber: loanAccObj.debt_ack_contract_number,
          endDate: loanAccObj.end_date,
        }),
        cicRiskGrpImportRepo.findCicRiskGrp(),
        collDpdHisRepo.findDpdByDebtAckContractNumberAndValueDate(loanAccObj.debt_ack_contract_number, payload.calDate)
      ])

  if (checkColDpdHist.length && dpdTriggerType == constant.DPD_TRIGGER_TYPE.CRON_JOB) {
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Job DPD da duoc thuc hien'
    }
  }
  const contractRiskGroupObj = rsContractRiskGroup.rows[0]
  const listContractRiskGroupByCustId = rsContractRiskGroupByCustId.rows
  let totalRemain = 0
  let dpdRiskGrp = 0
  let obserRiskGrp = 0
  if (rsInstallment.rowCount == 0) {
    rsInstallment.rowCount = 1
    rsInstallment.rows.push({
      due_date: moment(payload.calDate).toDate(),
      end_date: moment(payload.calDate).toDate(),
      num_cycle: 1,
      remain_amount: 0
    })
  }
  if (rsInstallment.rowCount > 0) {
    const insObj = rsInstallment.rows[0]
    const numDayDpd = moment(payload.calDate).diff(moment(insObj.end_date), 'days')

    const countMonth31Day = common.countMonth31Day(insObj.end_date, moment(payload.calDate).toDate())
    // tinh so tien con lai cua
    for (const j in rsInstallment.rows) {
      if (rsInstallment.rows[j].num_cycle == insObj.num_cycle) {
        totalRemain = totalRemain + Number(rsInstallment.rows[j].remain_amount)
      }
    }
    // tinh dpd risk group
    dpdRiskGrp = common.getDpdRiskGroupByDpd(numDayDpd, totalRemain)

    // neu co su kien doi risk group thi  thuc hien goi opcode
    const isDiffDpdRg = dpdRiskGrp != contractRiskGroupObj?.dpd_risk_grp
    const isDiffDpd = numDayDpd != contractRiskGroupObj?.dpd_num_day

    let observationTime
    if (contractRiskGroupObj == undefined) {
      obserRiskGrp = dpdRiskGrp
    } else {
      observationTime = contractRiskGroupObj.observation_time
      if (dpdRiskGrp > contractRiskGroupObj.dpd_risk_grp) {
        if (dpdRiskGrp >= contractRiskGroupObj.obs_risk_grp) {
          obserRiskGrp = dpdRiskGrp
        } else {
          obserRiskGrp = contractRiskGroupObj.obs_risk_grp
        }
      } else if (dpdRiskGrp == contractRiskGroupObj.dpd_risk_grp) {
        obserRiskGrp = contractRiskGroupObj.obs_risk_grp
      } else {
        obserRiskGrp = contractRiskGroupObj.obs_risk_grp
        observationTime = payload.calDate
      }
      if (observationTime && contractRiskGroupObj.dpd_risk_grp < contractRiskGroupObj.obs_risk_grp) {
        const diffObs = common.dateDiff(moment(payload.calDate).toDate(), observationTime).days()
        if ((loanAccObj.tenor <= 12 && diffObs >= 30) || (loanAccObj.tenor > 12 && diffObs >= 90)) {
          obserRiskGrp = dpdRiskGrp
        }
      }
    }
    // user risk group nguoi dung tu nhap vao
    const userRiskGrp = !contractRiskGroupObj ? 0 : contractRiskGroupObj.user_risk_grp

    // tinh contracts risk group
    let contractRiskGrp = 0
    if (userRiskGrp == 0) {
      contractRiskGrp = Math.max(dpdRiskGrp, contractRiskGrp)
      contractRiskGrp = Math.max(obserRiskGrp, contractRiskGrp)
      for (const k in listContractRiskGroupByCustId) {
        if (listContractRiskGroupByCustId[k].debt_ack_contract_number == loanAccObj.debt_ack_contract_number) continue
        contractRiskGrp = Math.max(listContractRiskGroupByCustId[k].dpd_risk_grp, contractRiskGrp)
        contractRiskGrp = Math.max(listContractRiskGroupByCustId[k].obs_risk_grp, contractRiskGrp)
      }
    } else if (userRiskGrp > 0) {
      contractRiskGrp = userRiskGrp > contractRiskGrp ? userRiskGrp : contractRiskGrp
      for (const k in listContractRiskGroupByCustId) {
        if (listContractRiskGroupByCustId[k].debt_ack_contract_number == loanAccObj.debt_ack_contract_number) continue
        contractRiskGrp = Math.max(listContractRiskGroupByCustId[k].user_risk_grp, contractRiskGrp)
      }
    }

    // tinh Individual Risk Group – INDI RG
    let individualRiskGrp = 0
    for (const k in listContractRiskGroupByCustId) {
      if (listContractRiskGroupByCustId[k].debt_ack_contract_number == loanAccObj.debt_ack_contract_number) continue
      individualRiskGrp = Math.max(listContractRiskGroupByCustId[k].obs_risk_grp, individualRiskGrp)
      individualRiskGrp = Math.max(listContractRiskGroupByCustId[k].cont_risk_grp, individualRiskGrp)
    }
    individualRiskGrp = Math.max(contractRiskGrp, individualRiskGrp)

    // tinh Final Risk Group – Final RG
    let finalRiskGrp = 0
    let cicRiskGrp = 0
    let isDiffCicRg = false
    if (contractRiskGroupObj) {
      if (
          rsFindCicImport.length &&
          contractRiskGroupObj.cic_import_date &&
          moment(rsFindCicImport[0].upload_date).format(constant.DATE_FORMAT.YYYYMMDD2) ==
          moment(contractRiskGroupObj.cic_import_date).format(constant.DATE_FORMAT.YYYYMMDD2) &&
          rsFindCicImport[0].id == contractRiskGroupObj.cic_risk_grp_import_id
      ) {
        cicRiskGrp = contractRiskGroupObj.cic_risk_grp
        isDiffCicRg = true
      } else if (contractRiskGroupObj.cic_risk_grp > 1) {
        cicRiskGrp = 1
        isDiffCicRg = true
      }
      finalRiskGrp = Math.max(individualRiskGrp, cicRiskGrp)
    } else {
      finalRiskGrp = individualRiskGrp
    }
    const dpd = numDayDpd > 0 ? numDayDpd : 0
    const dpdStrategy = numDayDpd > 0 ? numDayDpd - countMonth31Day : 0

    // thuc hien insert or update riskgroup
    const plInsUpdate = {
      contractNumber: loanAccObj.contract_number,
      custId: loanAccObj.cust_id,
      dpdRiskGrp,
      startDate: loanAccObj.start_date,
      dayOverdue: common.addDays(loanAccObj.end_date, loanAccObj.grace_day_number),
      obsRiskGrp: obserRiskGrp,
      userUpdateDate: moment().format(),
      contRiskGrp: contractRiskGrp,
      individualRiskGrp,
      cicRiskGrp,
      finalRiskGrp,
      createdUser: global.createdUser,
      ownerId: constant.config.ownerId,
      debtAckContractNumber: loanAccObj.debt_ack_contract_number,
      numDayDpd: dpd,
      calDate: payload.calDate,
      userRiskGrp,
      dpdStrategy,
      observationTime
    }
    const plUpdateLoanAcc = {
      debt_ack_contract_number: loanAccObj.debt_ack_contract_number,
      dpd,
      dpd_strategy: dpdStrategy
    }
    // them nghiep vu cap nhat bang loan account va trang thai is_contract_breach khi hd dat dpd >= 181 ngay
    if (numDayDpd >= 181 && loanAccObj.is_contract_breach != 1) {
      plUpdateLoanAcc.is_contract_breach = 1
    }
    if (contractRiskGroupObj != undefined) {
      await Promise.all([
        contractRiskGrpRepo.updateRiskGrpByContractNumberAndDebtAckContract(global.poolWrite, plInsUpdate),
        loanAccRepo.updateLoanAccount(global.poolWrite, plUpdateLoanAcc)
      ])
    } else {
      await Promise.all([
        contractRiskGrpRepo.insContractRiskGrp(global.poolWrite, plInsUpdate),
        loanAccRepo.updateLoanAccount(global.poolWrite, plUpdateLoanAcc)
      ])
    }
    // insert loan risk group and cust risk group and dpd hist
    if (isDiffDpd || dpdTriggerType == constant.DPD_TRIGGER_TYPE.CRON_JOB) {
      const dataInsertCollDpdHist = {
        contractNumber: loanAccObj.contract_number,
        debtAckContractNumber: loanAccObj.debt_ack_contract_number,
        dpd,
        dpdStrategy,
        valueDate: payload.calDate,
        ownerId: loanAccObj.owner_id
      }
      collDpdHisRepo.insertCollDpdHis(dataInsertCollDpdHist)
    }

    const isDiffObsRg = obserRiskGrp != contractRiskGroupObj?.obs_risk_grp

    if (isDiffDpdRg || isDiffObsRg || dpdTriggerType == constant.DPD_TRIGGER_TYPE.OTHER) {
      const dataInsertContractRg = {
        contractNumber: loanAccObj.contract_number,
        debtAckContractNumber: loanAccObj.debt_ack_contract_number,
        custId: loanAccObj.cust_id,
        dpd,
        dpdRgValue: dpdRiskGrp,
        obsRgValue: obserRiskGrp,
        conRgValue: contractRiskGrp,
        userRgValue: userRiskGrp,
        riskDate: payload.calDate,
        ownerId: loanAccObj.owner_id
      }
      const dataInsertCustRg = {
        custId: loanAccObj.cust_id,
        dpd,
        indRgValue: individualRiskGrp,
        finRgValue: finalRiskGrp,
        riskDate: payload.calDate,
        ownerId: loanAccObj.owner_id
      }
      await Promise.all([
        loanRgRepo.insertManyContractRg(dataInsertContractRg),
        custRgRepo.insertIndAndCicAndFinalRg(dataInsertCustRg)
      ])
    }

    if (isDiffCicRg && dpdTriggerType == constant.DPD_TRIGGER_TYPE.UPDATE_CIC && isInsertCicRg == true) {
      const dataInsertCustRg = {
        custId: loanAccObj.cust_id,
        dpd,
        finRgValue: finalRiskGrp,
        cicRgValue: cicRiskGrp,
        riskDate: payload.calDate
      }
      custRgRepo.insertIndAndCicAndFinalRg(dataInsertCustRg)
    }
    return {
      statusCode: 200,
      code: 0,
      message: '[LMS-MC] Thuc hien nhay DPD Risk Group thanh cong',
      dpd
    }
  }
}

const getDpdInfo = async function (req, res) {
  try {
    const pl = req.query
    if (pl == undefined || pl.debtAckContractNumber == undefined) {
      res.status(400).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Ban phai nhap truong thong tin '
        })
      )
    } else {
      const rsData = await contractRiskGrpRepo.findContractRiskGrp(global.poolRead, pl)
      if (rsData.rowCount == 0) {
        res.status(200).json(
          (res.body = {
            code: 1,
            message: '[LMS-MC] Khong tim thay du lieu'
          })
        )
      } else {
        for (const i in rsData.rows) {
          rsData.rows[i].dpd_vas = rsData.rows[i].dpd_num_day > 0 ? rsData.rows[i].dpd_num_day : 0
          rsData.rows[i].dpd_strategy = rsData.rows[i].dpd_strategy > 0 ? rsData.rows[i].dpd_strategy : 0
          // rsData.rows[i].dpd_risk_grp = rsData.rows[i].dpd_num_day > 0 ? rsData.rows[i].dpd_num_day : 0
        }
        res.status(200).json(
          (res.body = {
            code: 0,
            message: '[LMS-MC] Thuc hien lay thong tin thanh cong',
            data: camelcaseKeys(rsData.rows)
          })
        )
      }
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}
const getUserRiskList = async function (req, res) {
  try {
    const payload = {}
    const rsData = await userRiskGrpRepo.findAllUserRiskGrp(global.poolRead, payload)
    if (rsData.rowCount == 0) {
      res.status(200).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Khong tim thay du lieu'
        })
      )
    } else {
      res.status(200).json(
        (res.body = {
          code: 0,
          message: '[LMS-MC] Thuc hien lay thong tin thanh cong',
          data: camelcaseKeys(rsData.rows)
        })
      )
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}
const saveUserRiskGrp = async function (req, res) {
  try {
    const payload = req.body
    const rsData = await contractRiskGrpRepo.findContractRiskGrp(global.poolWrite, payload)

    if (rsData.rowCount == 0) {
      res.status(200).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Khong tim thay du lieu'
        })
      )
    } else {
      const contractRiskGroupObj = rsData.rows[0]
      const rs = await contractRiskGrpRepo.findContractRiskGrp(global.poolRead, {
        custId: contractRiskGroupObj.cust_id
      })

      const loanAccObj = await loanAccountV2Repo.findLoanAccByDebtAckContractNumber(contractRiskGroupObj.debt_ack_contract_number)
      
      const listContractRiskGroupByCustId = rs.rows
      const userRiskGrp = payload.userRiskGrp
      // tinh contracts risk group
      let contractRiskGrp = 0
      let dpdRiskGrp, obserRiskGrp
      if (userRiskGrp == 0) {
        contractRiskGrp = dpdRiskGrp > contractRiskGrp ? dpdRiskGrp : contractRiskGrp
        contractRiskGrp = obserRiskGrp > contractRiskGrp ? obserRiskGrp : contractRiskGrp

        if (common.isShareRiskGroupCustIdChannel(loanAccObj?.partner_code)) {
          for (const k in listContractRiskGroupByCustId) {
            if (listContractRiskGroupByCustId[k].debt_ack_contract_number == payload.debtAckContractNumber) continue
            contractRiskGrp =
                listContractRiskGroupByCustId[k].dpd_risk_grp > contractRiskGrp
                    ? listContractRiskGroupByCustId[k].dpd_risk_grp
                    : contractRiskGrp
            contractRiskGrp =
                listContractRiskGroupByCustId[k].obs_risk_grp > contractRiskGrp
                    ? listContractRiskGroupByCustId[k].obs_risk_grp
                    : contractRiskGrp
          }
        }
      } else if (userRiskGrp > 0) {
        contractRiskGrp = userRiskGrp > contractRiskGrp ? userRiskGrp : contractRiskGrp
        if (common.isShareRiskGroupCustIdChannel(loanAccObj?.partner_code)) {
          for (const k in listContractRiskGroupByCustId) {
            if (listContractRiskGroupByCustId[k].debt_ack_contract_number == payload.debtAckContractNumber) continue
            contractRiskGrp =
                listContractRiskGroupByCustId[k].user_risk_grp > contractRiskGrp
                    ? listContractRiskGroupByCustId[k].user_risk_grp
                    : contractRiskGrp
          }
        }
      }

      // tinh Individual Risk Group – INDI RG
      let individualRiskGrp = 0
      for (const k in listContractRiskGroupByCustId) {
        if (listContractRiskGroupByCustId[k].debt_ack_contract_number == payload.debtAckContractNumber) continue
        individualRiskGrp =
            listContractRiskGroupByCustId[k].obs_risk_grp > individualRiskGrp
                ? listContractRiskGroupByCustId[k].obs_risk_grp
                : individualRiskGrp
        individualRiskGrp =
            listContractRiskGroupByCustId[k].cont_risk_grp > individualRiskGrp
                ? listContractRiskGroupByCustId[k].cont_risk_grp
                : individualRiskGrp
      }
      individualRiskGrp = contractRiskGrp > individualRiskGrp ? contractRiskGrp : individualRiskGrp

      // tinh Final Risk Group – Final RG
      let finalRiskGrp = 0
      // let cicRiskGrp = 0
      if (contractRiskGroupObj != undefined) {
        finalRiskGrp =
          individualRiskGrp > contractRiskGroupObj.cic_risk_grp ? individualRiskGrp : contractRiskGroupObj.cic_risk_grp
        // cicRiskGrp = contractRiskGroupObj.cic_risk_grp
      } else {
        finalRiskGrp = individualRiskGrp
      }
      const plUpdate = {
        debtAckContractNumber: contractRiskGroupObj.debt_ack_contract_number,
        userRiskGrp,
        contRiskGrp: contractRiskGrp,
        individualRiskGrp,
        finalRiskGrp
      }
      await contractRiskGrpRepo.updateUserRiskGrpByDebtAckContract(global.poolWrite, plUpdate)
      res.status(200).json(
        (res.body = {
          code: 0,
          message: '[LMS-MC] Thuc hien cap nhat User risk group thanh cong'
          // data: camelcaseKeys(rsUpdate.rows)
        })
      )
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}
const importCicRiskGrp = async function (req, res) {
  try {
    const file = req.files.cicTemplate
    const buffer = file.data

    const body = req.body
    !body.createdBy && (body.createdBy = constant.config.createdBy)
    !body.importDate && (body.importDate = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmss))

    const plImport = {
      fileName: file.name,
      ownerId: constant.config.ownerId,
      isTesting: constant.config.isTesting,
      createdBy: body.createdBy,
      importDate: body.importDate
    }
    const rsImport = await cicRiskGrpImportRepo.insCicRiskGrp(plImport)
    if (rsImport.rowCount == 0) {
      return res.status(200).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Khong the them file import'
        })
      )
    }
    const result = excelToJson({
      source: buffer,
      header: {
        rows: 1
      },
      sheets: [
        {
          name: 'LO TH',
          columnToKey: {
            A: 'cust_id',
            B: 'cic_id',
            C: 'cust_name',
            D: 'risk_grp_val',
            E: 'risk_grp_val_es',
            F: 'total_bal_amt_es',
            G: 'credit_institution_name',
            H: 'created_user',
            I: 'product_code',
            J: 'risk_date'
          }
        }
      ]
    })
    const limitChunk = 200
    const resultChunks = lodash.chunk(result['LO TH'], limitChunk)

    console.log(`START INSERT ${result['LO TH'].length} customer cic log`)

    for (const [index, resultChunk] of resultChunks.entries()) {
      const listInsertCicLog = []
      console.log(`START INSERT index ${index * limitChunk} to index ${(index + 1) * limitChunk}`)
      await Promise.all(
        resultChunk.map(async (cic) => {
          const dataInsertCicLog = {
            ...cic,
            risk_type: constant.RISK_GROUP.CIC_RG,
            risk_grp_val: Number(cic.risk_grp_val),
            risk_grp_val_es: Number(cic.risk_grp_val_es),
            total_bal_amt_es: Number(cic.total_bal_amt_es),
            risk_date: moment(cic.risk_date).format(constant.DATE_FORMAT.YYYYMMDD2)
          }
          listInsertCicLog.push(dataInsertCicLog)
          await contractRiskGrpRepo.updateContractRiskGrpByCustId(
            dataInsertCicLog.cust_id,
            dataInsertCicLog.risk_grp_val,
            body.importDate,
            rsImport.rows[0].id
          )
        })
      )
      await cicLogRepo.insBatchCicLog(listInsertCicLog)
      console.log(`END INSERT index ${index * limitChunk} to index ${(index + 1) * limitChunk}`)
    }
    const listCustIdToUpdateDpd = await contractRiskGrpRepo.findCustIdHaveCic()
    await Promise.all(
      listCustIdToUpdateDpd.map(async (item) => {
        await updateDpdSequence(item.cust_id, body.importDate, constant.DPD_TRIGGER_TYPE.UPDATE_CIC, true)
      })
    )

    console.log(`SUCCESSFUL: INSERT ${result['LO TH'].length} customer cic log `)
    return res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Thuc hien import CIC risk group thanh cong'
      })
    )
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}
const getHistoryImport = async function (req, res) {
  try {
    const payload = req.query
    const rsData = await cicRiskGrpImportRepo.getHistoryImportByDebtAck(global.poolWrite, payload)
    if (rsData.rowCount == 0) {
      res.status(200).json(
        (res.body = {
          code: 1,
          message: '[LMS-MC] Khong tim thay du lieu'
        })
      )
    } else {
      res.status(200).json(
        (res.body = {
          code: 0,
          message: '[LMS-MC] Thuc hien lay lich su import thanh cong',
          data: camelcaseKeys(rsData.rows)
        })
      )
    }
  } catch (error) {
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}

async function updateDpdSequence(
  custId,
  now,
  dpdTriggerType = constant.DPD_TRIGGER_TYPE.CRON_JOB,
  isInsertCicRg = false
) {
  const listOrderByDpd = await loanAccRepo.findListContractByCustIdToRunDpd(now, custId)
  for (const loanAccount of listOrderByDpd) {
    const startTime = new Date()
    const payload = {
      debtAckContractNumber: loanAccount.debt_ack_contract_number,
      calDate: moment(now).format(constant.DATE_FORMAT.YYYYMMDD2)
    }
    const result = await doCalculatorDpdRiskGrp({ loanAccObj: loanAccount, payload, dpdTriggerType, isInsertCicRg })
    // insert cic 1 lan 1 KH vao bang cust risk grp
    isInsertCicRg = false
    const endTime = new Date()
    const payloadInsertLog = {
      productType: 'MCC',
      startDate: startTime,
      runAt: startTime,
      endDate: endTime,
      batchDate: now,
      jobName: 'batch_process_dpd_risk_grp',
      status: 'dpd_start',
      jobId: moment(now).format(constant.DATE_FORMAT.YYYYMMDD3),
      errorCode: result?.code || '00',
      errorMessage: result?.message || 'success',
      debtAckContractNumber: loanAccount.debt_ack_contract_number
    }
    try {
      batchProcessDetailRepo.insertBatchProcessDetail(payloadInsertLog)
    } catch (error) {
      console.log('error insert batch process detail')
    }
  }
}
const getHistoryDpdInfo = async function (payload) {
  const { custId, startDate, endDate } = payload
  if (!custId) {
    return {
      code: 1,
      statusCode: 400,
      message: 'Missing input: custId'
    }
  }
  let maxDpdCurrent = 0
  let maxDpdEver = 0
  let maxDpdAtEndDate = 0
  let maxCicRg = constant.DEFAULT_RG
  const [listLoanAccount, minTerminationMotive, maxCicLog] = await Promise.all([
    loanAccRepo.findListContractByCustIdV2(custId),
    loanAnnexRepo.findMinTerminationMotiveByCustId(custId, startDate, endDate),
    cicLogRepo.findMaxCicByCustId(custId, startDate, endDate)
  ])

  for (const loanAccount of listLoanAccount) {
    const [maxDpd, lastDpd] = await Promise.all([
      collDpdHisRepo.findMaxDpdByDebtAckContractNumber(loanAccount.debt_ack_contract_number, startDate, endDate),
      collDpdHisRepo.findLastDpdByDebtAckContractNumber(loanAccount.debt_ack_contract_number, endDate)
    ])
    maxDpdEver = Math.max(maxDpdEver, maxDpd?.[0]?.max || 0)
    maxDpdAtEndDate = Math.max(maxDpdAtEndDate, lastDpd?.[0]?.dpd || 0)
    maxDpdCurrent = Math.max(maxDpdCurrent, loanAccount.dpd)
  }
  maxCicRg = Math.max(maxCicRg, maxCicLog?.[0]?.max)

  return {
    code: 0,
    statusCode: 200,
    message: 'getHistoryDpdInfo successffully',
    data: {
      maxDpdCurrent,
      maxDpdEver,
      maxCicRg,
      maxDpdAtEndDate,
      minTerminationMotive: minTerminationMotive?.rows?.[0]?.min
    }
  }
}

async function deactiveRiskGroup({ loanAccObj, riskDate }) {
  console.log('START deactiveRiskGroup')
  const { debt_ack_contract_number, cust_id } = loanAccObj

  await Promise.all([
    loanRgRepo.deActiveByDebtAckContractNumberMoreDate(debt_ack_contract_number, riskDate),
    // custRgRepo.deActiveByCustIdMoreDate(cust_id, riskDate),
    collDpdHisRepo.deActiveByDebtAckContractNumberMoreDate(debt_ack_contract_number, riskDate)
  ])

  const [dpdRg, obsRg, contractRg, individualRg, finalRg, collDpd] = await Promise.all([
    loanRgRepo.findLastestContractRgTypeByDebtAckContractNumber(debt_ack_contract_number, constant.RISK_GROUP.DPD_RG),
    loanRgRepo.findLastestContractRgTypeByDebtAckContractNumber(debt_ack_contract_number, constant.RISK_GROUP.OBSER_RG),
    loanRgRepo.findLastestContractRgTypeByDebtAckContractNumber(
      debt_ack_contract_number,
      constant.RISK_GROUP.CONTRACT_RG
    ),
    custRgRepo.findLastestCustRgTypeByCustId(cust_id, constant.RISK_GROUP.INDIVIDUAL_RG),
    custRgRepo.findLastestCustRgTypeByCustId(cust_id, constant.RISK_GROUP.FINAL_RG),
    collDpdHisRepo.findLastDpdByDebtAckContractNumber(debt_ack_contract_number, riskDate)
  ])

  const plInsUpdate = {
    debtAckContractNumber: debt_ack_contract_number,
    custId: cust_id,
    dpdRiskGrp: Number(dpdRg?.[0]?.risk_grp_val || constant.DEFAULT_RG),
    obsRiskGrp: Number(obsRg?.[0]?.risk_grp_val || constant.DEFAULT_RG),
    contRiskGrp: Number(contractRg?.[0]?.risk_grp_val || constant.DEFAULT_RG),
    individualRiskGrp: Number(individualRg?.[0]?.risk_grp_val || constant.DEFAULT_RG),
    finalRiskGrp: Number(finalRg?.[0]?.risk_grp_val || constant.DEFAULT_RG),
    numDayDpd: Number(collDpd?.[0]?.dpd || constant.DEFAULT_DPD),
    dpdStrategy: Number(collDpd?.[0]?.dpd_strategy || constant.DEFAULT_DPD)
  }
  await contractRiskGrpRepo.updateContractRgByDebtAckContractNumberV2(plInsUpdate)
}
module.exports = {
  calDpbRiskGrp,
  getDpdInfo,
  getUserRiskList,
  saveUserRiskGrp,
  importCicRiskGrp,
  getHistoryImport,
  doCalculatorDpdRiskGrp,
  getHistoryDpdInfo,
  updateDpdSequence,
  deactiveRiskGroup
}
