const common = require('../utils/common')
const constant = require('../utils/constant')
const camelcaseKeys = require('camelcase-keys')
const merchantLimitRepo = require('../repositories/merchant-limit-repo')
const installmentRepo = require('../repositories/installment-repo')

const moment = require('moment')

const createMcCreditLimit = async function (req, res) {
  try {
    const payload = req.body
    console.log('req body createMcCreditLimit: ', JSON.stringify(payload))
    payload.remainAmount = payload.remainAmount ?? payload.amount
    payload.holdMoney = 0
    payload.status = 1
    payload.ownerId = constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting

    if (Number(payload.remainAmount) > Number(payload.remainAmount)) {
      return res.status(400).json((res.body = { code: 2, message: '[MC-LMS] remainAmount khong hop le' }))
    }

    if (!payload.tenor) {
      return res.status(400).json((res.body = { code: 2, message: '[MC-LMS] Tenor khong hop le' }))
    }

    const rsCheck = await merchantLimitRepo.findActiveByContractNumberAndCustId(global.poolRead, payload)

    if (rsCheck.rowCount > 0) {
      return res.status(400).json((res.body = { code: 2, message: '[MC-LMS] Khach hang da duoc khoi tao han muc' }))
    }
    const startDate = moment().toDate()
    const endDate = common.calNextCycleDate2(payload.tenor, common.convertDatetoString(startDate, 'yyyy-mm-dd'))
    payload.startDate = startDate
    payload.endDate = endDate
    merchantLimitRepo
      .insMerchantLimit(global.poolWrite, payload)
      .then((data) => {
        return res.status(200).json(
          (res.body = {
            code: 0,
            message: '[MC-LMS] Khoi tao han muc thanh cong',
            data: camelcaseKeys(data.rows)
          })
        )
      })
      .catch((err) => {
        return res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
      })
  } catch (err) {
    console.log(err)
    console.error('Error while ', err.message)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}
const checkAvalibleAmount = function (req, res) {
  try {
    const payload = req.body
    if (payload == undefined || (payload.contractNumber == undefined && payload.custId == undefined)) {
      res.status(400).json(
        (res.body = {
          code: 1,
          message: '[MC-LMS] Phai nhap toi thieu 1 truong du lieu'
        })
      )
    } else {
      merchantLimitRepo
        .findActiveByContractNumberAndCustId(global.poolRead, payload)
        .then(async (data) => {
          if (data != undefined && data.rows[0] != undefined && data.rows[0].remain_limit_amt > 0) {
            // const remainPrincipalAmount = Number(data.rows[0]?.apr_limit_amt) - Number(data.rows[0].remain_limit_amt)
            const remainPrincipalAmountData = await installmentRepo.sumRemainAmount(
              data.rows[0].cust_id
            )
            const remainPrincipalAmount = remainPrincipalAmountData?.rows?.[0]?.remain_amount
            res.status(200).json(
              (res.body = {
                code: 0,
                message: '[MC-LMS] So tien trong han muc cua khach hang con kha dung',
                data: {
                  avalibleAmount: data.rows[0].remain_limit_amt,
                  remainPrincipalAmount
                }
              })
            )
          } else {
            res.status(200).json(
              (res.body = {
                code: 1,
                message: '[MC-LMS] Khach hang da su dung het han muc hoac chua duoc khoi tao',
                data: {
                  avalibleAmount: data?.rows[0]?.remain_limit_amt
                    ? parseInt(data?.rows[0]?.remain_limit_amt).toFixed(0)
                    : null
                }
              })
            )
          }
        })
        .catch((err) => {
          res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
        })
    }
  } catch (err) {
    // console.log(err)
    console.error('Error while ', err.message)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}

const unlockMcLimit = async function (req, res) {
  try {
    const payload = req.params
    payload.status = 1
    const rsMclimit = await merchantLimitRepo.findByContractNumberAndCustId(global.poolRead, payload)
    if (rsMclimit.rowCount == 0) {
      res.status(200).json(
        (res.body = {
          code: 1,
          message: '[MC-LMS] Ma han muc khong ton tai'
        })
      )
    } else {
      if (rsMclimit.rows[0].status == 1) {
        res.status(200).json(
          (res.body = {
            code: 0,
            message: '[MC-LMS] Ma han muc da duoc mo khoa'
          })
        )
      } else {
        await merchantLimitRepo.updateStatusByMcLimitId(global.poolWrite, payload)
        res.status(200).json(
          (res.body = {
            code: 0,
            message: '[MC-LMS] Mo khoa thanh cong han muc'
          })
        )
      }
    }
  } catch (error) {
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}
const lockMcLimit = async function (req, res) {
  try {
    const payload = req.params
    payload.status = 0
    const rsMclimit = await merchantLimitRepo.findByContractNumberAndCustId(global.poolRead, payload)
    if (rsMclimit.rowCount == 0) {
      res.status(200).json(
        (res.body = {
          code: 1,
          message: '[MC-LMS] Ma han muc khong ton tai'
        })
      )
    } else {
      if (rsMclimit.rows[0].status == 0) {
        res.status(200).json(
          (res.body = {
            code: 0,
            message: '[MC-LMS] Han muc dang khoa'
          })
        )
      } else {
        await merchantLimitRepo.updateStatusByMcLimitId(global.poolWrite, payload)
        res.status(200).json(
          (res.body = {
            code: 0,
            message: '[MC-LMS] Khoa thanh cong han muc'
          })
        )
      }
    }
  } catch (error) {
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}
const deleteMcLimit = async function (req, res) {
  try {
    const payload = req.params
    payload.status = 2
    const rsMclimit = await merchantLimitRepo.findByContractNumberAndCustId(global.poolRead, payload)
    if (rsMclimit.rowCount == 0) {
      res.status(200).json(
        (res.body = {
          code: 1,
          message: '[MC-LMS] Ma han muc khong ton tai'
        })
      )
    } else {
      if (rsMclimit.rows[0].status != 0) {
        res.status(200).json(
          (res.body = {
            code: 0,
            message: '[MC-LMS] Huy HM that bai. HM da huy hoac dang hoat dong'
          })
        )
      } else {
        await merchantLimitRepo.updateStatusByMcLimitId(global.poolWrite, payload)
        res.status(200).json(
          (res.body = {
            code: 0,
            message: '[MC-LMS] Huy thanh cong han muc'
          })
        )
      }
    }
  } catch (error) {
    console.error('Error while ', error.message)
    res.status(error.statusCode || 500).json((res.body = { code: 99, message: error.message }))
  }
}
const updateReviewMcLimit = async function (payload) {
  try {
    const { contractNumber, amount, tenor } = payload

    if (!contractNumber || !amount || !tenor) {
      return {
        code: 0,
        statusCode: 400,
        message: 'Thieu input truyen vao'
      }
    }
    const findContractNumber = await merchantLimitRepo.findByContractNumberAndCustId(global.poolRead, {
      contractNumber
    })

    if (findContractNumber.rowCount == 0) {
      return {
        code: 0,
        statusCode: 400,
        message: 'Khong tim thay hop dong'
      }
    }

    await merchantLimitRepo.updateMcLimitByContractNumber(payload)
    return {
      code: 0,
      statusCode: 200,
      message: 'Review han muc thanh cong'
    }
  } catch (error) {
    return {
      code: 0,
      statusCode: 500,
      message: 'Some thing error' + JSON.stringify(error)
    }
  }
}
const unlockMcLimitV2 = async function (req, res) {
  try {
    const payload = req.body
    if (!payload.contractNumber) {
      return res.status(400).json({ code: 1, message: '[MC-LMS] contractNumber is required' })
    }
    const rsMclimit = await merchantLimitRepo.findByContractNumberAndCustId(global.poolRead, {contractNumber: payload.contractNumber})
    if (rsMclimit.rowCount == 0) {
      return res.status(404).json({ code: 1, message: '[MC-LMS] Ma han muc khong ton tai' })
    }
    if (rsMclimit.rows[0].status == 1) {
      return res.status(200).json({ code: 0, message: '[MC-LMS] Ma han muc da duoc mo khoa' })
    }
    await merchantLimitRepo.updateStatusByContractNumber(global.poolWrite, { contractNumber: payload.contractNumber, status: 1 })
    return res.status(200).json({ code: 0, message: '[MC-LMS] Mo khoa thanh cong han muc' })
  } catch (error) {
    console.error('Error unlockMcLimitV2 while ', error.message)
    return res.status(error.statusCode || 500).json({ code: 99, message: error.message })
  }
}

const lockMcLimitV2 = async function (req, res) {
  try {
    const payload = req.body
    if (!payload.contractNumber) {
      return res.status(400).json({ code: 1, message: '[MC-LMS] contractNumber is required' })
    }
    const rsMclimit = await merchantLimitRepo.findByContractNumberAndCustId(global.poolRead, {contractNumber: payload.contractNumber})
    if (rsMclimit.rowCount == 0) {
      return res.status(404).json({ code: 1, message: '[MC-LMS] Ma han muc khong ton tai' })
    }
    if (rsMclimit.rows[0].status == 0) {
      return res.status(200).json({ code: 0, message: '[MC-LMS] Han muc dang khoa' })
    }
    await merchantLimitRepo.updateStatusByContractNumber(global.poolWrite, { contractNumber: payload.contractNumber, status: 0 })
    return res.status(200).json({ code: 0, message: '[MC-LMS] Khoa thanh cong han muc' })
  } catch (error) {
    console.error('Error lockMcLimitV2 while ', error.message)
    return res.status(error.statusCode || 500).json({ code: 99, message: error.message })
  }
}

const checkAvalibleLimit = async function (req, res) {
  try {
    const payload = req.body
    if (!payload.contractNumber) {
      return res.status(400).json({ code: 1, message: '[MC-LMS] contractNumber is required' })
    }
    const rsMclimit = await merchantLimitRepo.findByContractNumberAndCustId(global.poolRead, {contractNumber: payload.contractNumber})
    if (rsMclimit.rowCount == 0) {
      return res.status(404).json({ code: 1, message: '[MC-LMS] Ma han muc khong ton tai' })
    }
    return res.status(200).json({
      code: 0,
      message: '[MC-LMS] So tien trong han muc cua khach hang con kha dung',
      data: {
        creditLimit: Number(rsMclimit.rows[0].apr_limit_amt || 0).toFixed(0),
        avalibleCreditLimit: Number(rsMclimit.rows[0].remain_limit_amt || 0).toFixed(0),
        isLock: rsMclimit.rows[0].status === 0 ? true : false
      }
    })
  } catch (error) {
    console.error('Error checkAvailableLimit while ', error.message)
    return res.status(error.statusCode || 500).json({ code: 99, message: error.message })
  }
}

module.exports = {
  createMcCreditLimit,
  checkAvalibleAmount,
  unlockMcLimit,
  lockMcLimit,
  deleteMcLimit,
  updateReviewMcLimit,
  unlockMcLimitV2,
  lockMcLimitV2,
  checkAvalibleLimit
}
