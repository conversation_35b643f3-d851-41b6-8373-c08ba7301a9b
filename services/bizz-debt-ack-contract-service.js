const common = require('../utils/common')
const constant = require('../utils/constant')
const camelcaseKeys = require('camelcase-keys')
const debtAckContractRepo = require('../repositories/debt-ack-contract-repo')
const mcLimitRepo = require('../repositories/merchant-limit-repo')
const installmentRepo = require('../repositories/installment-repo')
const disbursementRepo = require('../repositories/disbursement-repo')
const irChargeRepo = require('../repositories/ir-charge-repo')
const installmentDeductionRepo = require('../repositories/promotion-installment-deduction-repo')
const promotionRepo = require('../repositories/promotion-repo')
const loanEmiHistoryRepo = require('../repositories/loan-emi-history-repo')
const crmService = require('../other-services/crm-service')
const tranLogRepo = require('../repositories/tran-log-repo')
const contractRiskGrpService = require('./contract-risk-grp-service')
const installmentService = require('./installment-service')
const losService = require('../other-services/los-service')
const disbursementService = require('../other-services/disbursement-service')
const feesRepo = require('../repositories/fees-repo')
const loanAmortRepo = require('../repositories/loan-amort-repo')
const moment = require('moment')
const lodash = require('lodash')
const loanAccountRepo = require('../repositories/loan-account-repo')
const holidayService = require('./holiday-service')
const loanStatusHstService = require('./loan-status-hst-service')
const welcomePackageService = require('./welcome-package-service')
const actionAuditService = require('../other-services/action-audit-service')
const feeDetailRepo = require('../repositories/fee-detail-repo')
const insuranceService = require('./insurance-service')
const { isCashLoanPartnerCode, isLosUnitedPartnerCode} = require('../utils/helper')
const { LoanAccountRepository, InsuranceRepository, DisbursementRepository, IrChargeRepository } = require('../repositories-v2')
const productService = require('../other-services/product-service')
const {DEBT_ACK_STATUS} = require("../utils/constant");
const loanAccountOrdersRepo = require('../repositories/loan-account-orders-repo')
const LoanAccountOrdersRepository = require('../repositories-v2/loan-account-orders.repository')

/**
 * Tao khe uoc nhan no
 * @param {*} req
 * @param {*} res
 */
const createDebtAckContractFactoring = async function (req) {
  try {
    // Clone input to avoid mutation when logging
    const inputPayload = JSON.parse(JSON.stringify(req.body));
    console.log('req body createDebtAckContractFactoring: ', JSON.stringify(inputPayload));

    const payload = req.body
    const validated = validateInputCreateKunn(payload)

    if (!validated.isSuccess) {
      return {
        code: 1,
        message: validated.message,
        statusCode: 400
      }
    }
    const findLoanAcc = await loanAccountRepo.findLoanAccByDebtAckContractNumber(payload.debtAckContractNumber)

    if (findLoanAcc.length) {
      return {
        code: 1,
        message: '[LMS-MC] KUNN is already exists',
        statusCode: 200
      }
    }
    Object.assign(payload, {
      holdMoney: 0,
      status: 0,
      ownerId: payload.ownerId || constant.config.ownerId,
      createdBy: constant.config.createdBy,
      isTesting: constant.config.isTesting,
      disbursementType: payload.disbursementType || constant.DISBURSEMENT.TYPE.TRANSFER,
      amount: Array.isArray(payload.orders)
        ? payload.orders.reduce((sum, order) => sum + Number(order.prinAmt || 0), 0)
        : 0
    });
    const plLoanAcount = {
      contractNumber: payload.contractNumber,
      paymentStatus: 1
    };

    const [rsMclimit, rsLoanAcc, resProduct] = await Promise.all([
      mcLimitRepo.findActiveByContractNumberAndCustId(global.poolRead, payload),
      debtAckContractRepo.findByContractNumberAndPaymentStatusNotCompleted(global.poolRead, plLoanAcount),
      productService.getProductVoucherV2(payload.productCode, payload.amount)
    ])

    if (rsMclimit.rowCount == 0) {
      return {
        code: 1,
        message: '[MC-LMS] han muc chua duoc khoi tao',
        statusCode: 200
      }
    }
    if (!isCashLoanPartnerCode(payload.partnerCode)) {
      let remainLimitAmount = Number(rsMclimit.rows[0].apr_limit_amt)
      for (const loanAccObj of rsLoanAcc.rows) {
        remainLimitAmount -= Number(loanAccObj.apr_limit_amt)
      }
      if (remainLimitAmount < payload.amount) {
        return {
          code: 1,
          message: '[LMS-MC] Remaining limit amount is not enough',
          data: {
            remainLimitAmount
          },
          statusCode: 200
        }
      }
    }
    calculateNewPayloadFields(payload, rsMclimit.rows[0]);
    // Insert using new repository (no native query)
    const now = new Date();
    const loanAccObj = await LoanAccountRepository.save({
      product_code: payload.productCode,
      apr_limit_amt: payload.amount,
      tenor: payload.tenor,
      start_date: payload.startDate || null,
      end_date: payload.endDate || null,
      noti_date_num: payload.notiDateNum || null,
      product_child_code: payload.productChildCode || null,
      status: payload.status,
      contract_number: payload.contractNumber,
      hold_money: payload.holdMoney || 0,
      active_date: payload.activeDate || null,
      owner_id: payload.ownerId,
      is_testing: payload.isTesting,
      created_by: payload.createdBy,
      contract_limit_id: payload.contractLimitId,
      debt_ack_contract_number: payload.debtAckContractNumber,
      cust_id: payload.custId,
      periodicity: payload.periodicity || null,
      grace_day_number: payload.graceDayNumber,
      bill_day: payload.billDay || null,
      ccycd: payload.ccycd,
      contract_type: payload.contractType,
      partner_code: payload.partnerCode,
      phone_number: payload.phoneNumber,
      channel: payload.channel || null,
      disbursement_type: payload.disbursementType,
      dpd: constant.DEFAULT_DPD,
      order_amt: payload.orderAmt,
      prin_amt: payload.amount,
      approval_date: payload.approvalDate,
      signing_in_progress_date: payload.signingInProgressDate,
      signature_date: payload.signatureDate,
      channel: payload.channel || null,
      debt_tolerance_amt: payload.debtToleranceAmt || 0,
      suspend_account_holding_day: payload.suspendAccountHoldingDay || 0,
    });

    // const loanAccObj = rsIns.rows[0]
    loanStatusHstService.insertNewAndUpdateOldRecord(
      loanAccObj.debt_ack_contract_number,
      constant.LOAN_ACC_STATUS.SIG,
      now
    )

    payload.product = resProduct
    // insert IR charge
    insertIrCharge(payload)
    // insert loan account order
    insertLoanAccountOrder(payload)

    await insertFees(payload.fee, payload)
    // await insuranceService.insertInsurances(payload, loanAccObj)

    disbursementService.sendDisbursementRequest(payload, loanAccObj)

    return {
      code: 0,
      message: '[MC-LMS] create KUNN success',
      data: camelcaseKeys(loanAccObj),
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while creating KUNN: ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}
/**
 * Active khe uoc nhan no
 * @param {*} req
 * @param {*} res
 */
const activeDebtAckContractFactoring = async function (req) {
  try {
    const inputPayload = JSON.parse(JSON.stringify(req.body));
    console.log('req body activeDebtAckContractBizz: ', JSON.stringify(inputPayload));

    const payload = req.body
    payload.ownerId = constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting

    const [rsCheck, rsCheckByLoanId] = await Promise.all([
      debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, {
        debtAckContractNumber: payload.contractNumber
      }),
      debtAckContractRepo.findByLoanIdAndStatus(global.poolRead, payload)
    ])

    if (rsCheck.rowCount == 0 && rsCheckByLoanId.rowCount == 0) {
      return {
        code: 1,
        message: '[LMS-MC] KUNN not found',
        statusCode: 200
      }
    }
    const loanInfo = rsCheck.rows[0] || rsCheckByLoanId.rows[0]

    if (loanInfo.status != constant.DEBT_ACK_STATUS.SIG) {
      return {
        code: 1,
        message: '[LMS-MC] KUNN is not in SIGNED status',
        statusCode: 200
      }
    }
    // Ensure fromDate is always today's date in GMT+7, not affected by UTC offset
    const fromDate = payload.fromDate || moment().tz('Asia/Ho_Chi_Minh').format(constant.DATE_FORMAT.YYYYMMDD2)
    const { loanAccountObj, rsInsmIn, irChargePrinObj } = await handleGenerateInstallment(fromDate, loanInfo)

    loanStatusHstService.insertNewAndUpdateOldRecord(loanInfo.debt_ack_contract_number, constant.LOAN_ACC_STATUS.ACT)

    const mcLimitPayload = {
      mcLimitId: loanAccountObj.contract_limit_id,
      amount: loanAccountObj.apr_limit_amt
    }
    mcLimitRepo.updateRemainAmount(global.poolWrite, mcLimitPayload)
    const disburseObj = await insertTransLogAndUpdateDisbursementStatus(payload, loanAccountObj)
    crmService.callCrmCreateKunn(loanAccountObj)
    if (isLosUnitedPartnerCode(loanAccountObj.partner_code)) {
      crmService.activeLoan(loanAccountObj.debt_ack_contract_number)
    }
    losService.callActiveKunn(loanAccountObj)

    insuranceService.activeInsurances(loanAccountObj)
    // cap nhat khoi tao dpd risk group
    contractRiskGrpService.doCalculatorDpdRiskGrp({
      loanAccObj: loanAccountObj,
      payload: { debtAckContractNumber: loanAccountObj.debt_ack_contract_number, calDate: fromDate }
    })

    welcomePackageService.sendEmailWelcome(loanAccountObj, disburseObj, rsInsmIn, irChargePrinObj)
    actionAuditService.saveActionAudit(
      loanAccountObj.debt_ack_contract_number,
      { actionAuditType: 'CONTRACT_GENERATION', actionCodeType: 'WAITING_TO_BE_GENERATED' },
      { createdUser: payload.createdBy }
    )
    return {
      code: 0,
      message: '[MC-LMS] Kich hoat KUNN thanh cong',
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}

    // ----

    // Helper function to calculate and assign new fields to payload
    function calculateNewPayloadFields(payload, loanContractLimit) {
      payload.custId = loanContractLimit.cust_id;
      payload.contractLimitId = loanContractLimit.contract_limit_id;
      payload.ccycd = payload.ccycd || 'VND';
      payload.disbursementAmount = payload.amount;
      payload.graceDayNumber = payload.graceDayNumber || 0;
      if (!payload.phoneNumber) {
      payload.phoneNumber = loanContractLimit.phone_number;
      }
      // Calculate total order_amt from payload.orders
      payload.orderAmt = Array.isArray(payload.orders)
        ? payload.orders.reduce((sum, order) => sum + Number(order.orderAmt || 0), 0)
        : 0;
    }
const updateDisburserment = async function(req){
  try{
    const payload = req.body
    console.log('req body updateDisburserment v2: ', JSON.stringify(payload))
    payload.ownerId = constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting

    const [rsCheck, rsCheckByLoanId] = await Promise.all([
      debtAckContractRepo.getLoanAccByDebtAckContract(global.poolRead, {
        debtAckContractNumber: payload.contractNumber
      }),
      debtAckContractRepo.findByLoanIdAndStatus(global.poolRead, payload)
    ])
    const loanInfo = rsCheck.rows[0] || rsCheckByLoanId.rows[0]

    await insertTransLogAndUpdateDisbursementStatusV2(payload, loanInfo)

    return {
      code: 0,
      message: '[MC-LMS] Update disbursement status success',
      statusCode: 200
    }

  } catch (error) {
    console.log(error)
    console.error('Error while updateDisburserment:', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}
const getListLoanActive = async function (req, res) {
  try {
    const pl = {
      status: 1
    }
    const rsLoanAcc = await debtAckContractRepo.findLoanAccByPaymentStatusNotCompleted(global.poolRead, pl)
    if (rsLoanAcc.rowCount > 0) {
      const listDebt = []
      for (const i in rsLoanAcc.rows) {
        listDebt.push(rsLoanAcc.rows[i].debt_ack_contract_number)
      }
      return res.status(200).json(
        (res.body = {
          code: 0,
          message: '[LMS-MC] Thuc hien lay danh sach Loan thanh cong',
          data: listDebt
        })
      )
    }
    return res.status(200).json(
      (res.body = {
        code: 1,
        message: '[LMS-MC] Khong tim thay danh sach Loan'
      })
    )
  } catch (err) {
    console.log(err)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}

async function insTranLog(loanAccObj, payload) {
  const dateNow = moment().toDate()
  const pl = {
    contractNumber: loanAccObj.contract_number,
    loanId: loanAccObj.loan_id,
    tranType: 'BANK',
    refId: payload.transactionId,
    amtNumber: loanAccObj.apr_limit_amt,
    tranDate: dateNow,
    valueDate: dateNow,
    tranDesc: 'Disbursement for ' + loanAccObj.debt_ack_contract_number,
    tranStatus: payload.respCode,
    createdUser: constant.config.createdBy,
    sessionId: dateNow.getTime(),
    ownerId: constant.config.ownerId
  }
  const rs = await tranLogRepo.insTranLog(global.poolWrite, pl)
  if (rs.rowCount > 0) {
    return rs.rows[0]
  }
}
function validateInputCreateKunn(payload) {
  if (
    !payload.debtAckContractNumber ||
    !payload.contractNumber ||
    !payload.amount ||
    !payload.contractType ||
    // !payload.irCharge ||
    !payload.partnerCode ||
    !payload.tenor ||
    !payload.orders
  ) {
    return {
      message:
        'Thiếu một trong các trường debtAckContractNumber, contractNumber , amount, contractType, partnerCode, tenor, orders  ',
      isSuccess: false
    }
  }
  // if (payload.irCharge.length < 3) {
  //   return {
  //     message: 'irCharge thiếu dữ liệu truyền vào',
  //     isSuccess: false
  //   }
  // }
  if (!Array.isArray(payload.orders) || payload.orders.length === 0) {
    return {
      message: 'orders thiếu dữ liệu truyền vào',
      isSuccess: false
    }
  }
  if (
    payload.contractType == constant.CONTRACT_TYPE.CASHLOAN &&
    (payload.partnerCode == constant.PARTNER_CODE.MISA ||
      global.config?.data?.mobile?.listChannel.split(',').includes(payload.channel))
  ) {
    if (payload.irCharge.length < 7) {
      return {
        message: 'irCharge vay món thiếu dữ liệu truyền vào',
        isSuccess: false
      }
    }
    // if (payload.irCharge.filter((item) => item.irName == constant.EARLY_TERMINATION_RATE_TYPE).length < 4) {
    //   return {
    //     message: 'irCharge vay món thiếu dữ liệu phí phạt truyền vào',
    //     isSuccess: false
    //   }
    // }
    // const findIrCharge = payload.irCharge.find(
    //   (item) =>
    //     item.irName == constant.EARLY_TERMINATION_RATE_TYPE &&
    //     item.tenorFrom <= payload.tenor &&
    //     item.tenorTo >= payload.tenor
    // )
    // if (!findIrCharge) {
    //   return {
    //     message: 'Dữ liệu phí phạt truyền vào không khớp với tenor',
    //     isSuccess: false
    //   }
    // }
  }

  // if (common.isFactoringLoanChannel(payload.channel)) {
  //   if (
  //       !payload.preferentialEndDate ||
  //       !payload.endDate ||
  //       !payload.agreementNumber ||
  //       !payload.agreementAmt
  //   ) {
  //     return {
  //       message:
  //           'Thiếu một trong các trường preferentialEndDate, endDate , agreementNumber, agreementAmt',
  //       isSuccess: false
  //     }
  //   }
  // }

  // const checkIrValueIsNumber = payload.irCharge.every((item) => !isNaN(item.irValue))

  return {
    isSuccess: true,
    message: 'Validation successful'
  }
}

async function getEndDateOfKunn(loanInfo, fromDate) {
  if (common.isFactoringLoanChannel(loanInfo.channel)) {
    return new Date(loanInfo.end_date)
  }

  let toDate = common.calNextCycleDateV3(loanInfo.tenor, fromDate)
  // if (
  //   loanInfo.contract_type == constant.CONTRACT_TYPE.CASHLOAN &&
  //   loanInfo.partner_code == constant.PARTNER_CODE.MISA
  // ) {
  //   const firstActiveKunn = await loanAccountRepo.findFirstActiveKunnByContractNumberAndContractType(
  //     loanInfo.contract_number
  //   )
  //   if (
  //     firstActiveKunn &&
  //     common.getDifferencesDays(firstActiveKunn?.active_date, fromDate) >
  //       constant.MAX_DIFFERENT_DAYS_BETWEEN_KUNN_CASH_LOAN
  //   ) {
  //     return false
  //   }
  //   toDate = firstActiveKunn ? firstActiveKunn.end_date : toDate
  // }
  if (isLosUnitedPartnerCode(loanInfo.partner_code)) {
    const startDate = new Date(fromDate)
    const startDay = startDate.getDate()
    toDate = new Date(startDate)
    const tenorConfigData = global.tenorConfig[startDay]
    let endDay = startDay
    let deltaMonth = 1
    // if (tenorConfigData && loanInfo.tenor > 1) {
    //   endDay = tenorConfigData.endDay
    //   deltaMonth = tenorConfigData.deltaMonth
    // }
    if (tenorConfigData) {
      endDay = tenorConfigData.endDay
      deltaMonth = tenorConfigData.deltaMonth
    }
    toDate = common.getFirstNextTenor(toDate, endDay, deltaMonth, loanInfo.tenor)
    if (loanInfo.tenor > 1) {
      toDate = common.calNextCycleDateV3(loanInfo.tenor - 1, toDate)
    }
    toDate = holidayService.getNextDayAfterHoliday(toDate)
  }

  return toDate
}
function validateInputCreateKunnLimit(payload) {
  const requiredFields = [
    { key: 'debtAckContractNumber', label: 'debtAckContractNumber' },
    { key: 'contractNumber', label: 'contractNumber' },
    { key: 'amount', label: 'amount' },
    { key: 'contractType', label: 'contractType' },
    { key: 'partnerCode', label: 'partnerCode' },
    { key: 'tenor', label: 'tenor' },
    { key: 'bankInfo', label: 'bankInfo' },
    { key: 'billDay', label: 'billDay' }
  ];
  const missingFields = requiredFields.filter(f => !payload[f.key]).map(f => f.label);
  if (missingFields.length > 0) {
    return {
      message: `Fields are missing: ${missingFields.join(', ')}`,
      isSuccess: false
    }
  }
  return {
    isSuccess: true,
    message: 'Validation successful'
  }
}
async function handleGenerateInstallment(fromDate, loanInfo) {
  const loanAccountObj = {
    ...loanInfo,
    status: constant.DEBT_ACK_STATUS.ACTIVE,
    start_date: fromDate,
    active_date: fromDate,
    // end_date: toDate
  }
  // lay danh sach lai
  const plIrCharge = {
    debtAckContractNumber: loanAccountObj.debt_ack_contract_number,
    productCode: loanAccountObj.product_code
  }
  const [rsIrCharge, rsLoanOrders] = await Promise.all([
    irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, plIrCharge),
    loanAccountOrdersRepo.findOrderByKunnNumber(loanAccountObj.debt_ack_contract_number)
  ])
  const irChargePrinObj = rsIrCharge.rows.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.ON_DUE_PRIN)

  let toDate = new Date(loanInfo.end_date);

  const payloadInsertAmort = {
    debtAckContractNumber: loanAccountObj.debt_ack_contract_number,
    amtAmort: Number(loanAccountObj.apr_limit_amt),
    tenor: loanAccountObj.tenor,
    startDate: fromDate,
    endDate: toDate,
    intRate: Number(irChargePrinObj.ir_value)
  }
  const amortId = await loanAmortRepo.insertLoanAmort(payloadInsertAmort)
  // tao danh sach ban ghi theo tung ky
  let listInstallment

  listInstallment = installmentService.calCycleInstallmentFactoringV2(loanAccountObj, irChargePrinObj, amortId,rsLoanOrders)
  const rsInsmIn = await installmentRepo.insBatchInstallmentFactoring(listInstallment)
  let totalIr = 0
  let totalFee = 0
  const payloadActiveKunn = {
    loanId: loanAccountObj.loan_id,
    startDate: fromDate,
    endDate: toDate,
    intAmt: totalIr,
    feeAmt: totalFee,
    rlsAmt: loanInfo.apr_limit_amt,
    // billDay: minTenor?.end_date ? moment(minTenor.end_date).date() : loanAccountObj.bill_day
  }
  loanAccountRepo.updateActiveKunn(payloadActiveKunn)
  return { loanAccountObj: { ...loanAccountObj, bill_day: payloadActiveKunn.billDay }, rsInsmIn, irChargePrinObj }
}
async function insertFees(arrFee, payload) {
  const product = payload.product;
  const arrFees = [];
  // Handle product.fee as array of fee objects
  if (Array.isArray(product?.fee) && product.fee.length) {
    for (const feeObj of product.fee) {
      const plFee = {
        productCode: payload.productCode,
        code: feeObj.prdctFeId?.toString(),
        name: feeObj.feeName,
        feeAmt: Number(feeObj.value),
        ownerId: payload.ownerId,
        isTesting: payload.isTesting,
        createdBy: payload.createdBy,
        debtAckContractNumber: payload.debtAckContractNumber,
        feeType: feeObj.feeCalType,
        calculaType: feeObj.calculaType,
        instalFrom: feeObj.installmentFrom,
        instalTo: feeObj.installmentTo,
        priority: feeObj.priority
      };
      const feeInsertRs = await feesRepo.insertFees(global.poolWrite, plFee);
      arrFees.push(...feeInsertRs.rows);
    }
  } else if (Array.isArray(arrFee) && arrFee.length && Array.isArray(arrFee[0].feeDetail)) {
    // Fallback for arrFee with feeDetail
    const arrFeeDetail = arrFee[0].feeDetail;
    for (const arrFeeObj of arrFeeDetail) {
      const plFee = {
        productCode: payload.productCode,
        code: arrFeeObj.feId?.toString(),
        name: arrFeeObj.feeName,
        feeAmt: Number(arrFeeObj.isOnePayment),
        ownerId: payload.ownerId,
        isTesting: payload.isTesting,
        createdBy: payload.createdBy,
        debtAckContractNumber: payload.debtAckContractNumber
      };
      const feeInsertRs = await feesRepo.insertFees(global.poolWrite, plFee);
      arrFees.push(...feeInsertRs.rows);
    }
  }

  // Calculate and insert FIRST_TIME fees
  const feeFirtTimes = arrFees.filter(i => i.fee_type == constant.FEE.FEE_CAL_TYPE.FIRST_TIME);
  let feeFistTimeAmt = 0;
  for (const feeFirtTime of feeFirtTimes) {
    const feeFistTimeAmtTmp =
      feeFirtTime?.calcula_type == constant.FEE.TYPE_CALCULATE.FIXED_AMOUNT
        ? Number(feeFirtTime?.fee_amt || 0)
        : feeFirtTime?.calcula_type == constant.FEE.TYPE_CALCULATE.RECIPE
        ? common.roundUp(Number(feeFirtTime?.fee_amt || 0) * payload.amount, global.calcuCfg.scale)
        : 0;
    feeFistTimeAmt += feeFistTimeAmtTmp;
    feeDetailRepo.insertFeeDetail({
      ...payload,
      feeId: feeFirtTime.id,
      feeAmt: feeFistTimeAmtTmp
    });
  }
  payload.disbursementAmount -= feeFistTimeAmt;
  return arrFees;
}
async function insertIrCharge(payload) {
  const recordsIr = []
  // Use rates from payload.product.rate (resProduct.rate)
  const rates = payload.product && Array.isArray(payload.product.rate) ? payload.product.rate : []
  for (const rateObj of rates) {
    // Map rateType to ir_type
    let irType;
    if (rateObj.rateType === constant.IR_NAME.OFFER_NORMAL_RATE) {
      irType = 1
    } else if (rateObj.rateType === constant.IR_NAME.OVER_DUE_RATE) {
      irType = 2
    } else if (rateObj.rateType === constant.IR_NAME.PREFERENTIAL_RATE) {
      irType = 4
    } else {
      irType = rateObj.rateType // fallback to original if not matched
    }
    if (irType === constant.IR_CHARGE_TYPE.ON_DUE_PRIN) {
      payload.irPrinOnDueRate = rateObj.intRateVal
    }
    recordsIr.push({
      debt_ack_contract_number: payload.debtAckContractNumber,
      product_code: payload.productCode,
      ir_code: rateObj.rateType,
      ir_name: rateObj.intRateName,
      ir_type: irType,
      ir_value: rateObj.intRateVal / 100,
      status: constant.IR_CHARGE_STATUS.ACTIVE,
      owner_id: payload.ownerId,
      is_testing: payload.isTesting,
      created_by: payload.createdBy,
      tenor_from: null,
      tenor_to: null,
      instal_from: null,
      instal_to: null
    });
  }
  IrChargeRepository.saveBatch(recordsIr)
  // irChargeRepo.insBatchIrCharge(recordsIr)
}
async function insertLoanAccountOrder(payload) {
  const records = []
  for (const order of payload.orders) {
    records.push({
      order_index: order.orderIndex,
      order_number: order.orderNumber,
      prin_amt: order.prinAmt,
      order_amt: order.orderAmt,
      payment_date: order.paymentDate,
      debt_ack_contract_number: payload.debtAckContractNumber
    })
  }
  LoanAccountOrdersRepository.saveBatch(records)
}
async function insertIrChargeLimit(payload) {
  const recordsIr = []
  for (const irChargeObj of payload.irCharge) {
    if (irChargeObj.irType == constant.IR_CHARGE_TYPE.ON_DUE_PRIN) {
      payload.irPrinOnDueRate = irChargeObj.irValue
    }
    recordsIr.push([
      payload.debtAckContractNumber,
      undefined,
      payload.productCode,
      irChargeObj.irCode,
      irChargeObj.irName,
      irChargeObj.irType,
      irChargeObj.irValue,
      constant.IR_CHARGE_STATUS.ACTIVE,
      payload.ownerId,
      payload.isTesting,
      payload.createdBy,
      irChargeObj.tenorFrom,
      irChargeObj.tenorTo,
      irChargeObj.installmentFrom,
      irChargeObj.installmentTo
    ])
  }
  irChargeRepo.insBatchIrCharge(recordsIr)
}

async function insertTransLogAndUpdateDisbursementStatus(payload, loanAccountObj) {
  const tranLogObj = await insTranLog(loanAccountObj, payload)
  const plUpdateDisbur = payload
  plUpdateDisbur.status = constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
  plUpdateDisbur.tranId = tranLogObj.tran_id
  plUpdateDisbur.partnerTranNo = payload.transactionId
  plUpdateDisbur.tranDate = common.convertDatetoString(new Date(), constant.DATE_FORMAT.YYYYMMDD)
  // const rsDisbur = await disbursementRepo.updateStatusByLoanId(global.poolWrite, plUpdateDisbur)
  const [rsDisbur, rsDisburWithDebtAck] = await Promise.all([
    disbursementRepo.updateStatusByLoanId(global.poolWrite, plUpdateDisbur),
    disbursementRepo.updateStatusByDebtAck({
      ...plUpdateDisbur,
      debtAckContractNumber: loanAccountObj.debt_ack_contract_number
    })
  ])
  const disburseObj = {
    amt: 0,
    email: ''
  }
  if (rsDisbur.rowCount > 0) {
    disburseObj.email = rsDisbur.rows[0].email
    for (const e of rsDisbur.rows) {
      disburseObj.amt += Number(e.amt)
    }
  } else if (rsDisburWithDebtAck.rowCount > 0) {
    disburseObj.email = rsDisburWithDebtAck.rows[0].email
    for (const e of rsDisburWithDebtAck.rows) {
      disburseObj.amt += Number(e.amt)
    }
  }
  return disburseObj
}
async function insertTransLogAndUpdateDisbursementStatusV2(payload, loanAccObj) {
  // const tranLogObj = await insTranLog(loanAccountObj, payload)
  // const plUpdateDisbur = payload
  // plUpdateDisbur.status = constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
  // plUpdateDisbur.tranId = tranLogObj.tran_id
  // plUpdateDisbur.partnerTranNo = payload.transactionId
  // plUpdateDisbur.tranDate = common.convertDatetoString(new Date(), constant.DATE_FORMAT.YYYYMMDD)
  // const rsDisbur = await disbursementRepo.updateStatusByLoanId(global.poolWrite, plUpdateDisbur)
  const disBurListFunc = []
  if (!payload.disbursements || !Array.isArray(payload.disbursements)) {
    return {
      code: 2,
      message: '[LMS-MC] Update disbursement status fail',
      statusCode: 200
    }
  }
  const dateNow = moment().toDate()
  for (const disbur of payload.disbursements) {
    const tranLogIns = await tranLogRepo.insTranLog(global.poolWrite, {
      contractNumber: loanAccObj.contract_number,
      loanId: loanAccObj.loan_id,
      tranType: 'BANK',
      refId: disbur.transactionId,
      amtNumber: disbur.amount,
      tranDate: dateNow,
      valueDate: dateNow,
      tranDesc: 'Disbursement for ' + loanAccObj.debt_ack_contract_number,
      tranStatus: disbur.respCode,
      createdUser: constant.config.createdBy,
      sessionId: dateNow.getTime(),
      ownerId: constant.config.ownerId
    })
    const updatePayload = {
      ...payload,
      disburId: disbur.disburId,
      partnerCode: disbur.partnerCode,
      status:
        disbur.respCode == constant.DISBURSEMENT.RESP_CODE.SUCCESS
          ? constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
          : constant.DISBURSEMENT.TRANS_STATUS.DISBURMENT_IN_PROGESSING,
      tranId: tranLogIns?.rows?.[0].tran_id,
      partnerTranNo: disbur.transactionId,
      tranDate:
        disbur.respCode == constant.DISBURSEMENT.RESP_CODE.SUCCESS
          ? common.convertDatetoString(new Date(), constant.DATE_FORMAT.YYYYMMDD)
          : null
    }
    if (disbur.isReDisburse && disbur.respCode == constant.DISBURSEMENT.RESP_CODE.SUCCESS) {
      updatePayload.reDisburseDate = common.convertDatetoString(
        disbur.reDisburseDate ? new Date(disbur.reDisburseDate) : new Date(),
        constant.DATE_FORMAT.YYYYMMDD
      )
    }
    disBurListFunc.push(disbursementRepo.updateStatusByDisburId(global.poolWrite, updatePayload))
  }
  return await Promise.all(disBurListFunc)
}

const getDisbursements = async (debtAckContractNumber) => {
  if (!debtAckContractNumber) {
    return {
      code: 1,
      message: '[MC-LMS] Invalid input',
      statusCode: 400
    }
  }
  const loanAccount = await LoanAccountRepository.findOne({
    where: { debt_ack_contract_number: debtAckContractNumber }
  })

  if (!loanAccount) {
    return {
      code: 1,
      message: '[MC-LMS] Not found',
      statusCode: 400
    }
  }

  const disburs = await DisbursementRepository.findAll({
    where: {
      debt_ack_contract_number: debtAckContractNumber
    },
    order: {
      created_date: 'DESC'
    }
  })

  if (!disburs || disburs?.length === 0) {
    return {
      code: 1,
      message: '[MC-LMS] Insurance Not found',
      statusCode: 400
    }
  }

  let newDisburs = [];
  for (const disbur of disburs) {
    let init = {
      debt_ack_contract_number: disbur.debt_ack_contract_number,
      bank_name: disbur.bank_name,
      bank_account: disbur.bank_account,
      tran_id: disbur.tran_id,
      amt: disbur.amt,
      tran_date: disbur.tran_date ? moment(disbur.tran_date).format(constant.DATE_FORMAT.YYYYMMDD2) : disbur.tran_date,
      partner_code: disbur.partner_code,
      receiver_name: disbur.receiver_name,
      partner_tran_no: disbur.partner_tran_no,
      tran_status: disbur.tran_status ? constant.DISBURSEMENT.TRANS_STATUS.codeToStatus[disbur.tran_status] : null,
      re_disburse_date: disbur.re_disburse_date ? moment(disbur.re_disburse_date).format(constant.DATE_FORMAT.YYYYMMDD2) : disbur.re_disburse_date,
      bank_code: disbur.bank_code ?? null
    }
    newDisburs.push(init);
  }

  return {
    code: 0,
    message: '[MC-LMS] Success',
    statusCode: 200,
    data: [...newDisburs]
  }
}
/**
 * Tao khe uoc nhan no
 * @param {*} req
 * @param {*} res
 */
const createDebtAckContractBizzLimit = async function (req) {
  try {
    const payload = req.body
    console.log('req body createDebtAckContractBizzLimit: ', JSON.stringify(payload))

    const validated = validateInputCreateKunnLimit(payload)

    if (!validated.isSuccess) {
      return {
        code: 1,
        message: validated.message,
        statusCode: 400
      }
    }
    const findLoanAcc = await loanAccountRepo.findLoanAccByDebtAckContractNumber(payload.debtAckContractNumber)

    if (findLoanAcc.length) {
      return {
        code: 1,
        message: '[MC-LMS] debtAckContractNumber is already exists',
        statusCode: 200
      }
    }
    payload.holdMoney = 0
    payload.status = 0
    payload.ownerId = payload.ownerId || constant.config.ownerId
    payload.createdBy = constant.config.createdBy
    payload.isTesting = constant.config.isTesting
    !payload.disbursementType && (payload.disbursementType = constant.DISBURSEMENT.TYPE.TRANSFER)
    const plLoanAcount = {
      contractNumber: payload.contractNumber,
      paymentStatus: 1
    }

    const [rsMclimit, rsLoanAcc, firstActiveKunn, resProduct, resProductKunn] = await Promise.all([
      mcLimitRepo.findActiveByContractNumberAndCustId(global.poolRead, payload),
      debtAckContractRepo.findByContractNumberAndPaymentStatusNotCompleted(global.poolRead, plLoanAcount),
      loanAccountRepo.findFirstActiveKunnByContractNumberAndContractType(payload.contractNumber),
      productService.getProductInfoV4(payload.productCode),
      productService.getIrChargeKunnProduct(payload.productCode)
    ])
    // const rsMclimit = listFunc[0]
    // const rsLoanAcc = listFunc[1]

    if (rsMclimit.rowCount == 0) {
      return {
        code: 1,
        message: '[MC-LMS] ContractNumber limit not found',
        statusCode: 200
      }
    }
    if (!isCashLoanPartnerCode(payload.partnerCode)) {
      let remainLimitAmount = Number(rsMclimit.rows[0].apr_limit_amt)
      for (const loanAccObj of rsLoanAcc.rows) {
        remainLimitAmount -= Number(loanAccObj.prin_amt)
      }
      if (remainLimitAmount < payload.amount) {
        return {
          code: 1,
          message: '[MC-LMS] Remaining limit of contract is not enough',
          data: {
            remainLimitAmount
          },
          statusCode: 200
        }
      }
    }
    const loanContractLimit = rsMclimit.rows[0]
    payload.custId = loanContractLimit.cust_id
    payload.contractLimitId = loanContractLimit.contract_limit_id
    payload.ccycd = payload.ccycd || 'VND'
    payload.disbursementAmount = payload.amount

    payload.graceDayNumber = payload.graceDayNumber || 0
    !payload.phoneNumber && (payload.phoneNumber = loanContractLimit.phone_number)

    // const firstActiveKunn = listFunc[2]
    const now = new Date()
    const rsIns = await debtAckContractRepo.insDebtAckContract(global.poolWrite, payload)
    const loanAccObj = rsIns.rows[0]
    loanStatusHstService.insertNewAndUpdateOldRecord(
      loanAccObj.debt_ack_contract_number,
      constant.LOAN_ACC_STATUS.SIG,
      now
    )
    payload.irCharge = resProductKunn
    insertIrChargeLimit(payload)

    const arrFee = payload.fee
    // const resProduct = listFunc[3]
    payload.product = resProduct
    await insertFees(arrFee, payload)
    // await insuranceService.insertInsurances(payload, loanAccObj)

    disbursementService.disbursmentMultiBanks(payload, loanAccObj)

    return {
      code: 0,
      message: '[MC-LMS] create KUNN success',
      data: camelcaseKeys(loanAccObj),
      statusCode: 200
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { code: 99, message: error.message, statusCode: error.statusCode || 500 }
  }
}

module.exports = {
  createDebtAckContractFactoring,
  activeDebtAckContractFactoring,
  getListLoanActive,
  updateDisburserment,
  insertTransLogAndUpdateDisbursementStatusV2,
  getDisbursements,
  createDebtAckContractBizzLimit,
}
