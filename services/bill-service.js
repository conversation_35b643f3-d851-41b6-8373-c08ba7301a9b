const common = require('../utils/common')
const installmentRepo = require('../repositories/installment-repo')
const loanAccountRepo = require('../repositories/debt-ack-contract-repo')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const constant = require('../utils/constant')
const moment = require('moment')
const tranLogRepo = require('../repositories/tran-log-repo')
const loanAnnexService = require('../services/loan-annex-service')
const repaymentService = require('../services/repayment-service')
const batchProcessDetailRepo = require('../repositories/batch-process-detail-repo')
const {LoanAccountRepository} = require("../repositories-v2");

const doCreateBillOnDue = async function (payload) {
  const { debtAckContractNumber } = payload

  if (!debtAckContractNumber) {
      return null
  }

  const loanAccountObj = await LoanAccountRepository.findOne({ where: {
      debt_ack_contract_number: debtAckContractNumber
    } });

  if (common.isFactoringLoanChannel(loanAccountObj?.partner_code)) {
    return await createBillOnDueFactoring(payload)
  } else {
    return await createBillOnDue(payload)
  }
}

const createBillOnDue = async function (payload) {
  try {
    console.log('req body createBillOnDue: ', JSON.stringify(payload))
    if (
      payload == undefined ||
      (payload.contractNumber == undefined && payload.debtAckContractNumber == undefined) ||
      payload.onDueDate == null
    ) {
      return { statusCode: 400, code: 1, message: 'Chua nhap du lieu dau vao' }
    }
    const rsLoanDebtAck = await loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, payload)
    if (rsLoanDebtAck.rowCount == 0) {
      return { statusCode: 200, code: 3, message: 'KUNN not found' }
    }
    const rsBillOnDue = await billOnDueRepo.getBillByContractAndOnDueDate(global.poolRead, payload)

    if (rsBillOnDue.rowCount > 0) {
      return { statusCode: 200, code: 1, message: 'Hoa don cua ngay da duoc tao' }
    }
    const rsInsNotClosed = await installmentRepo.getInstallmentNotClosed({
      debtAckContractNumber: payload.debtAckContractNumber,
      endDate: payload.onDueDate
    })
    if (rsInsNotClosed.rowCount == 0) {
      return { statusCode: 200, code: 2, message: 'Ngay khong ton tai ky bill' }
    }
    const loanAccountObj = rsLoanDebtAck.rows[0]

    const listRecords = []
    const listIds = []
    let toCollect = 0
    for (const i in rsInsNotClosed.rows) {
      const obj = rsInsNotClosed.rows[i]
      toCollect += Number(obj.remain_amount)
      const startDate = common.formatDate({ date: obj.start_date })
      const endDate = common.formatDate({ date: obj.end_date })
      const dueDate = common.formatDate({ date: obj.due_date })
      listIds.push(obj.id)
      listRecords.push([
        obj.contract_number,
        obj.debt_ack_contract_number,
        obj.remain_amount,
        obj.remain_amount,
        obj.type,
        obj.num_cycle,
        endDate,
        obj.payment_status,
        startDate,
        endDate,
        dueDate,
        loanAccountObj.owner_id,
        constant.config.createdBy,
        obj.id,
        obj.payment_priority,
          null,
          null,
      ])
    }
    await Promise.all([
      billOnDueRepo.insBatchBillOnDue(listRecords),
      installmentRepo.updateInsClosed(global.poolWrite, listIds),
      loanAccountRepo.updateToCollect({ toCollect, loanId: loanAccountObj.loan_id })
    ])

    return { statusCode: 200, code: 0, message: 'Tao bill on due thanh cong' }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { statusCode: error.statusCode || 500, code: 99, message: error.message }
  }
}

const createBillOnDueFactoring = async function (payload) {
  try {
    console.log('req body createBillOnDueFactoring: ', JSON.stringify(payload))
    if (
      payload == undefined ||
      (payload.contractNumber == undefined && payload.debtAckContractNumber == undefined) ||
      payload.onDueDate == null
    ) {
      return { statusCode: 400, code: 1, message: 'Chua nhap du lieu dau vao' }
    }
    const rsLoanDebtAck = await loanAccountRepo.getLoanAccByDebtAckContract(global.poolRead, payload)
    if (rsLoanDebtAck.rowCount == 0) {
      return { statusCode: 200, code: 3, message: 'KUNN not found' }
    }
    const rsBillOnDue = await billOnDueRepo.getBillByContractAndOnDueDate(global.poolRead, payload)

    if (rsBillOnDue.rowCount > 0) {
      return { statusCode: 200, code: 1, message: 'Hoa don cua ngay da duoc tao' }
    }
    const rsInsNotClosed = await installmentRepo.getInstallmentNotClosedFactoring({
      debtAckContractNumber: payload.debtAckContractNumber,
      endDate: payload.onDueDate
    })
    if (rsInsNotClosed.rowCount == 0) {
      return { statusCode: 200, code: 2, message: 'Ngay khong ton tai ky bill' }
    }
    const loanAccountObj = rsLoanDebtAck.rows[0]

    const listRecords = []
    const listIds = []
    let toCollect = 0
    for (const i in rsInsNotClosed.rows) {
      const obj = rsInsNotClosed.rows[i]
      toCollect += Number(obj.remain_amount)
      const startDate = common.formatDate({ date: obj.start_date })
      const endDate = common.formatDate({ date: obj.end_date })
      const dueDate = common.formatDate({ date: obj.due_date })
      listIds.push(obj.id)

      let acceptPaymentDate = null
      if (obj.type === constant.INSTALLMENT.TYPE.PRIN) {
        acceptPaymentDate = common.formatDate({ date: payload.onDueDate })
      } else {
        acceptPaymentDate = common.formatDate({ date: obj.accept_payment_date })
      }
      let invoiced_date = common.formatDate({ date: obj.invoiced_date })

      listRecords.push([
        obj.contract_number,
        obj.debt_ack_contract_number,
        obj.remain_amount,
        obj.remain_amount,
        obj.type,
        obj.num_cycle,
        endDate,
        obj.payment_status,
        startDate,
        endDate,
        dueDate,
        loanAccountObj.owner_id,
        constant.config.createdBy,
        obj.id,
        obj.payment_priority,
        acceptPaymentDate,
        invoiced_date
      ])
    }
    await Promise.all([
      billOnDueRepo.insBatchBillOnDue(listRecords),
      installmentRepo.updateInsClosed(global.poolWrite, listIds),
      loanAccountRepo.updateToCollect({ toCollect, loanId: loanAccountObj.loan_id })
    ])

    return { statusCode: 200, code: 0, message: 'Tao bill on due factoring thanh cong' }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { statusCode: error.statusCode || 500, code: 99, message: error.message }
  }
}

async function insertTranLog(loanAccObj, amt) {
  const dateNow = moment().toDate()
  const pl = {
    contractNumber: loanAccObj.contract_number,
    loanId: loanAccObj.loan_id,
    tranType: '',
    amtNumber: amt,
    tranDate: dateNow,
    valueDate: dateNow,
    tranDesc: 'ACCURAL_INTEREST',
    createdUser: global.createdBy,
    sessionId: dateNow.getTime(),
    ownerId: constant.config.ownerId
  }
  const rs = await tranLogRepo.insTranLog(global.poolWrite, pl)
  if (rs.rowCount > 0) {
    return rs.rows[0]
  } else {
    return {}
  }
}

const billRepaymentFlow = async function (debtAckContractNumber, date, isJob = true) {
  const startTime = new Date()
  const result = await doCreateBillOnDue({ debtAckContractNumber, onDueDate: date })
  if (result.code == 0) {
    await repaymentService.doRepayment({
      debtAckContractNumber,
      paymentDate: date
    })
  }
  const endTime = new Date()
  const payloadInsertLog = {
    productType: 'MCC',
    startDate: startTime,
    runAt: startTime,
    endDate: endTime,
    batchDate: date,
    jobName: 'batch_process_bill_on_due',
    status: 'bill_start',
    jobId: common.formatDate({ date, format: constant.DATE_FORMAT.YYYYMMDD3 }),
    errorCode: result.code,
    errorMessage: result.message,
    debtAckContractNumber
  }
  if (isJob) {
    batchProcessDetailRepo.insertBatchProcessDetail(payloadInsertLog)
  }
}

const billPayment = async function (body) {
  try {
    const amount = body.amount
    const debtAckContractNumber = body.debtAckContractNumber
    const effectStartDate = body.effectStartDate
    if (!amount || !debtAckContractNumber || !effectStartDate) {
      return { code: 1, message: '[LMS-MC] Thieu du lieu dau vao', statusCode: 400 }
    }
    const amountCompare = await billOnDueRepo.checkCreateAnnex(amount, debtAckContractNumber)
    if (amountCompare <= 0) {
      return {
        statusCode: 200,
        code: 0,
        message: '[LMS-MC] billPayment successful'
      }
    }
    const interestCurrent = await billOnDueRepo.calculateInterestCurrentDate(debtAckContractNumber)
    if (!interestCurrent) {
      return {
        statusCode: 400,
        code: 1,
        message: '[LMS-MC] Có lỗi xảy ra, xin vui lòng thử lại sau'
      }
    }
    let annexAmount = (amountCompare - interestCurrent).toFixed(0)
    let redunAmount = 0
    if (annexAmount % 1000000 != 0) {
      const numberString = annexAmount.toString()
      const firstChar = numberString.charAt(0)
      const stringlength = numberString.length
      const stringRound = `${firstChar}`.padEnd(stringlength, '0')
      const numberRound = parseInt(stringRound)
      redunAmount = annexAmount - numberRound
      annexAmount = numberRound
    }
    const createAnnexRs = await loanAnnexService.simulationLoanAnnexV2({
      ...body,
      amountAnnex: annexAmount,
      isCreated: true
    })
    if (createAnnexRs?.code == 0 && redunAmount > 0) {
      return {
        code: 0,
        message: `[LMS-MC] Số tiền hệ thống đóng cho bạn vào kỳ này là ${annexAmount}, số tiền ${redunAmount} sẽ được dữ để thanh toán vào kỳ sau`,
        statusCode: 200
      }
    }
    return createAnnexRs
  } catch (e) {
    console.error('Error at billPayment: ', e?.message)
    console.log(e)
    return { statusCode: 500, code: -1, message: e?.message }
  }
}
module.exports = {
  doCreateBillOnDue,
  insertTranLog,
  billPayment,
  billRepaymentFlow,
}
