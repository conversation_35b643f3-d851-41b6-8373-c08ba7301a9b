const welcomePackageRepo = require('../repositories/welcome-package-repo')
const installmentRepo = require('../repositories/installment-repo')
const {
  WelcomePackageRepository,
  LoanAccountRepository,
  IrChargeRepository,
  LoanContractLimitRepository
} = require('../repositories-v2')
const s3Service = require('../utils/s3-service')
const common = require('../utils/common')
const constant = require('../utils/constant')
const url = require('url')
const crmService = require('../other-services/crm-service')
const losService = require('../other-services/los-service')
const losCashLoanService = require('../other-services/los-cashloan')
const installmentService = require('../services/installment-service')
const moment = require('moment')
const axios = require('axios')
const { isPrinEveryCycle, isLosUnitedPartnerCode, isCashLoanPartnerCode, readTemplateFile } = require('../utils/helper')
const { In } = require('../utils/find-operator')
const lodash = require('lodash')

const getWelcomePackage = async function (req, res) {
  try {
    const input = req.query
    if (!input || !input.contractNumber || !input.authCode) {
      return res.status(400).json((res.body = { code: 1, message: 'input invalid' }))
    }

    const wclList = await welcomePackageRepo.findByContractNumber(global.poolRead, input.contractNumber, input.authCode)
    if (wclList.rows.length === 0) {
      return res.status(200).json((res.body = { code: 2, message: 'welcome package not exists || auth code is valid' }))
    }

    const unsignedFileUrl = wclList.rows?.[0].url
    const urlParse = new url.URL(unsignedFileUrl)
    const unsignedFileKey = urlParse.pathname.slice(1)

    s3Service
      .download(unsignedFileKey)
      .then((buffer) => {
        // console.log(buffer)
        const fileData = Buffer.from(buffer.Body).toString('base64')
        res.body = { code: 200, message: 'Success' }
        return res.status(200).send(fileData)
      })
      .catch((error) => {
        console.log(error)
        res.status(500).json((res.body = { message: error.message, command: error }))
      })
  } catch (error) {
    console.log(error)
    console.error('Error while getWelcomePackage', error.message)
    res.status(500).json((res.body = { message: error.message, code: -1 }))
  }
}
const getWelcomePackageSMA = async function (req, res) {
  try {
    const input = req.query
    if (!input || !input.contractNumber) return res.status(400).json((res.body = { code: 1, message: 'input invalid' }))

    const wclList = await welcomePackageRepo.findByContractNumberV2(input.contractNumber)
    if (wclList.rows.length === 0) {
      return res.status(200).json((res.body = { code: 2, message: 'welcome package not exists || auth code is valid' }))
    }

    const unsignedFileUrl = wclList.rows?.[0].url
    const urlParse = new url.URL(unsignedFileUrl)
    const unsignedFileKey = urlParse.pathname.slice(1)

    s3Service
      .download(unsignedFileKey)
      .then((buffer) => {
        res.body = { code: 200, message: 'Success' }
        return res.status(200).send(buffer.Body)
      })
      .catch((error) => {
        console.log(error)
        res.status(500).json((res.body = { message: error.message, command: error }))
      })
  } catch (error) {
    console.log(error)
    console.error('Error while getWelcomePackageSMA', error.message)
    res.status(500).json((res.body = { message: error.message, code: -1 }))
  }
}

async function generateWelcomePackage(loanAccObj, listInstallment, custObj, irChargePrinObj) {
  if (isLosUnitedPartnerCode(loanAccObj.partner_code)) {
    return generateWelcomePackageCashLoan(loanAccObj, listInstallment, custObj, irChargePrinObj)
  } else {
    return generateWelcomePackageMerchant(loanAccObj, listInstallment, custObj, irChargePrinObj)
  }
}

async function generateWelcomePackageMerchant(loanAccObj, listInstallment, custObj, irChargePrinObj) {
  const jsonBody = {
    total_cycle: listInstallment.length,
    contract_data: {}
  }
  jsonBody.contract_data.customer_name = custObj.fullName
  jsonBody.contract_data.cus = custObj.fullName
  jsonBody.contract_data.address_cur = custObj.addressCur ? custObj.addressCur : ''
  jsonBody.contract_data.phone = custObj.phoneNumber1
  jsonBody.contract_data.current_date = common.convertDatetoString(moment().toDate(), 'dd-mm-yyyy')
  jsonBody.contract_data.contract_number = loanAccObj.debt_ack_contract_number
  jsonBody.contract_data.contract_number_parent = loanAccObj.contract_number
  jsonBody.contract_data.cn = loanAccObj.debt_ack_contract_number
  jsonBody.contract_data.co_nu = loanAccObj.debt_ack_contract_number
  jsonBody.contract_data.amount = common.convertCurrency(Number(loanAccObj.apr_limit_amt))
  jsonBody.contract_data.bill_day = loanAccObj.bill_day
  jsonBody.contract_data.id_number = custObj.idNumber
  jsonBody.contract_data.loan_purpose = ''
  jsonBody.contract_data.loan_amount = common.convertCurrency(Number(loanAccObj.apr_limit_amt))
  jsonBody.contract_data.loan_amt = Number(loanAccObj.apr_limit_amt)
  jsonBody.contract_data.rate = irChargePrinObj.ir_value * 100
  jsonBody.contract_data.tenor = loanAccObj.tenor
  jsonBody.contract_data.product_code = loanAccObj.product_code
  jsonBody.contract_data.product_name = loanAccObj.product_code
  jsonBody.contract_data.active_date = common.convertDatetoString(loanAccObj.active_date, 'dd-mm-yyyy')
  jsonBody.contract_data.end_date = common.convertDatetoString(loanAccObj.end_date, 'dd-mm-yyyy')
  jsonBody.contract_data.insm = []
  for (const i in listInstallment) {
    jsonBody.contract_data.insm.push({
      cycle: listInstallment[i].numCycle,
      bill_date: common.convertDatetoString(listInstallment[i].endDate, 'dd-mm-yyyy'),
      total: common.convertCurrency(listInstallment[i].totalCycle || 0),
      prin_amt: common.convertCurrency(listInstallment[i].capitalRefunded),
      int_amt: common.convertCurrency(listInstallment[i].interest || 0),
      fee_amt: common.convertCurrency(listInstallment[i].feeAmount || 0),
      remain_prin: common.convertCurrency(listInstallment[i].remainPrinAmount)
    })
    if (listInstallment[i].numCycle == listInstallment.length) {
      jsonBody.contract_data.amt_e = common.convertCurrency(listInstallment[i].totalCycle || 0)
    }
    if (listInstallment[i].numCycle == 1) {
      jsonBody.contract_data.first_date = common.convertDatetoString(listInstallment[i].endDate, 'dd-mm-yyyy')
    }
  }
  const fileWp = await losService.genCalculatorRepayment(jsonBody)

  return {
    fileWp,
    jsonBody
  }
}

async function generateWelcomePackageCashLoan(loanAccObj, listInstallment, custObj, irChargePrinObj) {
  const contract_data = {
    insm: [],
    address: '',
    customer_name: custObj.customerName || custObj.fullName,
    cust_name: custObj.customerName || custObj.fullName,
    phone_number: custObj.phoneNumber || custObj.phoneNumber1 || custObj.phoneNumber2 || custObj.phoneNumber3
  }
  const [contractInfo, loanContractLimit, listLoanAcc] = await Promise.all([
    losService.getContractInfo(loanAccObj.debt_ack_contract_number),
    LoanContractLimitRepository.findOne({ where: { contract_number: loanAccObj.contract_number } }),
    LoanAccountRepository.findAll({
      where: {
        contract_number: loanAccObj.contract_number,
        status: In([constant.DEBT_ACK_STATUS.ACTIVE, constant.DEBT_ACK_STATUS.SIG])
      }
    })
  ])
  for (const installment of listInstallment) {
    if (installment.numCycle == 1) {
      contract_data.first_due_date = common.convertDatetoString(installment.dueDate, 'dd/mm/yyyy')
      contract_data.monthy_amt = common.convertCurrency(
        Number(installment.capitalRefunded) + Number(installment.interest) + Number(installment.feeAmount)
      )
      contract_data.mont_amt = common.convertCurrency(
        Number(installment.capitalRefunded) + Number(installment.interest) + Number(installment.feeAmount)
      )
      contract_data.delta_month = installment.dueDate.getDate()
      contract_data.delta_m = installment.dueDate.getDate()
    }
    if (installment.numCycle == loanAccObj.tenor) {
      contract_data.end_date = common.convertDatetoString(installment.dueDate, 'dd/mm/yyyy')
      contract_data.last_amt = common.convertCurrency(
        Number(installment.capitalRefunded) + Number(installment.interest) + Number(installment.feeAmount)
      )
    }
    contract_data.insm.push({
      idx: installment.numCycle,
      due_date: common.convertDatetoString(installment.endDate, 'dd-mm-yyyy'),
      total_ins: common.convertCurrency(installment.totalCycle || 0),
      prin: common.convertCurrency(installment.capitalRefunded),
      int: common.convertCurrency(installment.interest || 0),
      fee: common.convertCurrency(installment.feeAmount || 0),
      out_prin: common.convertCurrency(installment.remainPrinAmount)
    })
  }
  contract_data.limit_amt = Number(loanContractLimit.apr_limit_amt)
  contract_data.limit_amt_used =
    lodash.sumBy(listLoanAcc, function (item) {
      return Number(item.apr_limit_amt)
    }) - Number(loanAccObj.apr_limit_amt)
  contract_data.remain_limit_amt =
    loanContractLimit.apr_limit_amt - contract_data.limit_amt_used - Number(loanAccObj.apr_limit_amt)
    const isNewAddress = !!custObj.currentNewWard;
    if(isNewAddress) {
        contract_data.address = [custObj.addressCur, custObj.currentNewWard, custObj.currentNewProvince].join(', ');
    } else {
        contract_data.address += custObj.addressCur ? ', ' + custObj.addressCur : ''
        contract_data.address += custObj.wardCur ? ', ' + custObj.wardCur : ''
        contract_data.address += custObj.districtCur ? ', ' + custObj.districtCur : ''
        contract_data.address += custObj.provinceCur ? ', ' + custObj.provinceCur : ''
        contract_data.address = contract_data.address.substring(1, contract_data.address.length)
    }
  contract_data.current_date = common.convertDatetoString(moment().toDate(), 'dd-mm-yyyy')
  contract_data.contract_number = loanAccObj.debt_ack_contract_number
  contract_data.contract_number_limit = loanAccObj.contract_number
  contract_data.contr_num = loanAccObj.debt_ack_contract_number
  contract_data.loan_amt = Number(loanAccObj.apr_limit_amt)
  contract_data.id_number = custObj.idCard || custObj.idNumber
  contract_data.loan_purpose = contractInfo?.loanPurposeText || ''
  contract_data.tenor = loanAccObj.tenor
  contract_data.loan_amount = common.convertCurrency(Number(loanAccObj.apr_limit_amt))
  contract_data.rate = parseFloat((irChargePrinObj.ir_value * 100).toFixed(2))
  contract_data.tenor = loanAccObj.tenor
  contract_data.product_code = loanAccObj.product_code
  contract_data.product_name = 'Cho Vay Tiền Mặt'
  contract_data.active_date = common.convertDatetoString(loanAccObj.active_date, 'dd-mm-yyyy')
  contract_data.end_date = common.convertDatetoString(loanAccObj.end_date, 'dd-mm-yyyy')
  contract_data.company_name = contractInfo?.companyName || ''
  contract_data.company_address = contractInfo?.companyAddress || ''
  contract_data.company_full_address = contractInfo?.companyFullAddress || ''
  contract_data.limit_amt_used = common.convertCurrency(contract_data.limit_amt_used)
  contract_data.remain_limit_amt = common.convertCurrency(contract_data.remain_limit_amt)
  contract_data.limit_amt = common.convertCurrency(contract_data.limit_amt)
  const fileWp = await losCashLoanService.genCalculatorRepayment(contract_data)

  return {
    fileWp,
    jsonBody: contract_data
  }
}

async function getEmailTemplatePath(loanAccObj) {
  if ([constant.PARTNER_CODE.DNSE].includes(loanAccObj.partner_code)) {
    return `welcome_package_dnse.html`
  }

  return global.config?.data?.wcl?.partnerSme.split(',').includes(loanAccObj.partner_code)
      ? `welcome_package_sme_v2.html` : `welcome_package_ec.html`;
}
async function sendEmailWelcome(loanAccObj, disburmentObj, rsInsmIn, irChargePrinObj) {
  try {
    const custObj = await crmService.getCustomerInfo(
      global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj.cust_id,
      {}
    )

    const listInstallment = installmentService.processListInstallment(
      rsInsmIn,
      irChargePrinObj.ir_value,
      isPrinEveryCycle(loanAccObj)
    )

    const { fileWp, jsonBody } = await generateWelcomePackage(loanAccObj, listInstallment, custObj, irChargePrinObj)

    const authCode = common.genAuthCode(8)

    welcomePackageRepo.insert(global.poolWrite, {
      contractNumber: loanAccObj.debt_ack_contract_number,
      bodyData: jsonBody,
      url: fileWp,
      createdUser: global.createdUser,
      ownerId: constant.config.ownerId,
      phoneNumber: custObj.phoneNumber1,
      authCode
    })
    if (fileWp && !isCashLoanPartnerCode(loanAccObj.partner_code)) {
      const image = await axios.get(fileWp, { responseType: 'arraybuffer' })
      const returnedB64 = Buffer.from(image.data).toString('base64')

      const templatePath = await getEmailTemplatePath(loanAccObj.partner_code);

      let  html = readTemplateFile(templatePath);
      html= html.replace('${custObj.fullName}',custObj.fullName);
      html= html.replace('${loanAccObj.debt_ack_contract_number}',loanAccObj.debt_ack_contract_number);
      html= html.replace('${custObj.companyName}', custObj.companyName);

      const reqBodyEmail = {
        receiver: disburmentObj.email
          ? disburmentObj.email + ',' + global.config?.data?.wcl?.mailList
          : global.config?.data?.wcl?.mailList,
        subject: 'KHẾ ƯỚC NHẬN NỢ SỐ: ' + loanAccObj.debt_ack_contract_number + '- LỊCH THANH TOÁN',
        html: html,
        attachment: {
          fileName: 'Lịch thanh toán ' + loanAccObj.debt_ack_contract_number + '.pdf',
          content: returnedB64
        }
      }
      console.log('reqBodyEmail.receiver: ', reqBodyEmail.receiver)
      common
        .postAPI(global.bssEmailUrl, reqBodyEmail, undefined, { 'Content-Type': 'application/json' })
        .then((data) => {
          console.log('[LMS-MC] Response call send email: ', JSON.stringify(data))
        })
        .catch((err) => {
          console.log('[LMS-MC] Error call send email: ', err)
        })
    }
  } catch (e) {
    console.log('[LMS-MC] Error sendEmailWelcome: ', e)
  }
}

async function sendEmailWelcomeV2(loanAccObj, disburmentObj, rsInsmIn, irChargePrinObj) {
  try {
    const custObj = await crmService.getCustomerInfo(
      global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj.cust_id,
      {}
    )

    const listInstallment = installmentService.processListInstallment(
      rsInsmIn,
      irChargePrinObj.ir_value,
      isPrinEveryCycle(loanAccObj)
    )

    const { fileWp, jsonBody } = await generateWelcomePackage(loanAccObj, listInstallment, custObj, irChargePrinObj)

    const authCode = common.genAuthCode(8)

    welcomePackageRepo.insert(global.poolWrite, {
      contractNumber: loanAccObj.debt_ack_contract_number,
      bodyData: jsonBody,
      url: fileWp,
      createdUser: global.createdUser,
      ownerId: constant.config.ownerId,
      phoneNumber: custObj.phoneNumber1,
      authCode
    })
  } catch (e) {
    console.log('[LMS-MC] Error sendEmailWelcomeV2: ', e)
  }
}

const getWelcomePackageV2 = async function (req, res) {
  try {
    const input = req.query
    if (!input || !input.contractNumber) {
      return res.status(400).json((res.body = { code: 1, message: 'input invalid' }))
    }
    const [welcomePackageObj, loanAccObj] = await Promise.all([
      WelcomePackageRepository.findOne({ where: { contract_number: input.contractNumber } }),
      LoanAccountRepository.findOne({ where: { debt_ack_contract_number: input.contractNumber } })
    ])

    if (!welcomePackageObj || !loanAccObj) {
      return res
        .status(200)
        .json((res.body = { code: 2, message: 'welcome package not exists or loan account not exist' }))
    }

    if (!welcomePackageObj.url) {
      const [custObj, rsInsmIn, irChargePrinObj] = await Promise.all([
        crmService.getCustomerInfo(
          global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccObj.cust_id
        ),
        installmentRepo.getInsmInfo(global.poolRead, { debtAckContractNumber: input.contractNumber }),
        IrChargeRepository.findOne({
          where: {
            debt_ack_contract_number: input.contractNumber,
            ir_type: constant.IR_CHARGE_TYPE.ON_DUE_PRIN,
            status: 1
          }
        })
      ])
      const listInstallment = installmentService.processListInstallment(
        rsInsmIn,
        irChargePrinObj.ir_value,
        isPrinEveryCycle(loanAccObj)
      )
      const { fileWp, jsonBody } = await generateWelcomePackage(loanAccObj, listInstallment, custObj, irChargePrinObj)
      welcomePackageObj.url = fileWp
      welcomePackageObj.body_data = jsonBody

      await welcomePackageObj.save()
    }

    const unsignedFileUrl = welcomePackageObj.url
    const urlParse = new url.URL(unsignedFileUrl)
    const unsignedFileKey = urlParse.pathname.slice(1)

    s3Service
      .download(unsignedFileKey)
      .then((buffer) => {
        // console.log(buffer)
        const fileData = Buffer.from(buffer.Body).toString('base64')
        res.body = { code: 200, message: 'Success' }
        return res.status(200).send(fileData)
      })
      .catch((error) => {
        console.log(error)
        res.status(500).json((res.body = { message: error.message, command: error }))
      })
  } catch (error) {
    console.log(error)
    console.error('Error while getWelcomePackage', error.message)
    res.status(500).json((res.body = { message: error.message, code: -1 }))
  }
}

module.exports = {
  getWelcomePackageSMA,
  getWelcomePackage,
  sendEmailWelcome,
  sendEmailWelcomeV2,
  getWelcomePackageV2
}
