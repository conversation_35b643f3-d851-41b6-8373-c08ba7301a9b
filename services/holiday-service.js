const holidayRepo = require('../repositories/holiday-repo')
const moment = require('moment')
const constant = require('../utils/constant')

async function importHoliday(payload) {
  !payload.createdBy && (payload.createdBy = constant.config.createdBy)
  const { listHoliday, createdBy } = payload
  if (!Array.isArray(listHoliday) || !listHoliday.length) {
    return {
      statusCode: 400,
      code: 1,
      message: 'listHoliday must be not empty array'
    }
  }
  holidayRepo.insertListHoliday(listHoliday, createdBy)
  return {
    statusCode: 200,
    code: 0,
    message: 'Import holiday success'
  }
}
function getNextDayAfterHoliday(dateCheck) {
  dateCheck = moment(dateCheck).format(constant.DATE_FORMAT.YYYYMMDD2)
  const listHolidayDate = global.listHoliday
  const dateCheckTmp = new Date(dateCheck)

  while (true) {
    const formatDateCheck = moment(dateCheckTmp).format(constant.DATE_FORMAT.YYYYMMDD2)
    if ([0, 6].includes(dateCheckTmp.getDay()) || listHolidayDate.includes(formatDateCheck)) {
      // check weekends or holiday
      dateCheckTmp.setDate(dateCheckTmp.getDate() + 1)
    } else {
      return formatDateCheck
    }
  }
}
module.exports = {
  importHoliday,
  getNextDayAfterHoliday
}
