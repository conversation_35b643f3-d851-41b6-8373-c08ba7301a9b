const common = require('../utils/common')
const constant = require('../utils/constant')
const paymentRepo = require('../repositories/payment-repo')
const disburmentRepo = require('../repositories/disbursement-repo')

const getListTransaction = async function (req, res) {
  try {
    const payload = req.query
    if (!payload || (!payload.partnerCode && !payload.transactionDate && !payload.mode)) {
      res.body = { code: -1, message: '[LMS-MCC] Ban phai nhap it 1 truong du lieu' }
      return res.status(400).json(res.body)
    }
    payload.fromDate = payload.transactionDate
    payload.toDate = common.convertDatetoString(
      new Date(payload.transactionDate).setDate(new Date(payload.transactionDate).getDate() + 1),
      constant.DATE_FORMAT.YYYYMMDD
    )
    const listFuncs = await Promise.all([
      paymentRepo.findAllReconcili(global.poolRead, payload),
      disburmentRepo.findAllReconcili(global.poolRead, payload)
    ])
    const listPayment = listFuncs[0].rows || []
    const listDisbur = listFuncs[1].rows || []
    const listResult = []

    for (const i in listPayment) {
      listResult.push({
        contractNumber: listPayment[i].debt_ack_contract_number,
        transactionId: listPayment[i].partner_tran_no,
        paymentDate: common.convertDatetoString(listPayment[i].payment_date, constant.DATE_FORMAT.DDMMYYYY2),
        amount: Number(listPayment[i].installment_amort)
      })
    }
    if (payload.transactionType && payload.transactionType == constant.TRANS_TYPE.OUT) {
      listDisbur.forEach((element) => {
        listResult.push({
          contractNumber: element.debt_ack_contract_number,
          transactionId: element.partner_tran_no,
          paymentDate: common.convertDatetoString(element.tran_date, constant.DATE_FORMAT.DDMMYYYY2),
          amount: Number(element.amt)
        })
      })
    }
    res.status(200).json((res.body = { code: 200, message: '[LMS-MCC] Lay thong tin thanh cong', data: listResult }))
  } catch (err) {
    console.log(err)
    res.status(500).json((res.body = { code: 99, message: err.message }))
  }
}

const reconciledResult = async function (req, res) {
  try {
    const payload = req.body
    if (!payload || !payload.partnerCode || !payload.transactionList || payload.transactionList.length === 0) {
      res.body = { code: -1, message: '[LMS-MCC] Thieu du lieu dau vao' }
      return res.status(400).json(res.body)
    }
    if (payload.date) {
      payload.date = new Date(payload.date)
    } else {
      payload.date = new Date()
    }
    const errorList = []
    const warringList = []
    const payIds = []
    const transactionList = payload.transactionList
    for (const i in transactionList) {
      const rs = await Promise.all([
        paymentRepo.findAllReconcili(global.poolRead, transactionList[i]),
        disburmentRepo.findAllReconcili(global.poolRead, transactionList[i])
      ])
      // console.log({rs})
      const arr = []
      rs[0].rows.forEach((element) => {
        arr.push(element)
      })
      rs[1].rows.forEach((element) => {
        arr.push(element)
      })
      if (arr.length === 0) {
        errorList.push({
          contractNumber: transactionList[i].contractNumber,
          transactionId: transactionList[i].transactionId,
          note: 'Not found transaction reconciled'
        })
      } else if (arr[0].payment_reconciled == 1) {
        warringList.push({
          contractNumber: transactionList[i].contractNumber,
          transactionId: transactionList[i].transactionId,
          note: 'Transaction is reconciled'
        })
      } else {
        payIds.push(arr[0].partner_tran_no)
      }
    }
    if (payIds.length > 0) {
      await paymentRepo.updatePaymentReconciled(global.poolWrite, payIds)
      await disburmentRepo.updatePaymentReconciled(global.poolWrite, payIds)
    }
    res.status(200).json(
      (res.body = {
        code: 200,
        message: '[LMS-MCC] Success',
        data: {
          partnerCode: payload.partnerCode,
          errorList,
          warringList
        }
      })
    )
  } catch (err) {
    // common.logError(serviceName, req.originalUrl, err)
    console.log(err)
    res.status(err.status || 500).json((res.body = { code: 99, message: err.message, command: err }))
  }
}

module.exports = {
  getListTransaction,
  reconciledResult
}
