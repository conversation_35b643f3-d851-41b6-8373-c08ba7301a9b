const { formatDate, logError } = require('../utils/common')
const constant = require('../utils/constant')
const requestLogRepo = require('../repositories/request-log-repo')

async function saveRequestLog(req, res) {
  try {
    let _req = req.body
    const reqParams = req.params || {}
    const reqQuery = req.query
    if (req.method == 'GET') {
      _req = req.query
    }
    const api = req.baseUrl + req.route ? req.route.path : req.originalUrl
    const eventType = req.eventType || api
    const now = formatDate({ format: constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS })
    const contractNumber =
      _req.debtAckContractNumber ||
      _req.debt_ack_contract_number ||
      reqParams.debtAckContractNumber ||
      _req.contractNumber ||
      _req.contract_number ||
      reqParams.contractNumber ||
      reqQuery.contractNumber ||
      reqQuery.contract_number
    const sendData = {
      api,
      eventType,
      contractNumber,
      custId: _req.custId || _req.cust_id,
      serviceType: 1,
      tranTime: _req.date || now,
      method: req.method,
      requestBody: JSON.stringify(_req),
      requestTime: req.requestTime,
      createdDate: now,
      updatedDate: now,
      createdUser: global.createdUser,
      ownerId: global.ownerId,
      response: JSON.stringify(res.body),
      responseCode: res.body ? res.body.code : -1,
      statusCode: res.statusCode,
      responseTime: now,
      requestParams: JSON.stringify(reqParams),
      requestQuery: JSON.stringify(reqQuery),
      requestHeaders: JSON.stringify(req.headers)
    }
    await requestLogRepo.insert(global.poolWrite, sendData)
  } catch (e) {
    console.log(e)
    logError(`Error at Write log ${req.originalUrl}`)
  }
}
module.exports = {
  saveRequestLog
}
