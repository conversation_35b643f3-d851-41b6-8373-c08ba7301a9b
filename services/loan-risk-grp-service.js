/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2022-05-30 13:13:12
 * @modify date 2022-05-30 13:13:12
 * @desc [service for handle dpd and risk group]
 */
const loanAccountRepo = require('./../repositories/loan-account-repo')
const loanRgRepo = require('./../repositories/loan-risk-grp-repo')
const custRgRepo = require('./../repositories/cust-risk-grp-repo')
const contractRgRepo = require('./../repositories/contract-risk-grp-repo')
// const producerService = require('./producer')
const { RISK_GROUP, DEFAULT_RG } = require('../utils/constant')
const crmService = require('../other-services/crm-service')

async function createUserRg(debtAckContractNumber, userRgValue, valueDate) {
  const findLoanAcc = await loanAccountRepo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)

  if (!findLoanAcc.length) {
    return {
      message: 'Contract number not found',
      code: 401
    }
  }

  const loanAccount = findLoanAcc[0]
  const custId = loanAccount.cust_id
  const contractNumber = loanAccount.contract_number

  valueDate = valueDate ? new Date(valueDate) : new Date()

  const [lastDpdRgList, listContractOfCustomer, lastCicRgList, listContractRg] = await Promise.all([
    loanRgRepo.findLastestContractRgTypeByDebtAckContractNumber(debtAckContractNumber, RISK_GROUP.DPD_RG),
    loanAccountRepo.findListContractByCustId(custId),
    custRgRepo.findLastestCustRgTypeByCustId(custId, RISK_GROUP.CIC_RG),
    contractRgRepo.findContractRiskGrpByCustId(custId)
  ])

  const lastDpdRg = lastDpdRgList.length ? lastDpdRgList[0] : null

  let indRgValue = userRgValue
  let finRgValue
  const contractRg = listContractRg.find((item) => item.debt_ack_contract_number == debtAckContractNumber)

  if (contractRg) {
    for (const contractRgEle of listContractRg) {
      const { obs_risk_grp, cont_risk_grp } = contractRgEle

      const maxObsConRg = Math.max(obs_risk_grp, cont_risk_grp)

      indRgValue = Math.max(indRgValue, maxObsConRg)
    }
    finRgValue = Math.max(indRgValue, contractRg.cic_risk_grp)
  } else {
    for (const contract of listContractOfCustomer) {
      const { debt_ack_contract_number } = contract

      const lastObsRgList = await loanRgRepo.findLastestContractRgTypeByDebtAckContractNumber(
        debt_ack_contract_number,
        RISK_GROUP.OBSER_RG
      )
      const lastObsRgVal = lastObsRgList.length ? lastObsRgList[0].risk_grp_val : DEFAULT_RG

      const lastConRgList = await loanRgRepo.findLastestContractRgTypeByDebtAckContractNumber(
        debt_ack_contract_number,
        RISK_GROUP.CONTRACT_RG
      )
      const lastConRgVal = lastConRgList.length ? lastConRgList[0].risk_grp_val : DEFAULT_RG

      const maxObsConRg = Math.max(lastObsRgVal, lastConRgVal)

      indRgValue = Math.max(indRgValue, maxObsConRg)
    }
    let lastCicRgVal = DEFAULT_RG

    if (lastCicRgList.length) {
      const lastCicRg = lastCicRgList[0]
      const endCicRgDate = new Date(lastCicRg.risk_date)
      endCicRgDate.setMonth(endCicRgDate.getMonth() + 1)
      if (valueDate < endCicRgDate) {
        lastCicRgVal = lastCicRg.risk_grp_val
      }
    }
    finRgValue = Math.max(indRgValue, lastCicRgVal)
  }
  const dpd = lastDpdRg?.dpd || loanAccount.dpd || contractRg?.dpd

  const dataInsertContractRg = {
    contractNumber,
    debtAckContractNumber,
    custId,
    dpd,
    userRgValue,
    conRgValue: userRgValue,
    riskDate: valueDate
  }

  const dataInsertCustRg = {
    custId,
    dpd,
    indRgValue,
    finRgValue,
    riskDate: valueDate
  }
  const dataUpdateContractRg = {
    contractNumber,
    debtAckContractNumber,
    indRgValue,
    finRgValue,
    userRgValue,
    conRgValue: userRgValue
  }

  if (contractRg) {
    contractRgRepo.updateContractRgByDebtAckContractNumber(dataUpdateContractRg)
  }

  loanRgRepo.insertManyContractRg(dataInsertContractRg)

  custRgRepo.insertIndAndCicAndFinalRg(dataInsertCustRg)

  // console.log('crm-dpd-rg: ', JSON.stringify(dataQueue))
  // producerService.sendMessageV2(dataQueue, 'crm-dpd-rg')
  // crmService.sendQueueDpdRiskGrp(dataQueue)

  return {
    code: 201,
    message: 'Created user risk group successfully'
  }
}

module.exports = {
  createUserRg
}
