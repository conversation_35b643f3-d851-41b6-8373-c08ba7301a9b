const common = require('../utils/common')
const irRepo = require('../repositories/ir-repo')
const installmentRepo = require('../repositories/installment-repo')
const debtActContractRepo = require('../repositories/debt-ack-contract-repo')
const irChargeRepo = require('../repositories/ir-charge-repo')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const constant = require('../utils/constant')
const crmService = require('../other-services/crm-service')
const productService = require('../other-services/product-service')
const tranLogRepo = require('../repositories/tran-log-repo')
const contractRiskGrpRepo = require('../repositories/contract-risk-grp-repo')
const moment = require('moment')
const loanAccountRepo = require('../repositories/loan-account-repo')
const paymentRepo = require('../repositories/payment-repo')
const _ = require('lodash')
const { getDueDateCalLpi } = require('../utils/helper')
const { Add } = require('../utils/set-operator')
const { InstallmentRepository, BillOnDueRepository, IrRepository } = require('../repositories-v2')
const loanAmortRepo = require('../repositories/loan-amort-repo')
const feeRepo = require('../repositories/fees-repo')
const LoanOrderFeesRepository = require('../repositories-v2/loan-order-fees.repository')
const { LoanAccountOrdersEntity } = require('../entities/loan-account-orders.entity')

const calcuIrEveryDayRefactoring = async function (payload) {
  try {
    common.log('req body calcuIrEveryDayRefactoring: ' + JSON.stringify(payload))
    if (payload.debtAckContractNumber == undefined || payload.irDate == undefined) {
      return {
        statusCode: 200,
        code: 1,
        message: '[MC-LMS] Phai truyen day du thong tin contractNumber va irDate'
      }
    }
    // kiem tra xem ngay irDate da duoc tinh lai hay chua
    const [rsIrDate,rsOrderFee] = await Promise.all([ 
      irRepo.findByContractNumberAndIrDate(payload),
      LoanOrderFeesRepository.findAllBy({ debt_ack_contract_number: payload.debtAckContractNumber, cal_fee_date: payload.irDate, flag_active: constant.LOAN_ORDER_FEE.STATUS.ACTIVE })
    ])

    if (rsIrDate. rowCount > 0 || rsOrderFee.length > 0) {
      return {
        statusCode: 200,
        code: 1,
        message: '[LMS-MC] Ngay irDate truyen vao da duoc tinh lai'
      }
    }
    payload.paymentStatus = 1
    payload.status = 1
    const [listInstallment, listDebtActContract] = await Promise.all([
      installmentRepo.findByContractNumberAndPaymentStatusNotCompleted(global.poolRead, {
        debtAckContractNumber: payload.debtAckContractNumber
      }),
      debtActContractRepo.findByContractNumberAndStatus(global.poolRead, {
        debtAckContractNumber: payload.debtAckContractNumber,
        status: constant.LOAN_ACC_STATUS.ACT
      })
    ])
    if (listInstallment.rowCount == 0 || listDebtActContract.rowCount == 0) {
      return {
        statusCode: 200,
        code: 1,
        message: '[MC-LMS] Khong tim thay KUNN da giai ngan de tinh lai'
      }
    }
    const payloadIr = []
    const payloadFee = []
    const debtAckContractObj = listDebtActContract.rows[0]

    if(!common.isFactoringLoanChannel(debtAckContractObj?.partner_code)){
          return {
            statusCode: 200,
            code: 1,
            message: '[MC-LMS] Khong tinh lai lai hang ngay voi channel khac Factoring'
          }
        }
    if(common.formatDate({ date: payload.irDate }) <= common.formatDate({ date: debtAckContractObj.active_date })) {
      return {
        statusCode: 200,
        code: 1,
        message: '[MC-LMS] Ngay tinh lai bat buoc phai lon hon ngay bat dau hop dong'
      }
    }
    payload.ownerId = debtAckContractObj.owner_id || constant.config.ownerId
    payload.createdBy = debtAckContractObj.created_by || constant.config.createdBy
    payload.isTesting = debtAckContractObj.is_testing || constant.config.isTesting

    const irDate = new Date(payload.irDate)
    const payloadIrCharge = {
      debtAckContractNumber: debtAckContractObj.debt_ack_contract_number,
      productCode: debtAckContractObj.product_code
    }
    const [listIr,listFee] = await Promise.all([
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, payloadIrCharge),
      feeRepo.findByDebtAckAndProductCode({ debtAckContractNumber: debtAckContractObj.debt_ack_contract_number, productCode: debtAckContractObj.product_code })
    ])

    let irRate, irRatePrinOverdue, irRatePrefer, feeRate
    let feeObj;
    if (listFee.rowCount > 0) {
      feeObj = listFee.rows[0];
      if (feeObj.fee_type === constant.FEE.TYPE_CALCULATE.REPAYMENT) {
      feeRate = feeObj;
      }
    }
    for (const irObj of listIr.rows) {
      if (irObj.ir_type == constant.IR_CHARGE_TYPE.ON_DUE_PRIN) {
        irRate = irObj
      } else if (irObj.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_PRIN) {
        irRatePrinOverdue = irObj
      } else if (irObj.ir_type == constant.IR_CHARGE_TYPE.PREFERENTIAL_RATE) {
        irRatePrefer = irObj
      }
    }
    if (!irRate || !irRatePrinOverdue) {
      return {
        statusCode: 200,
        code: 1,
        message: '[MC-LMS] Missing data ircharge'
      }
    }
    const currentPrinObj = listInstallment.rows.find(
      (item) => item.type == constant.INSTALLMENT.TYPE.PRIN && irDate > item.start_date && irDate <= item.end_date
    )
    const currentIrObj = listInstallment.rows.find(
      (item) => item.type == constant.INSTALLMENT.TYPE.INT && irDate > item.start_date && irDate <= item.end_date
    )

    const payloadUpdateDebtAck = {
      lpiAmt: 0,
      toCollect: 0,
      intAmt: 0,
      normalAmt: 0,
      prefIntAmt: 0,
      feeAmt: 0,
      factoringFee: 0,
      loanId: debtAckContractObj.loan_id
    }
    for (const installment of listInstallment.rows) {
      const dueDateCalLpi = getDueDateCalLpi(installment.end_date, installment.due_date)
      const irDateString = common.formatDate({ date: irDate })
      const acceptPaymentDateString = common.formatDate({ date: installment.accept_payment_date })
      if (
        irDateString > dueDateCalLpi &&
        installment.type == constant.INSTALLMENT.TYPE.PRIN &&
        irRatePrinOverdue &&
        Number(irRatePrinOverdue?.ir_value) > 0
      ) {
        await calculateLpiInstallmentFactoring({
          installment,
          payloadUpdateDebtAck,
          irRateOverdueObj: irRatePrinOverdue,
          currentInsmObj: currentPrinObj,
          payload,
          payloadIr,
          lpiType: constant.INSTALLMENT.TYPE.LPI_PRIN
        })
      } else if (
        irDateString > acceptPaymentDateString &&
        installment.type == constant.INSTALLMENT.TYPE.PRIN &&
        irRate &&
        Number(irRate?.ir_value) > 0
      ) {
        await calculateInterestInstallmentFactoring({
          installment,
          payloadUpdateDebtAck,
          irRateObj: irRate,
          currentInsmObj: currentIrObj,
          payload,
          payloadIr,
          intType: constant.INSTALLMENT.TYPE.INT
        })
      }
      // tinh lai uu dai neu co
      if (irRatePrefer && Number(irRatePrefer?.ir_value) > 0 && installment.type == constant.INSTALLMENT.TYPE.PRIN) {
        await calculateInterestInstallmentFactoring({
          installment,
          payloadUpdateDebtAck,
          irRateObj: irRatePrefer,
          currentInsmObj: currentIrObj,
          payload,
          payloadIr,
          intType: constant.INSTALLMENT.TYPE.PREF_INT
        })
      }
      //còn gốc là còn tính phí
      if (feeRate && Number(feeRate?.fee_amt) > 0 &&installment.type == constant.INSTALLMENT.TYPE.PRIN) {
          await calculateFeeInstallmentFactoring({
            installment,
            payloadUpdateDebtAck,
            feeRateObj: feeRate,
            currentInsmObj: currentIrObj,
            payloadFee,
            payload,
            type: constant.INSTALLMENT.TYPE.FACTORING_FEE
          })
      }
    }
    const { lpiAmt = 0, toCollect = 0, intAmt = 0, feeAmt = 0, prefIntAmt = 0, factoringFee = 0, normalAmt = 0 } = payloadUpdateDebtAck
    if (lpiAmt !== 0 || toCollect !== 0 || intAmt !== 0 || feeAmt !== 0 || prefIntAmt !== 0 || factoringFee !== 0 || normalAmt !== 0) {
      await debtActContractRepo.updateDataIrJobAmtFactoring(payloadUpdateDebtAck)
    }

    if (payloadIr.length > 0) {
      await IrRepository.save(payloadIr)
    }
    if (payloadFee.length > 0) {
      await LoanOrderFeesRepository.save(payloadFee)
    }
    if (payloadIr.length > 0 || payloadFee.length > 0) {
      return {
      statusCode: 200,
      code: 0,
      message: '[MC-LMS] Thuc thi tinh lai theo ngay thanh cong'
      }
    }

    return {
      statusCode: 200,
      code: 1,
      message: '[MC-LMS] Hop dong moi chua phat sinh lai'
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { statusCode: 500, code: 99, message: error.message }
  }
}
async function calculateLpiInstallmentFactoring({
  installment,
  payloadUpdateDebtAck,
  irRateOverdueObj,
  payloadIr,
  payload,
  currentInsmObj,
  lpiType
}) {
  const calcuCfg = constant.CALCUCFG
  const insCycleDate = common.formatDate({ date: installment.cycle_date })
  const insStartDate = common.formatDate({ date: installment.start_date })
  const insEndDate = common.formatDate({ date: installment.end_date })
  const insDueDate = common.formatDate({ date: installment.due_date })
  const irAmtExpire = common.roundUp(
    (irRateOverdueObj?.ir_value / calcuCfg.totalDayOfYear) * installment.remain_amount,
    calcuCfg.scaleOfDay
  )
  payloadIr.push({
    ir_charge_id: irRateOverdueObj.id,
    installment_id: installment.id,
    ir_date: payload.irDate,
    ir_rate: irRateOverdueObj?.ir_value,
    ir_type: irRateOverdueObj?.ir_type,
    ir_amount: irAmtExpire,
    owner_id: payload.ownerId,
    is_testing: payload.isTesting,
    created_by: payload.createdBy,
    contract_number: payload.contractNumber,
    debt_ack_contract_number: payload.debtAckContractNumber,
    prin_amount: installment.remain_amount
  })

  const insObjNow = currentInsmObj || installment
  const lpiAtCycle = insObjNow.ir_num_cycle || insObjNow.num_cycle
  const plUpdateRemainPrinExpire = {
    remainAmount: irAmtExpire,
    debtAckContractNumber: installment.debt_ack_contract_number,
    numCycleOfType: installment.num_cycle,
    lpiAtCycle,
    type: lpiType
  }
  const insExistObj = await InstallmentRepository.findOne({
    where: {
      ir_on_prin: installment.id,
      lpi_at_cycle: lpiAtCycle,
      type: lpiType
    }
  })
  let reAmt = 0
  if (insExistObj) {
    const reAmtDelta = common.roundUp(Number(insExistObj.origin_amt) + irAmtExpire, calcuCfg.scale)
    plUpdateRemainPrinExpire.originAmt = irAmtExpire
    reAmt = reAmtDelta - Number(insExistObj.remain_amount)
    plUpdateRemainPrinExpire.remainAmount = reAmt
    plUpdateRemainPrinExpire.irToDate = payload.irDate
    plUpdateRemainPrinExpire.installmentId = insExistObj.id

    const rsUpdateInsm = await InstallmentRepository.update({
      where: { id: insExistObj.id },
      data: {
        remain_amount: Add(plUpdateRemainPrinExpire.remainAmount),
        amount: Add(plUpdateRemainPrinExpire.remainAmount),
        origin_amt: Add(plUpdateRemainPrinExpire.originAmt),
        ir_to_date: payload.irDate,
        payment_status: constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE,
        accept_payment_date: payload.irDate
      }
    })
    if (rsUpdateInsm.length > 0) {
      const objUpdateInsm = rsUpdateInsm[0]
      await BillOnDueRepository.update({
        where: { installment_id: objUpdateInsm.id },
        data: {
          amount: Add(reAmt),
          remain_amount: Add(reAmt),
          payment_status: constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE,
          accept_payment_date: payload.irDate
        }
      })
    }
  } else {
    reAmt = common.roundUp(irAmtExpire, calcuCfg.scale)
    const objInsInsm = await InstallmentRepository.save({
      debt_ack_contract_id: installment.debt_ack_contract_id,
      num_cycle: installment.num_cycle,
      amount: reAmt,
      remain_amount: reAmt,
      cycle_date: insCycleDate,
      type: lpiType,
      payment_status: constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE,
      owner_id: payload.ownerId,
      is_testing: payload.isTesting,
      created_by: payload.createdBy,
      contract_number: insObjNow.contract_number,
      debt_ack_contract_number: insObjNow.debt_ack_contract_number,
      start_date: insStartDate,
      end_date: insEndDate,
      due_date: insDueDate,
      of_num_cycle: insObjNow.num_cycle,
      lpi_at_cycle: lpiAtCycle,
      closed: constant.INSTALLMENT.CLOSE.TRUE,
      origin_amt: irAmtExpire,
      ir_from_date: payload.irDate,
      ir_to_date: payload.irDate,
      ir_on_prin: installment.id,
      ir_num_cycle: installment.ir_num_cycle,
      amort_id: insObjNow.amort_id,
      accept_payment_date: payload.irDate,
      invoiced_date: installment.invoiced_date,
    })

    if (objInsInsm) {
      await BillOnDueRepository.save({
        contract_number: objInsInsm.contract_number,
        debt_ack_contract_number: objInsInsm.debt_ack_contract_number,
        amount: objInsInsm.amount,
        remain_amount: objInsInsm.remain_amount,
        type: objInsInsm.type,
        num_cycle: objInsInsm.num_cycle,
        on_due_date: insEndDate,
        payment_status: constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE,
        start_date: insStartDate,
        end_date: insEndDate,
        due_date: insDueDate,
        owner_id: payload.ownerId,
        created_by: payload.createdBy,
        installment_id: objInsInsm.id,
        payment_priority: objInsInsm.payment_priority,
        accept_payment_date: objInsInsm.accept_payment_date,
        invoiced_date: objInsInsm.invoiced_date
      })
    }
  }
  payloadUpdateDebtAck.lpiAmt += reAmt
  payloadUpdateDebtAck.toCollect += reAmt
}
async function calculateFeeInstallmentFactoring({
  installment,
  payloadUpdateDebtAck,
  feeRateObj,
  payloadFee,
  payload,
  currentInsmObj,
  type
}) {
  const calcuCfg = constant.CALCUCFG
  const insCycleDate = common.formatDate({ date: installment.cycle_date })
  const insStartDate = common.formatDate({ date: installment.start_date })
  const insEndDate = common.formatDate({ date: installment.end_date })
  const insDueDate = common.formatDate({ date: installment.due_date })
  const factoringFee = common.roundUp(
    (feeRateObj?.fee_amt / calcuCfg.totalDayOfYear) * installment.remain_amount,
    calcuCfg.scaleOfDay
  )
  payloadFee.push({
    fee_id: feeRateObj.id,
    installment_id: installment.id,
    cal_fee_date: payload.irDate,
    fee_rate: feeRateObj?.fee_amt,
    fee_type: feeRateObj?.fee_type,
    fee_amount: factoringFee,
    owner_id: payload.ownerId,
    is_testing: payload.isTesting,
    created_by: payload.createdBy,
    contract_number: payload.contractNumber,
    debt_ack_contract_number: payload.debtAckContractNumber,
    prin_amount: installment.remain_amount
  })

  const insObjNow = currentInsmObj || installment
  // const lpiAtCycle = insObjNow.ir_num_cycle || insObjNow.num_cycle

  const plUpdateRemainPrinExpire = {
    remainAmount: factoringFee,
    debtAckContractNumber: installment.debt_ack_contract_number,
    numCycleOfType: installment.num_cycle,
    // lpiAtCycle,
    type: type
  }
  const insExistObj = await InstallmentRepository.findOne({
    where: {
      ir_on_prin: installment.id,
      // lpi_at_cycle: lpiAtCycle,
      type: type

    }
  })
  let reAmt = 0
  if (insExistObj) {
    const reAmtDelta = common.roundUp(Number(insExistObj.origin_amt) + factoringFee, calcuCfg.scale)
    plUpdateRemainPrinExpire.originAmt = factoringFee
    reAmt = reAmtDelta - Number(insExistObj.remain_amount)
    plUpdateRemainPrinExpire.remainAmount = reAmt
    plUpdateRemainPrinExpire.irToDate = payload.irDate
    plUpdateRemainPrinExpire.installmentId = insExistObj.id

    const rsUpdateInsm = await InstallmentRepository.update({
      where: { id: insExistObj.id },
      data: {
        remain_amount: Add(plUpdateRemainPrinExpire.remainAmount),
        amount: Add(plUpdateRemainPrinExpire.remainAmount),
        origin_amt: Add(plUpdateRemainPrinExpire.originAmt),
        ir_to_date: payload.irDate,
        payment_status: constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE,
        accept_payment_date: payload.irDate
      }
    })
    if (rsUpdateInsm.length > 0) {
      const objUpdateInsm = rsUpdateInsm[0]
      await BillOnDueRepository.update({
        where: { installment_id: objUpdateInsm.id },
        data: {
          amount: Add(reAmt),
          remain_amount: Add(reAmt),
          payment_status: constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE,
          accept_payment_date: payload.irDate // Cập nhật ngày chấp nhận thanh toán
        }
      })
    }
  } else {
    reAmt = common.roundUp(factoringFee, calcuCfg.scale)
    const objInsInsm = await InstallmentRepository.save({
      debt_ack_contract_id: installment.debt_ack_contract_id,
      num_cycle: installment.num_cycle,
      amount: reAmt,
      remain_amount: reAmt,
      cycle_date: insCycleDate,
      type: type,
      payment_status: constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE,
      owner_id: payload.ownerId,
      is_testing: payload.isTesting,
      created_by: payload.createdBy,
      contract_number: insObjNow.contract_number,
      debt_ack_contract_number: insObjNow.debt_ack_contract_number,
      start_date: insStartDate,
      end_date: insEndDate,
      due_date: insDueDate,
      of_num_cycle: insObjNow.num_cycle,
      amort_id: insObjNow.amort_id,
      // lpi_at_cycle: lpiAtCycle,
      closed: constant.INSTALLMENT.CLOSE.TRUE,
      origin_amt: factoringFee,
      ir_from_date: payload.irDate,
      ir_to_date: payload.irDate,
      ir_on_prin: installment.id,
      ir_num_cycle: installment.ir_num_cycle,
      accept_payment_date: payload.irDate,
      invoiced_date: installment.invoiced_date
    })

    if (objInsInsm) {
      await BillOnDueRepository.save({
        contract_number: objInsInsm.contract_number,
        debt_ack_contract_number: objInsInsm.debt_ack_contract_number,
        amount: objInsInsm.amount,
        remain_amount: objInsInsm.remain_amount,
        type: objInsInsm.type,
        num_cycle: objInsInsm.num_cycle,
        on_due_date: insEndDate,
        payment_status: constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE,
        start_date: insStartDate,
        end_date: insEndDate,
        due_date: insDueDate,
        owner_id: payload.ownerId,
        created_by: payload.createdBy,
        installment_id: objInsInsm.id,
        payment_priority: objInsInsm.payment_priority,
        accept_payment_date: objInsInsm.accept_payment_date,
        invoiced_date: objInsInsm.invoiced_date
      })
    }
  }
  payloadUpdateDebtAck.factoringFee += reAmt
  payloadUpdateDebtAck.toCollect += reAmt
}
async function calculateInterestInstallmentFactoring({
  installment,
  payloadUpdateDebtAck,
  irRateObj,
  payloadIr,
  payload,
  currentInsmObj,
  intType
}) {
  const calcuCfg = constant.CALCUCFG
  const insCycleDate = common.formatDate({ date: installment.cycle_date })
  const insStartDate = common.formatDate({ date: installment.start_date })
  const insEndDate = common.formatDate({ date: installment.end_date })
  const insDueDate = common.formatDate({ date: installment.due_date })
  const irAmt = common.roundUp(
    (irRateObj?.ir_value / calcuCfg.totalDayOfYear) * installment.remain_amount,
    calcuCfg.scaleOfDay
  )
  payloadIr.push({
    ir_charge_id: irRateObj.id,
    installment_id: installment.id,
    ir_date: payload.irDate,
    ir_rate: irRateObj?.ir_value,
    ir_type: irRateObj?.ir_type,
    ir_amount: irAmt,
    owner_id: payload.ownerId,
    is_testing: payload.isTesting,
    created_by: payload.createdBy,
    contract_number: payload.contractNumber,
    debt_ack_contract_number: payload.debtAckContractNumber,
    prin_amount: installment.remain_amount
  })

  const insObjNow = currentInsmObj || installment
  // const lpiAtCycle = insObjNow.ir_num_cycle || insObjNow.num_cycle
  const plUpdateRemainPrinExpire = {
    remainAmount: irAmt,
    debtAckContractNumber: installment.debt_ack_contract_number,
    numCycleOfType: installment.num_cycle,
    // lpiAtCycle,
    type: intType
  }
  const insExistObj = await InstallmentRepository.findOne({
    where: {
      ir_on_prin: installment.id,
      // lpi_at_cycle: lpiAtCycle,
      type: intType
    }
  })
  let reAmt = 0
  if (insExistObj) {
    const reAmtDelta = common.roundUp(Number(insExistObj.origin_amt) + irAmt, calcuCfg.scale)
    plUpdateRemainPrinExpire.originAmt = irAmt
    reAmt = reAmtDelta - Number(insExistObj.remain_amount)
    plUpdateRemainPrinExpire.remainAmount = reAmt
    plUpdateRemainPrinExpire.irToDate = payload.irDate
    plUpdateRemainPrinExpire.installmentId = insExistObj.id

    const rsUpdateInsm = await InstallmentRepository.update({
      where: { id: insExistObj.id },
      data: {
        remain_amount: Add(plUpdateRemainPrinExpire.remainAmount),
        amount: Add(plUpdateRemainPrinExpire.remainAmount),
        origin_amt: Add(plUpdateRemainPrinExpire.originAmt),
        ir_to_date: payload.irDate,
        payment_status: constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE,
        accept_payment_date: payload.irDate // Cập nhật ngày chấp nhận thanh toán
      }
    })
    if (rsUpdateInsm.length > 0) {
      const objUpdateInsm = rsUpdateInsm[0]
      await BillOnDueRepository.update({
        where: { installment_id: objUpdateInsm.id },
        data: {
          amount: Add(reAmt),
          remain_amount: Add(reAmt),
          payment_status: constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE,
          accept_payment_date: payload.irDate // Cập nhật ngày chấp nhận thanh toán
        }
      })
    }
  } else {
    reAmt = common.roundUp(irAmt, calcuCfg.scale)
    const objInsInsm = await InstallmentRepository.save({
      debt_ack_contract_id: installment.debt_ack_contract_id,
      num_cycle: installment.num_cycle,
      amount: reAmt,
      remain_amount: reAmt,
      cycle_date: insCycleDate,
      type: intType,
      payment_status: constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE,
      owner_id: payload.ownerId,
      is_testing: payload.isTesting,
      created_by: payload.createdBy,
      contract_number: insObjNow.contract_number,
      debt_ack_contract_number: insObjNow.debt_ack_contract_number,
      start_date: insStartDate,
      end_date: insEndDate,
      due_date: insDueDate,
      of_num_cycle: insObjNow.num_cycle,
      amort_id: insObjNow.amort_id,
      // lpi_at_cycle: lpiAtCycle,
      closed: constant.INSTALLMENT.CLOSE.TRUE,
      origin_amt: irAmt,
      ir_from_date: payload.irDate,
      ir_to_date: payload.irDate,
      ir_on_prin: installment.id,
      ir_num_cycle: installment.ir_num_cycle,
      accept_payment_date: payload.irDate,
      invoiced_date: installment.invoiced_date
    })

    if (objInsInsm) {
      await BillOnDueRepository.save({
        contract_number: objInsInsm.contract_number,
        debt_ack_contract_number: objInsInsm.debt_ack_contract_number,
        amount: objInsInsm.amount,
        remain_amount: objInsInsm.remain_amount,
        type: objInsInsm.type,
        num_cycle: objInsInsm.num_cycle,
        on_due_date: insEndDate,
        payment_status: constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE,
        start_date: insStartDate,
        end_date: insEndDate,
        due_date: insDueDate,
        owner_id: payload.ownerId,
        created_by: payload.createdBy,
        installment_id: objInsInsm.id,
        payment_priority: objInsInsm.payment_priority,
        accept_payment_date: objInsInsm.accept_payment_date,
        invoiced_date: objInsInsm.invoiced_date
      })
    }
  }
  payloadUpdateDebtAck.intAmt += reAmt
  payloadUpdateDebtAck.toCollect += reAmt
  if(intType === constant.INSTALLMENT.TYPE.PREF_INT) payloadUpdateDebtAck.prefIntAmt += reAmt
  else if(intType === constant.INSTALLMENT.TYPE.INT) payloadUpdateDebtAck.normalAmt += reAmt
  
}

module.exports = {
  calcuIrEveryDayRefactoring,
  calculateFeeInstallmentFactoring,
  calculateInterestInstallmentFactoring
}