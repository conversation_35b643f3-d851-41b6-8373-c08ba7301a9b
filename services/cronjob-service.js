const common = require('../utils/common')
const moment = require('moment')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const batchProcessLogRepo = require('../repositories/batch-process-log-repo')
const installmentRepo = require('../repositories/installment-repo')
const batchProcessDetailRepo = require('../repositories/batch-process-detail-repo')
const loanAccRepo = require('../repositories/loan-account-repo')
const constant = require('../utils/constant')
const lodash = require('lodash')
const { cancelAnnex } = require('./loan-annex-service')
const { billRepaymentFlow, doCreateBillOnDue} = require('./bill-service')
const { updateDpdSequence } = require('./contract-risk-grp-service')
const { calcuIrEveryDay } = require('./ir-service')
const { calcuIrEveryDayRefactoring } = require('./ir-factoring-service')
const repaymentService = require("./repayment-service");

const cancelAnnexBatchJob = async function (now = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmss)) {
  try {
    common.log('STARTING cancelAnnexBatchJob ' + new Date())
    const billAnnexList = await billOnDueRepo.findAllBillAnnexPaymentNotCompleted(global.poolRead, {})
    const arrList = lodash.chunk(billAnnexList.rows, 100)
    for (const lst of arrList) {
      const promiseArr = []
      for (const debtAck of lst) {
        promiseArr.push(
          cancelAnnex({
            debtAckContractNumber: debtAck.debt_ack_contract_number,
            scanDate: now
          })
        )
      }
      Promise.all(promiseArr)
    }
    common.log('END cancelAnnexBatchJob ' + new Date())
    return { statusCode: 200, code: 0, message: 'Success cancelAnnexBatchJob' }
  } catch (e) {
    console.error('Error at cancelAnnexBatchJob:', e.message)
    console.log(e)
    return { statusCode: 500, code: 99, message: e.message }
  }
}

const createBillOnDueBatchJob = async function (now = moment().format(constant.DATE_FORMAT.YYYYMMDD2)) {
  try {
    common.log('STARTING createBillOnDueBatchJob ' + new Date())
    const insList = await installmentRepo.findAllNotClosedNotFactoring(global.poolRead, {})
    const arrList = lodash.chunk(insList.rows, 100)
    const startTime = new Date()
    for (const [index, lst] of arrList.entries()) {
      common.log(`START STEP ${index + 1} INDEX from ${index * 100} to ${(index + 1) * 100}`)
      await Promise.all(
        lst.map(async (loanAccount) => {
          await billRepaymentFlow(loanAccount.debt_ack_contract_number, now)
        })
      )
      common.log(`END STEP ${index + 1} INDEX from ${index * 100} to ${(index + 1) * 100}`)
    }
    const endTime = new Date()
    const payloadInsertLog = {
      productType: 'MCC',
      startDate: startTime,
      runAt: startTime,
      endDate: endTime,
      batchDate: now,
      jobName: 'batch_process_bill_on_due',
      status: 'bill_start',
      jobId: moment(now).format(constant.DATE_FORMAT.YYYYMMDD3)
    }
    batchProcessLogRepo.insertBatchProcessLog(payloadInsertLog)
    common.log('END createBillOnDueBatchJob ' + new Date())
    return { statusCode: 200, code: 0, message: 'Success createBillOnDueBatchJob' }
  } catch (e) {
    console.error('Error at createBillOnDueBatchJob: ', e.message)
    console.log(e)
    return { statusCode: 500, code: -1, message: e.message }
  }
}

const createBillOnDueFactoringBatchJob = async function (now = moment().format(constant.DATE_FORMAT.YYYYMMDD2)) {
  try {
    common.log('STARTING createBillOnDueFactoringBatchJob ' + new Date())
    const insList = await installmentRepo.findAllNotClosedFactoring(global.poolRead, {})
    const arrList = lodash.chunk(insList.rows, 100)
    const startTime = new Date()
    for (const [index, lst] of arrList.entries()) {
      common.log(`START STEP ${index + 1} INDEX from ${index * 100} to ${(index + 1) * 100}`)
      await Promise.all(
        lst.map(async (loanAccount) => {
          await billRepaymentFlow(loanAccount.debt_ack_contract_number, now)
        })
      )
      common.log(`END STEP ${index + 1} INDEX from ${index * 100} to ${(index + 1) * 100}`)
    }
    const endTime = new Date()
    const payloadInsertLog = {
      productType: 'MCC',
      startDate: startTime,
      runAt: startTime,
      endDate: endTime,
      batchDate: now,
      jobName: 'batch_process_bill_on_due',
      status: 'bill_start',
      jobId: moment(now).format(constant.DATE_FORMAT.YYYYMMDD3)
    }
    batchProcessLogRepo.insertBatchProcessLog(payloadInsertLog)
    common.log('END createBillOnDueFactoringBatchJob ' + new Date())
    return { statusCode: 200, code: 0, message: 'Success createBillOnDueFactoringBatchJob' }
  } catch (e) {
    console.error('Error at createBillOnDueFactoringBatchJob: ', e.message)
    console.log(e)
    return { statusCode: 500, code: -1, message: e.message }
  }
}

const calDpbRiskGrpBatchJob = async function (now = moment().format(constant.DATE_FORMAT.YYYYMMDD2)) {
  try {
    common.log('STARTING calDpbRiskGrpBatchJob ' + new Date())
    let offset = 0
    const limit = 20
    const startTime = new Date()
    while (true) {
      const listCustId = await loanAccRepo.findAllCustIdToRunDpd(now, offset, limit)
      if (!listCustId.length) {
        break
      }
      common.log(`START INDEX from ${offset} to ${offset + limit}`)
      await Promise.all(
        listCustId.map(async (obj) => {
          await updateDpdSequence(obj.cust_id, now)
        })
      )
      common.log(`END INDEX from ${offset} to ${offset + limit}`)

      offset += limit
    }
    const endTime = new Date()
    const payloadInsertLog = {
      productType: 'MCC',
      startDate: startTime,
      runAt: startTime,
      endDate: endTime,
      batchDate: now,
      jobName: 'batch_process_dpd_risk_grp',
      status: 'dpd_start',
      jobId: moment(now).format(constant.DATE_FORMAT.YYYYMMDD3)
    }
    batchProcessLogRepo.insertBatchProcessLog(payloadInsertLog)
    common.log('END calDpbRiskGrpBatchJob ' + new Date())
    return { statusCode: 200, code: 0, message: 'Success calDpbRiskGrpBatchJob' }
  } catch (e) {
    console.error('Error at calDpbRiskGrpBatchJob:', e.message)
    console.log(e)
    return { statusCode: 500, code: 99, message: e.message }
  }
}

async function calcuIrEveryDayBatchJob(dateNow = moment().format(constant.DATE_FORMAT.YYYYMMDD2)) {
  try {
    common.log('START calcuIrEveryDayBatchJob')
    let offset = 0
    const limit = 100
    const startTime = new Date()
    while (true) {
      const listKunn = await loanAccRepo.findAllActiveAndPaymentNotComplete(offset, limit)
      if (!listKunn.length) {
        break
      }
      common.log(`START INDEX from ${offset} to ${offset + limit}`)
      await Promise.all(
        listKunn.map(async (val) => {
          const startTime = new Date()
          const bodyIr = { debtAckContractNumber: val.debt_ack_contract_number, irDate: dateNow }
          const result = await calcuIrEveryDay(bodyIr)
          const endTime = new Date()
          const payloadInsertLog = {
            productType: 'MCC',
            startDate: startTime,
            runAt: startTime,
            endDate: endTime,
            batchDate: dateNow,
            jobName: 'batch_process_cal_ir',
            status: 'calculate_ir',
            jobId: moment(dateNow).format(constant.DATE_FORMAT.YYYYMMDD3),
            errorCode: result?.code,
            errorMessage: result?.message,
            debtAckContractNumber: val.debt_ack_contract_number
          }
          try {
            await batchProcessDetailRepo.insertBatchProcessDetail(payloadInsertLog)
          } catch (error) {
            console.log('error insert batch detail')
          }
        })
      )
      common.log(`END INDEX from ${offset} to ${offset + limit}`)
      offset += limit
    }
    const endTime = new Date()
    const payloadInsertLog = {
      productType: 'MCC',
      startDate: startTime,
      runAt: startTime,
      endDate: endTime,
      batchDate: dateNow,
      jobName: 'batch_process_calculate_ir',
      status: 'calculate_ir',
      jobId: moment(dateNow).format(constant.DATE_FORMAT.YYYYMMDD3)
    }
    batchProcessLogRepo.insertBatchProcessLog(payloadInsertLog)
    common.log('END calcuIrEveryDayBatchJob')
    return { statusCode: 200, code: 0, message: 'Success calcuIrEveryDayBatchJob' }
  } catch (error) {
    return { statusCode: 500, code: -1, message: error.message }
  }
}
async function calcuIrFactoringEveryDayBatchJob(dateNow = moment().format(constant.DATE_FORMAT.YYYYMMDD2)) {
  try {
    common.log('START calcuIrFactoringEveryDayBatchJob')
    let offset = 0
    const limit = 100
    const startTime = new Date()
    while (true) {
      const listKunn = await loanAccRepo.findAllFactoringActiveAndPaymentNotComplete(offset, limit)
      if (!listKunn.length) {
        break
      }
      common.log(`START INDEX from ${offset} to ${offset + limit}`)
      await Promise.all(
        listKunn.map(async (val) => {
            const startTime = new Date()
            let result
            for (let i = 2; i >= 0; i--) {
              const irDate = moment(dateNow, constant.DATE_FORMAT.YYYYMMDD2).subtract(i, 'days').format(constant.DATE_FORMAT.YYYYMMDD2)

              let checkTerminateFactoring = await repaymentService.checkTerminateFactoring({
                debtAckContractNumber: val.debt_ack_contract_number,
                valueDate: irDate
              })

              if (checkTerminateFactoring) {
                const dateRunJob = common.formatDate({ date: irDate })
                let result = await doCreateBillOnDue({ debtAckContractNumber: val.debt_ack_contract_number, onDueDate: dateRunJob })
                if (result.code == 0) {
                  await repaymentService.doRepayment({
                    debtAckContractNumber: val.debt_ack_contract_number,
                    paymentDate: dateRunJob,
                    isTerminateFactoring: true,
                  })
                }
              }

              const bodyIr = { debtAckContractNumber: val.debt_ack_contract_number, irDate }
              result = await calcuIrEveryDayRefactoring(bodyIr)
            }
          const endTime = new Date()
          const payloadInsertLog = {
            productType: 'MCC',
            startDate: startTime,
            runAt: startTime,
            endDate: endTime,
            batchDate: dateNow,
            jobName: 'batch_process_cal_ir',
            status: 'calculate_ir',
            jobId: moment(dateNow).format(constant.DATE_FORMAT.YYYYMMDD3),
            errorCode: result?.code,
            errorMessage: result?.message,
            debtAckContractNumber: val.debt_ack_contract_number
          }
          try {
            await batchProcessDetailRepo.insertBatchProcessDetail(payloadInsertLog)
          } catch (error) {
            console.log('error insert batch detail')
          }
        })
      )
      common.log(`END INDEX from ${offset} to ${offset + limit}`)
      offset += limit
    }
    const endTime = new Date()
    const payloadInsertLog = {
      productType: 'MCC',
      startDate: startTime,
      runAt: startTime,
      endDate: endTime,
      batchDate: dateNow,
      jobName: 'batch_process_calculate_ir',
      status: 'calculate_ir',
      jobId: moment(dateNow).format(constant.DATE_FORMAT.YYYYMMDD3)
    }
    batchProcessLogRepo.insertBatchProcessLog(payloadInsertLog)
    common.log('END calcuIrFactoringEveryDayBatchJob')
    return { statusCode: 200, code: 0, message: 'Success calcuIrFactoringEveryDayBatchJob' }
  } catch (error) {
    return { statusCode: 500, code: -1, message: error.message }
  }
}

module.exports = {
  cancelAnnexBatchJob,
  createBillOnDueBatchJob,
  createBillOnDueFactoringBatchJob,
  calDpbRiskGrpBatchJob,
  calcuIrEveryDayBatchJob,
  calcuIrFactoringEveryDayBatchJob
}
