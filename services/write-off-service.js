const loanAccountRepo = require('./../repositories/loan-account-repo')
const { CONTRACT_LIMIT_STATUS, IS_WRITE_OFF } = require('../utils/constant')
const loanRgService = require('./loan-risk-grp-service')

async function updateWriteOff(payload) {
  let { debtAckContractNumber, custId, date } = payload
  try {
    const findLoanAcc = await loanAccountRepo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)

    if (!findLoanAcc.length) {
      return { message: 'Do not find contract_number: ' + debtAckContractNumber, code: -2 }
    }

    const loanAccount = findLoanAcc[0]

    if (loanAccount.status != CONTRACT_LIMIT_STATUS.statusToCode.ACTIVE) {
      return { message: 'Contract number not active, do not write off: ' + debtAckContractNumber, code: -2 }
    }

    if (loanAccount.is_writing_off) {
      return {
        message: 'Do not write off, Loan written off with contract_number: ' + debtAckContractNumber,
        code: -2
      }
    }

    date = date ? new Date(date) : new Date()

    !custId && (custId = loanAccount.cust_id)

    const userRgValue = 5
    loanRgService.createUserRg(debtAckContractNumber, userRgValue, date)

    // TODO: UPDATE IS_WRITING OFF
    loanAccountRepo.updateIsWritingOffbyDebtAckContractNumber(debtAckContractNumber, IS_WRITE_OFF, date)

    return { code: 200, message: '[LMS-MCC] Update write off successfully' }
  } catch (err) {
    console.log(err)
    return { code: 500, message: err.message }
  }
}

module.exports = {
  updateWriteOff
}
