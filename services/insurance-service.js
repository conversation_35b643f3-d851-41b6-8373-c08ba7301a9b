const common = require('../utils/common')
const constant = require('../utils/constant')
const s3Service = require('../utils/s3-service')
const moment = require('moment')
const micService = require('../other-services/mic-service')
const bmiService = require('../other-services/bmi-service')
const crmService = require('../other-services/crm-service')
const insuranceRepo = require('./../repositories/insurance-repo')
const { v4: uuidv4 } = require('uuid')

async function insertInsurances(payload, loanAccObj) {
  if (!payload.insurances || !payload.insurances.length) {
    return
  }

  const startDate = moment().format(constant.DATE_FORMAT.DDMMYYYY)
  const endDate = common.calNextCycleDateV3(12, moment(), constant.DATE_FORMAT.DDMMYYYY)
  let totalInsurance = 0
  for (const insur of payload.insurances) {
    const { insurType, insurPartnerCode } = insur

    const insurance = {
      loan_id: loanAccObj.loan_id,
      contract_number: loanAccObj.contract_number,
      debt_ack_contract_number: loanAccObj.debt_ack_contract_number,
      cust_id: loanAccObj.cust_id,
      insur_id_ctlg: insur.insurId,
      insur_name: insur.insurName,
      insur_type: insur.insurType,
      amount: payload.originalAmount,
      insur_amt: insur.insurAmt,
      start_date: startDate,
      end_date: endDate,
      phi: insur.insurAmt,
      thue: 0,
      flag_active: constant.FLAG_ACTIVE_INIT,
      insur_partner_code: insurPartnerCode,
      owner_id: payload.ownerId,
      created_by: payload.createdBy
    }

    if (insurPartnerCode === constant.INSURANCE_PARTNER_CODE.BMI) {
      insurance.insur_amt = insur.insurAmt
      insurance.phi = 0
      insurance.thue = 0
      insurance.end_date = common.calNextCycleDateV3(6, moment(), constant.DATE_FORMAT.DDMMYYYY)
    } else {
      const result = await micService.callTinhPhiSkVtd({
        amount: payload.originalAmount,
        start_date: startDate,
        end_date: endDate,
        insur_type: insurType
      })
      if (result?.data) {
        insurance.insur_amt = result?.data?.ttoan
        insurance.phi = result?.data?.phi
        insurance.thue = result?.data?.thue
      }
    }

    insuranceRepo.insertInsurance(insurance)
    totalInsurance += insurance.insur_amt
  }
  payload.disbursementAmount -= totalInsurance
}

async function activeInsurances(loanAccountObj) {
  const [listInsurances, customer] = await Promise.all([
    insuranceRepo.getInitInsurance(loanAccountObj.debt_ack_contract_number),
    crmService.getCustomerInfoByCustId(loanAccountObj.cust_id)
  ])
  const startDate = moment().format(constant.DATE_FORMAT.DDMMYYYY)
  let endDate = common.calNextCycleDateV3(12, moment(), constant.DATE_FORMAT.DDMMYYYY)
  for (const insur of listInsurances) {
    let insurPartnerCode = insur.insur_partner_code;
    insur.id_tras = uuidv4()
    insur.start_date = startDate
    insur.end_date = endDate

    let s3FileName = `${loanAccountObj.debt_ack_contract_number}.pdf`

    if (insurPartnerCode === constant.INSURANCE_PARTNER_CODE.BMI) {
      endDate = common.calNextCycleDateV3(6, moment(), constant.DATE_FORMAT.DDMMYYYY)
      insur.end_date = endDate

      const result = await bmiService.callGcnSkVtd(insur, customer, loanAccountObj)
      const { request_body, response_body } = result
      const data = response_body?.data
      if (data) {
        s3FileName = `${loanAccountObj.debt_ack_contract_number}_${data?.[1]}.docx`
        const s3Url = await uploadFileGcnS3(data?.[4], s3FileName, insurPartnerCode)

        const payloadActive = {
          id_tras: insur.id_tras,
          so_hd: data?.[1],
          gcn: data?.[4],
          so_id: data?.[1],
          phi: data?.[2].replace(/,/g, ''),
          s3_url: s3Url,
          flag_active: constant.FLAG_ACTIVE,
          request_body: JSON.stringify(request_body),
          response_body: JSON.stringify(response_body),
          start_date: startDate,
          end_date: endDate
        }
        await insuranceRepo.activeInsurance(insur.insur_id, payloadActive)
      }
    } else {
      const result = await micService.callGcnSkVtd(insur, customer, insurPartnerCode)
      const { request_body, response_body } = result
      const data = response_body?.data
      if (data) {
        s3FileName = `${loanAccountObj.debt_ack_contract_number}_${data?.so_hd}.pdf`
        const s3Url = await uploadFileGcnS3(data?.gcn, s3FileName)
        const payloadActive = {
          id_tras: insur.id_tras,
          so_hd: data?.so_hd,
          gcn: data?.gcn,
          so_id: data?.so_id,
          file: data?.file,
          phi: data?.phi,
          thue: data?.thue,
          insur_amt: data?.ttoan,
          s3_url: s3Url,
          flag_active: constant.FLAG_ACTIVE,
          request_body: JSON.stringify(request_body),
          response_body: JSON.stringify(response_body),
          start_date: startDate,
          end_date: endDate
        }
        await insuranceRepo.activeInsurance(insur.insur_id, payloadActive)
      }
    }
  }
}

async function uploadFileGcnS3(gcnUrl, fileName, insurPartnerCode) {
  try {
    let base64File
    if (insurPartnerCode === constant.INSURANCE_PARTNER_CODE.BMI) {
      base64File = await common.retry({
        func: () => bmiService.callDownloadGcn(gcnUrl),
        retryCount: 5,
        retryInterval: 1000,
        retryBackoffFactor: 2
      })
    } else {
      base64File = await common.retry({
        func: () => micService.callDownloadGcn(gcnUrl),
        retryCount: 5,
        retryInterval: 1000,
        retryBackoffFactor: 2
      })
    }

    if (!base64File) {
      return null
    }
    const response = await s3Service.upload(fileName, Buffer.from(base64File, 'base64'), 'GCN_MIC')
    return response.Location
  } catch (error) {
    return null
  }
}
module.exports = {
  insertInsurances,
  activeInsurances
}
