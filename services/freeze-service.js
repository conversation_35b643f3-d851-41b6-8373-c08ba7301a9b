const constant = require('../utils/constant')
const { LoanAccountRepository, FreezeHstRepository } = require('../repositories-v2')
const repaymentService = require("./repayment-service");

async function updateFreeze(payload) {
  try {
    const loanAccount = await LoanAccountRepository.findOne({
      where: { debt_ack_contract_number: payload.debtAckContractNumber }
    })
    if (!loanAccount) {
      return {
        code: 3,
        statusCode: 400,
        message: 'DebtAckContractNumber not found'
      }
    }
    if (![0, 1].includes(payload.isFreeze)) {
      return {
        code: 3,
        statusCode: 400,
        message: 'Invalid freeze value, must be 0 or 1'
      }
    }

    if (loanAccount.is_freeze === payload.isFreeze) {
      return {
        code: 3,
        statusCode: 400,
        message: 'Freeze value is the same as the current value'
      }
    }
    loanAccount.is_freeze = payload.isFreeze
    await loanAccount.save()
    await FreezeHstRepository.save({
      debt_ack_contract_number: payload.debtAckContractNumber,
      status: constant.MAP_FREEZE_STATUS[payload.isFreeze],
      created_user: payload.createdUser
    })

    if (payload.isFreeze === 0) {
      let dataRepayment = {
        debtAckContractNumber: payload.debtAckContractNumber,
        paymentDate: new Date(),
        valueDate: new Date()
      }

      await repaymentService.doRepayment(dataRepayment)
    }
    
    return {
      code: 0,
      statusCode: 200,
      message: 'Success'
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 999,
      message: error.message
    }
  }
}

async function getFreezeHst(debtAckContractNumber) {
  try {
    const [loanAccount, listHst] = await Promise.all([
      LoanAccountRepository.findOne({ where: { debt_ack_contract_number: debtAckContractNumber } }),
      FreezeHstRepository.findAll({ where: { debt_ack_contract_number: debtAckContractNumber }, order: { id: 'DESC' } })
    ])
    if (!loanAccount) {
      return {
        code: 3,
        statusCode: 400,
        message: 'DebtAckContractNumber not found'
      }
    }
    return {
      code: 0,
      statusCode: 200,
      message: 'Success',
      data: {
        listHst,
        info: {
          is_freeze: loanAccount.is_freeze,
          freeze_status: constant.MAP_FREEZE_STATUS[loanAccount.is_freeze]
        }
      }
    }
  } catch (error) {
    return {
      statusCode: 500,
      code: 999,
      message: error.message
    }
  }
}

module.exports = {
  updateFreeze,
  getFreezeHst
}
