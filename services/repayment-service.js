const paymentRepo = require('../repositories/payment-repo')
const debtAckContractRepo = require('../repositories/debt-ack-contract-repo')
const installmentRepo = require('../repositories/installment-repo')
const paymentAllocateRepo = require('../repositories/payment-allocate-repo')
const paymentDetailRepo = require('../repositories/payment-detail-repo')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const loanAccountOrderRepo = require('../repositories/loan-account-order-repo')
const installmentDeductionRepo = require('../repositories/promotion-installment-deduction-repo')
const promotionRepo = require('../repositories/promotion-repo')
const constant = require('../utils/constant')
const helper = require('../utils/helper')
const crmService = require('../other-services/crm-service')
const backendMobileService = require('../other-services/backend-mobile-service')
const actionAuditService = require('../other-services/action-audit-service')
const loanAnnexRepo = require('../repositories/loan-annex-repo')
const contractRiskGrpService = require('./contract-risk-grp-service')
const loanAccountV2Repo = require('../repositories/loan-account-repo')
const { LoanAccountRepository } = require('../repositories-v2')
const merchantLimitRepo = require('../repositories/merchant-limit-repo')
const losService = require('../other-services/los-service')
const loanAmortRepo = require('../repositories/loan-amort-repo')
const loanStatusHstService = require('../services/loan-status-hst-service')
const { formatDate, sleep } = require('../utils/common')
const { setCacheField, clearCacheField, checkCacheField, checkCacheFieldSuspend, setCacheFieldSuspend,
  clearCacheFieldSuspend
} = require('../utils/redis')
const lodash = require('lodash')
const DEFAULT_TIME_WAIT = 3 * 1000
const common = require('../utils/common')
const irRepo = require("../repositories/ir-repo");
const smsService = require("../other-services/sms-service");

const getRuleAllocate = function(activeDate, partnerCode) {
  return global.ruleAllocate.find(
      (item) => item.startDate <= activeDate && item.endDate > activeDate
  )?.typeAllocate
}

// kiểm tra xem hợp đồng BTT có bị TER vào ngày đầu tiên không?
const checkTerminateFactoring = async function ({debtAckContractNumber, valueDate}) {
  const [loanAccountObj, listPaymentRemainAmortCur, rsIrDate] = await Promise.all([
      LoanAccountRepository.findOne({ where: {
        debt_ack_contract_number: debtAckContractNumber
      }}),
      paymentRepo.getRemainAmortByContractNumberV2(debtAckContractNumber),
      irRepo.findByContractNumberAndIrDate({
        debtAckContractNumber
      })
  ]);

  if (common.getDifferenceInDays(loanAccountObj.activation_date, valueDate) !== -1) {
    return false
  }

  if (!common.isFactoringLoanChannel(loanAccountObj?.partner_code)) {
    return false
  }

  // nếu load lãi rồi thì cũng thôi
  if (rsIrDate.rowCount > 0) {
    return false
  }

  const totalNonAmt = lodash.sumBy(listPaymentRemainAmortCur, function (payment) {
    return Number(payment.non_allocated_amt)
  })

  if (loanAccountObj.apr_limit_amt <= totalNonAmt) {
    return true
  }

  return false
}

const doRepayment = async function (payload) {
  if (!payload.debtAckContractNumber) {
    return null
  }

  const loanAccountObj = await LoanAccountRepository.findOne({ where: {
      debt_ack_contract_number: payload.debtAckContractNumber
    } });

  if (common.isFactoringLoanChannel(loanAccountObj?.partner_code)) {
    return await repaymentFactoring(payload)
  } else {
    return await repayment(payload)
  }
}

const doRepaymentSuspend = async function (payload) {
  try {
    const checkCache = await checkCacheFieldSuspend(payload.debtAckContractNumber)
    if (checkCache) {
      await sleep(DEFAULT_TIME_WAIT)
      return doRepaymentSuspend(payload)
    }
    setCacheFieldSuspend(payload.debtAckContractNumber)
    console.log(`START REPAYMENT SUSPEND with payload ${JSON.stringify(payload)}`)
    const paymentDate = formatDate({ date: payload.paymentDate })
    const valueDate = formatDate({ date: payload.valueDate, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss })

    const [
      loanAccObj,
      listAllPaymentAllocate,
    ] =
        await Promise.all([
          LoanAccountRepository.findOne({ where: { debt_ack_contract_number: payload.debtAckContractNumber } }),
          paymentAllocateRepo.findAllPaymentAllocateCode(),
        ])
    let message = '[MC-LMS] Khong thuc hien phan bo duoc tien'

    if (!loanAccObj.suspend_account_holding_day || Number(loanAccObj.suspend_account_holding_day) <= 0) {
      await clearCacheFieldSuspend(payload.debtAckContractNumber)
      return {
        statusCode: 200,
        code: 0,
        message,
        data: {
          totalNonAllocateAmt: Number(loanAccObj.suspend_amt)
        }
      }
    }

    if (loanAccObj.is_freeze) {
      await clearCacheFieldSuspend(payload.debtAckContractNumber)
      return {
        statusCode: 200,
        code: 0,
        message,
        data: {
          totalNonAllocateAmt: Number(loanAccObj.suspend_amt)
        }
      }
    }

    payload.suspendAccountHoldingDay = Number(loanAccObj.suspend_account_holding_day)

    const listPayAllNotExpire = listAllPaymentAllocate.filter(
        (item) =>
            item.code == constant.PAY_ALLOCATE.NOT_EXPIRE &&
            item.start_time <= loanAccObj.activation_date &&
            item.end_time >= loanAccObj.activation_date
    )
    const listPayAllNotDue = listAllPaymentAllocate.filter(
        (item) =>
            item.code == constant.PAY_ALLOCATE.NOT_DUE &&
            item.start_time <= loanAccObj.activation_date &&
            item.end_time >= loanAccObj.activation_date
    )
    const listPayAllExpire = listAllPaymentAllocate.filter(
        (item) =>
            item.code == constant.PAY_ALLOCATE.EXPIRE &&
            item.start_time <= loanAccObj.activation_date &&
            item.end_time >= loanAccObj.activation_date
    )

    const [listBillOnDue, listBillOnDueNotActive, loanAcc] =
        await Promise.all([
          billOnDueRepo.getBillOnDuePaymentNotCompleteAndAcceptPaymentFactoring(global.poolRead, {
            ...payload,
          }),
          billOnDueRepo.getBillOnDuePaymentNotCompleteAndAcceptPaymentV3Factoring(
              payload.debtAckContractNumber,
              constant.BILL_ON_DUE.STATUS.NOT_ACTIVE,
              payload.paymentDate,
              payload.suspendAccountHoldingDay
          ),
          LoanAccountRepository.findOne({ where: { debt_ack_contract_number: payload.debtAckContractNumber } }),
        ])

    let totalNonAllAmt = Number(loanAcc.suspend_amt)

    const arrPaymentDetail = []
    let object = {
      arrPaymentDetail,
      totalNonAllAmt,
      valueDate,
      loanAccObj
    }
    console.log('suspend object: ', JSON.stringify(object))

    const {
      mapAllBillOnDue,
    } = helper.getMapBillOnDueFactoring({
      listBillOnDue,
      listBillOnDueNotActive: listBillOnDueNotActive.rows,
      paymentDate,
      listPayAllExpire,
      listPayAllNotExpire,
      listPayAllNotDue,
      totalNonAmt: totalNonAllAmt,
      partnerCode: loanAccObj.partner_code
    })

    const dues = []
    let flagAnnex = false
    let annexNumber

    const listBillOnDueTime = Object.keys(mapAllBillOnDue)
    listBillOnDueTime.sort((a, b) => a - b)
    for (const bodTime of listBillOnDueTime) {
      const mapObj = mapAllBillOnDue[bodTime]
      if (mapObj.type == null) {
        continue
      }
      const billOnDueCycle = mapObj.data
      if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.NOT_EXPIRED) {
        object = await repaymentProcessSuspend(object, listPayAllNotExpire, billOnDueCycle)
        dues.push(object.dueObj)
      } else if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.EXPIRED) {
        object = await repaymentProcessSuspend(object, listPayAllExpire, billOnDueCycle)
        dues.push(object.dueObj)
      } else if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.NOT_DUE) {
        object = await repaymentProcessSuspend(object, listPayAllNotDue, billOnDueCycle)
        dues.push(object.dueObj)
      } else if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.ANNEX) {
        const totalNonAllocatedAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
          return Number(payment.non_allocated_amt)
        })
        let totalAnnexAmt = 0
        let mapToArray = []
        Object.values(billOnDueCycle).forEach((value) => {
          mapToArray = mapToArray.concat(value)
        })
        for (const bill of mapToArray) {
          totalAnnexAmt += Number(bill.remain_amount)
          annexNumber = bill.annex_number
        }
        if (totalNonAllocatedAmt >= totalAnnexAmt) {
          object = await repaymentProcessSuspend(object, listPayAllNotExpire, billOnDueCycle)
          flagAnnex = true
          object.dueObj.flagAnnex = true
          dues.push(object.dueObj)
        } else {
          continue
        }
      }
    }
    totalNonAllAmt = object.totalNonAllAmt

    handleUpdatePaidAmtAndReduntAmtFactoring(loanAccObj, dues, object)

    if (flagAnnex) {
      await handleUpdateStatusAnnexInsm(annexNumber, payload, loanAccObj)
    }

    await checkTerminated(loanAccObj, paymentDate, valueDate, totalNonAllAmt)

    if (arrPaymentDetail.length) {
      const listAllocatedPaymentIds = lodash.uniq(arrPaymentDetail.map((item) => item.payment_id)).filter((item) => item != null)
      await Promise.all([
        paymentDetailRepo.insBatchPaymentDetailV2(arrPaymentDetail),
        paymentRepo.updateLastAllocationDate(listAllocatedPaymentIds)
      ])
      message = '[MC-LMS] Thuc hien thanh toan thanh cong'
    }

    await contractRiskGrpService.doCalculatorDpdRiskGrp({
      loanAccObj,
      payload: {
        debtAckContractNumber: payload.debtAckContractNumber,
        calDate: paymentDate
      }
    })

    await clearCacheFieldSuspend(payload.debtAckContractNumber)

    return {
      statusCode: 200,
      code: 0,
      message,
      data: {
        totalNonAllocateAmt: totalNonAllAmt
      }
    }
  } catch (err) {
    await clearCacheFieldSuspend(payload.debtAckContractNumber)
    console.error('Error while repayment suspend: ', err.message)
    return { statusCode: 500, code: 99, message: err.message }
  }
}

/**
 * Ham xu ly gach no cho khach hang
 * @param {*} req
 * @param {*} res
 * @returns
 */
const repayment = async function (payload) {
  try {
    const checkCache = await checkCacheField(payload.debtAckContractNumber)
    if (checkCache) {
      await sleep(DEFAULT_TIME_WAIT)
      return repayment(payload)
    }
    setCacheField(payload.debtAckContractNumber)
    console.log(`START REPAYMENT with payload ${JSON.stringify(payload)}`)
    const paymentDate = formatDate({ date: payload.paymentDate })
    const valueDate = formatDate({ date: payload.valueDate, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss })

    const [loanAccObj, listBillOnDue, listBillOnDueNotActive, listPaymentRemainAmort, listAllPaymentAllocate] =
      await Promise.all([
        LoanAccountRepository.findOne({ where: { debt_ack_contract_number: payload.debtAckContractNumber } }),
        billOnDueRepo.getBillOnDuePaymentNotComplete(global.poolRead, payload),
        billOnDueRepo.getBillOnDuePaymentNotCompleteV3(
          payload.debtAckContractNumber,
          'asc',
          constant.BILL_ON_DUE.STATUS.NOT_ACTIVE
        ),
        paymentRepo.getRemainAmortByContractNumberV2(payload.debtAckContractNumber),
        paymentAllocateRepo.findAllPaymentAllocateCode()
      ])
    let message = '[MC-LMS] Nop tien vao thanh cong. Khong thuc hien phan bo duoc tien'

    //callback nhan duoc tien cho los
    if (!helper.isLosUnitedPartnerCode(loanAccObj.partner_code)) {
      losService.callReceivedPayment({
        kunnNumber: loanAccObj.debt_ack_contract_number,
        contractNumber: loanAccObj.contract_number,
        paymentDate,
        paymentAmount: payload.installmentAmort
      })
    }

    const totalNonAmt = lodash.sumBy(listPaymentRemainAmort, function (payment) {
      return Number(payment.non_allocated_amt)
    })
    if (loanAccObj.is_freeze) {
      await clearCacheField(payload.debtAckContractNumber)
      return {
        statusCode: 200,
        code: 0,
        message,
        data: {
          totalNonAllocateAmt: totalNonAmt
        }
      }
    }
    const listPayAllNotExpire = listAllPaymentAllocate.filter(
      (item) =>
        item.code == constant.PAY_ALLOCATE.NOT_EXPIRE &&
        item.start_time <= loanAccObj.activation_date &&
        item.end_time >= loanAccObj.activation_date
    )
    const listPayAllExpire = listAllPaymentAllocate.filter(
      (item) =>
        item.code == constant.PAY_ALLOCATE.EXPIRE &&
        item.start_time <= loanAccObj.activation_date &&
        item.end_time >= loanAccObj.activation_date
    )

    const activeDate = formatDate({ date: loanAccObj.active_date })
    const ruleAllocate = getRuleAllocate(activeDate, loanAccObj.partner_code)

    const { mapAllBillOnDue } = helper.getMapBillOnDue({
      listBillOnDue,
      listBillOnDueNotActive,
      paymentDate,
      ruleAllocate,
      listPayAllExpire,
      listPayAllNotExpire,
      totalNonAmt,
    })

    const arrPaymentDetail = []
    let object = {
      arrPaymentDetail,
      listPaymentRemainAmort: listPaymentRemainAmort.filter(
        (item) => formatDate({ date: item.value_date, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss }) <= valueDate
      ),
      valueDate
    }
    console.log('object: ', JSON.stringify(object))
    const dues = []
    let flagAnnex = false
    let annexNumber

    const listBillOnDueTime = Object.keys(mapAllBillOnDue)
    listBillOnDueTime.sort((a, b) => a - b)
    for (const bodTime of listBillOnDueTime) {
      const mapObj = mapAllBillOnDue[bodTime]
      if (mapObj.type == null) {
        continue
      }
      const billOnDueCycle = mapObj.data
      if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.NOT_EXPIRED) {
        object = await repaymentProcess(object, listPayAllNotExpire, billOnDueCycle)
        dues.push(object.dueObj)
      } else if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.EXPIRED) {
        object = await repaymentProcess(object, listPayAllExpire, billOnDueCycle)
        dues.push(object.dueObj)
      } else if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.ANNEX) {
        const totalNonAllocatedAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
          return Number(payment.non_allocated_amt)
        })
        let totalAnnexAmt = 0
        let mapToArray = []
        Object.values(billOnDueCycle).forEach((value) => {
          mapToArray = mapToArray.concat(value)
        })
        for (const bill of mapToArray) {
          totalAnnexAmt += Number(bill.remain_amount)
          annexNumber = bill.annex_number
        }
        if (totalNonAllocatedAmt >= totalAnnexAmt) {
          object = await repaymentProcess(object, listPayAllNotExpire, billOnDueCycle)
          flagAnnex = true
          object.dueObj.flagAnnex = true
          dues.push(object.dueObj)
        } else {
          continue
        }
      }
    }
    const totalNonAllAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
      return Number(payment.non_allocated_amt)
    })
    handleUpdatePaidAmtAndReduntAmt(loanAccObj, dues, object)

    if (flagAnnex) {
      await handleUpdateStatusAnnexInsm(annexNumber, payload, loanAccObj)
    }

    await checkTerminated(loanAccObj, paymentDate, valueDate, totalNonAllAmt, totalNonAmt)

    await contractRiskGrpService.doCalculatorDpdRiskGrp({
      loanAccObj,
      payload: {
        debtAckContractNumber: payload.debtAckContractNumber,
        calDate: paymentDate
      }
    })
    if (arrPaymentDetail.length) {
      const listAllocatedPaymentIds = lodash.uniq(arrPaymentDetail.map((item) => item.payment_id))
      await Promise.all([
        paymentDetailRepo.insBatchPaymentDetailV2(arrPaymentDetail),
        paymentRepo.updateLastAllocationDate(listAllocatedPaymentIds)
      ])
      message = '[MC-LMS] Thuc hien thanh toan thanh cong'
    }
    await clearCacheField(payload.debtAckContractNumber)
    return {
      statusCode: 200,
      code: 0,
      message,
      data: {
        totalNonAllocateAmt: totalNonAllAmt
      }
    }
  } catch (err) {
    await clearCacheField(payload.debtAckContractNumber)
    console.error('Error while repayment: ', err.message)
    return { statusCode: 500, code: 99, message: err.message }
  }
}

const repaymentFactoring = async function (payload) {
  try {
    const checkCache = await checkCacheField(payload.debtAckContractNumber)
    if (checkCache) {
      await sleep(DEFAULT_TIME_WAIT)
      return repaymentFactoring(payload)
    }
    setCacheField(payload.debtAckContractNumber)
    console.log(`START REPAYMENT FACTORING with payload ${JSON.stringify(payload)}`)
    const paymentDate = formatDate({ date: payload.paymentDate })
    const valueDate = formatDate({ date: payload.valueDate, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss })

    const [
        loanAccObj,
      listBillOnDue,
      listBillOnDueNotActive,
      listAllPaymentAllocate,
      listPaymentRemainAmort
    ] =
      await Promise.all([
        LoanAccountRepository.findOne({ where: { debt_ack_contract_number: payload.debtAckContractNumber } }),
        billOnDueRepo.getBillOnDuePaymentNotCompleteFactoring(global.poolRead, {
          ...payload,
        }),
        billOnDueRepo.getBillOnDuePaymentNotCompleteV3Factoring(
            payload.debtAckContractNumber,
            'asc',
            constant.BILL_ON_DUE.STATUS.NOT_ACTIVE,
        ),
        paymentAllocateRepo.findAllPaymentAllocateCode(),
        paymentRepo.getRemainAmortByContractNumberV2(payload.debtAckContractNumber),
      ])
    let message = '[MC-LMS] Nop tien vao thanh cong. Khong thuc hien phan bo duoc tien'

    let debtAckContractNumber = payload.debtAckContractNumber
    const dateRunJob = common.formatDate({ date: payload.valueDate })
    await checkTerminateFactoring({
      debtAckContractNumber,
      valueDate: dateRunJob
    })

    let totalNonAllAmt = lodash.sumBy(listPaymentRemainAmort, function (payment) {
      return Number(payment.non_allocated_amt)
    })
    if (loanAccObj.is_freeze) {
      await clearCacheField(payload.debtAckContractNumber)
      return {
        statusCode: 200,
        code: 0,
        message,
        data: {
          totalNonAllocateAmt: totalNonAllAmt
        }
      }
    }
    const listPayAllNotExpire = listAllPaymentAllocate.filter(
      (item) =>
        item.code == constant.PAY_ALLOCATE.NOT_EXPIRE &&
        item.start_time <= loanAccObj.activation_date &&
        item.end_time >= loanAccObj.activation_date
    )
    const listPayAllNotDue = listAllPaymentAllocate.filter(
      (item) =>
        item.code == constant.PAY_ALLOCATE.NOT_DUE &&
        item.start_time <= loanAccObj.activation_date &&
        item.end_time >= loanAccObj.activation_date
    )
    const listPayAllExpire = listAllPaymentAllocate.filter(
      (item) =>
        item.code == constant.PAY_ALLOCATE.EXPIRE &&
        item.start_time <= loanAccObj.activation_date &&
        item.end_time >= loanAccObj.activation_date
    )

    const arrPaymentDetail = []
    let object = {
      arrPaymentDetail,
      listPaymentRemainAmort: listPaymentRemainAmort.filter(
          (item) => formatDate({ date: item.value_date, format: constant.DATE_FORMAT.YYYYMMDD_HHmmss }) <= valueDate
      ),
      valueDate,
      isTerminateFactoring: payload.isTerminateFactoring,
      loanAccObj,
    }
    console.log('object: ', JSON.stringify(object))

    // kiểm tra xem có cần bổ sung suspend amt cho order trước không
    if (!payload.isTerminateFactoring && common.getDifferenceInDays(payload.paymentDate, loanAccObj.end_date) <= 0) {
      let notFullOrders = await loanAccountOrderRepo.getNotFullSuspendLoanAccountOrderByDebtAckContractNumber(global.poolRead, {
        debtAckContractNumber: payload.debtAckContractNumber
      })

      if (notFullOrders?.rows.length > 0) {
        for (const notFullOrder of notFullOrders?.rows) {
          if (common.getDifferenceInDays(valueDate, notFullOrder.suspend_date) !== 0) {
            continue
          }

          let notFullNumCycle = notFullOrder.order_index

          const [notFullAllActiveBill, notFullLoanAccountOrder] = await Promise.all([
            billOnDueRepo.findAllBillActiveWithNumCycle(
                payload.debtAckContractNumber,
                notFullNumCycle
            ),
            loanAccountOrderRepo.getLoanAccountOrderByDebtAckContractNumberAndNumCycle(global.poolRead, {
              debtAckContractNumber: payload.debtAckContractNumber,
              numCycle: notFullNumCycle
            })
          ])

          const hasRemainAmount = notFullAllActiveBill.some(bill => bill.remain_amount > 0);

          if (notFullAllActiveBill.length > 0 && !hasRemainAmount) {
            const notFullTotalBillAmt = lodash.sumBy(notFullAllActiveBill, function (payment) {
              return Number(payment.amount)
            })

            const notFullOrderAmount = notFullLoanAccountOrder?.rows?.[0]?.order_amt || 0

            if (notFullTotalBillAmt < notFullOrderAmount) {
              let notFullRefundAmt = notFullOrderAmount - notFullTotalBillAmt
              let remainRefundAmt = notFullRefundAmt - notFullLoanAccountOrder?.rows?.[0]?.suspend_amt

              if (remainRefundAmt > 0) {
                let isFullSuspend = false

                let suspendAmt = 0
                if (remainRefundAmt > totalNonAllAmt) {
                  suspendAmt = totalNonAllAmt
                } else {
                  let remainAmt = totalNonAllAmt - remainRefundAmt
                  let debt_tolerance_amt = loanAccObj.debt_tolerance_amt ? Number(loanAccObj.debt_tolerance_amt) : 0

                  if (remainAmt <= debt_tolerance_amt) {
                    suspendAmt = totalNonAllAmt
                  } else {
                    suspendAmt = remainRefundAmt
                  }

                  isFullSuspend = true
                }

                object = await suspendProcess(object, suspendAmt, payload.debtAckContractNumber, notFullNumCycle, isFullSuspend, new Date(valueDate))
              }
            }
          }
        }
      }
    }

    totalNonAllAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
      return Number(payment.non_allocated_amt)
    })

    const {
      mapAllBillOnDue,
    } = helper.getMapBillOnDueFactoring({
      listBillOnDue,
      listBillOnDueNotActive,
      paymentDate,
      listPayAllExpire,
      listPayAllNotExpire,
      listPayAllNotDue,
      totalNonAmt: totalNonAllAmt,
      partnerCode: loanAccObj.partner_code
    })

    const dues = []
    let flagAnnex = false
    let annexNumber

    const listBillOnDueTime = Object.keys(mapAllBillOnDue)
    listBillOnDueTime.sort((a, b) => a - b)
    for (const bodTime of listBillOnDueTime) {
      const mapObj = mapAllBillOnDue[bodTime]
      if (mapObj.type == null) {
        continue
      }
      const billOnDueCycle = mapObj.data
      if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.NOT_EXPIRED) {
        object = await repaymentProcessFactoring(object, listPayAllNotExpire, billOnDueCycle)
        dues.push(object.dueObj)
      } else if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.EXPIRED) {
        object = await repaymentProcessFactoring(object, listPayAllExpire, billOnDueCycle)
        dues.push(object.dueObj)
      } else if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.NOT_DUE) {
        object = await repaymentProcessFactoring(object, listPayAllNotDue, billOnDueCycle)
        dues.push(object.dueObj)
      } else if (mapObj.type == constant.BILL_ON_DUE.RULE_PAYMENT.ANNEX) {
        const totalNonAllocatedAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
          return Number(payment.non_allocated_amt)
        })
        let totalAnnexAmt = 0
        let mapToArray = []
        Object.values(billOnDueCycle).forEach((value) => {
          mapToArray = mapToArray.concat(value)
        })
        for (const bill of mapToArray) {
          totalAnnexAmt += Number(bill.remain_amount)
          annexNumber = bill.annex_number
        }
        if (totalNonAllocatedAmt >= totalAnnexAmt) {
          object = await repaymentProcessFactoring(object, listPayAllNotExpire, billOnDueCycle)
          flagAnnex = true
          object.dueObj.flagAnnex = true
          dues.push(object.dueObj)
        } else {
          continue
        }
      }
    }

    totalNonAllAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
      return Number(payment.non_allocated_amt)
    })

    if (object.isTerminateFactoring) {
      // logic xử lý tk treo và gạch nợ tiếp
      const [allActiveBill] = await Promise.all([
        billOnDueRepo.findAllBillActived(
            payload.debtAckContractNumber,
        ),
      ])

      const hasRemainAmount = allActiveBill.some(bill => bill.remain_amount > 0);

      if (!hasRemainAmount) {
        object = await suspendProcess(object, totalNonAllAmt, payload.debtAckContractNumber, null, true, new Date(valueDate))
      }
    }

    totalNonAllAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
      return Number(payment.non_allocated_amt)
    })

    handleUpdatePaidAmtAndReduntAmtFactoring(loanAccObj, dues, object)

    if (flagAnnex) {
      await handleUpdateStatusAnnexInsm(annexNumber, payload, loanAccObj)
    }

    await checkTerminated(loanAccObj, paymentDate, valueDate, totalNonAllAmt)

    if (arrPaymentDetail.length) {
      const listAllocatedPaymentIds = lodash.uniq(arrPaymentDetail.map((item) => item.payment_id))
      await Promise.all([
        paymentDetailRepo.insBatchPaymentDetailV2(arrPaymentDetail),
        paymentRepo.updateLastAllocationDate(listAllocatedPaymentIds)
      ])
      message = '[MC-LMS] Thuc hien thanh toan thanh cong'
    }

    await contractRiskGrpService.doCalculatorDpdRiskGrp({
      loanAccObj,
      payload: {
        debtAckContractNumber: payload.debtAckContractNumber,
        calDate: paymentDate
      }
    })

    await clearCacheField(payload.debtAckContractNumber)

    return {
      statusCode: 200,
      code: 0,
      message,
      data: {
        totalNonAllocateAmt: totalNonAllAmt
      }
    }
  } catch (err) {
    await clearCacheField(payload.debtAckContractNumber)
    console.error('Error while repayment factoring: ', err)
    console.error('Error while repayment factoring: ', err.message)
    return { statusCode: 500, code: 99, message: err.message }
  }
}

async function suspendProcess(object, suspendAmt, debtAckContractNumber, numCycleToRepayment, isFullSuspend = false, suspendDate = new Date()) {
  const { arrPaymentDetail, listPaymentRemainAmort, valueDate, isTerminateFactoring, loanAccObj } = object

  let remainAmt = suspendAmt
  let totalSuspendAmt = 0
  for (const paymentRemain of listPaymentRemainAmort) {
    const delta = Math.min(paymentRemain.non_allocated_amt, remainAmt)
    const postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.SUSPEND

    if (delta) {
      arrPaymentDetail.push({
        bill_id: null,
        payment_id: paymentRemain.id,
        amount: delta,
        owner_id: constant.config.ownerId,
        is_testing: constant.config.isTesting,
        created_by: constant.config.createdBy,
        post_affect: postAffect,
        debt_ack_contract_number: debtAckContractNumber,
        created_date: new Date(),
        updated_date: new Date(),
        value_date: valueDate
      })
      const plUpdateRemainPay = {
        paymentId: paymentRemain.id,
        deltaAmort: delta
      }
      await paymentRepo.updateRemainAmort(global.poolWrite, plUpdateRemainPay)
      actionAuditService.saveActionAudit(
          debtAckContractNumber,
          { actionAuditType: 'MATCHING_ALLOCATION', actionCodeType: 'ALLOCATED_MONEY_ON' },
          { title: postAffect }
      )
    }

    paymentRemain.non_allocated_amt -= delta
    totalSuspendAmt += delta
  }

  if (numCycleToRepayment) {
    await Promise.all([
      loanAccountV2Repo.updateLoanAccountSuspendAmtAndNonAllocationAmt( {
        debtAckContractNumber,
        suspendAmt: totalSuspendAmt
      }),
      loanAccountOrderRepo.addSuspendAmtByDebtAckContractNumberAndNumCycle(global.poolRead, {
        debtAckContractNumber,
        numCycle: numCycleToRepayment,
        suspendAmt: totalSuspendAmt,
        isFullSuspend: isFullSuspend,
        suspendDate: suspendDate,
      })
    ])
  } else {
    await Promise.all([
      loanAccountV2Repo.updateLoanAccountSuspendAmtAndNonAllocationAmt( {
        debtAckContractNumber,
        suspendAmt: totalSuspendAmt
      }),
      loanAccountOrderRepo.fillSuspendAmtByDebtAckContractNumber(global.poolRead, {
        debtAckContractNumber,
        numCycle: numCycleToRepayment,
        isFullSuspend: isFullSuspend,
        suspendDate: suspendDate,
      })
    ])
  }


  return {
    arrPaymentDetail,
    listPaymentRemainAmort,
    valueDate,
    isTerminateFactoring,
    loanAccObj
  }
}

async function refundProcess(
    suspendAmt,
    nonAllocateAmt,
    debtAckContractNumber
) {
  let arrPaymentDetail = []

  if (suspendAmt) {
    arrPaymentDetail.push({
      bill_id: null,
      payment_id: null,
      amount: suspendAmt,
      owner_id: constant.config.ownerId,
      is_testing: constant.config.isTesting,
      created_by: constant.config.createdBy,
      post_affect: constant.PAYMENT_DETAIL_POST_AFFECT.REFUND,
      debt_ack_contract_number: debtAckContractNumber,
      created_date: new Date(),
      updated_date: new Date(),
      value_date: new Date()
    })
    arrPaymentDetail.push({
      bill_id: null,
      payment_id: null,
      amount: -suspendAmt,
      owner_id: constant.config.ownerId,
      is_testing: constant.config.isTesting,
      created_by: constant.config.createdBy,
      post_affect: constant.PAYMENT_DETAIL_POST_AFFECT.SUSPEND,
      debt_ack_contract_number: debtAckContractNumber,
      created_date: new Date(),
      updated_date: new Date(),
      value_date: new Date()
    })
  }

  if (nonAllocateAmt) {
    let remainNonAllocateAmt = nonAllocateAmt
    const listPaymentRemainAmortCur = await paymentRepo.getRemainAmortByContractNumberV2(debtAckContractNumber)
    for (const paymentRemain of listPaymentRemainAmortCur) {
      const delta = Math.min(paymentRemain.non_allocated_amt, remainNonAllocateAmt)
      if (delta) {
        arrPaymentDetail.push({
          bill_id: null,
          payment_id: paymentRemain.id,
          amount: delta,
          owner_id: constant.config.ownerId,
          is_testing: constant.config.isTesting,
          created_by: constant.config.createdBy,
          post_affect: constant.PAYMENT_DETAIL_POST_AFFECT.REFUND,
          debt_ack_contract_number: debtAckContractNumber,
          created_date: new Date(),
          updated_date: new Date(),
          value_date: new Date()
        })
        const plUpdateRemainPay = {
          paymentId: paymentRemain.id,
          deltaAmort: delta
        }
        await paymentRepo.updateRemainAmort(global.poolWrite, plUpdateRemainPay)
      }
      remainNonAllocateAmt -= delta

      if (delta <= 0) {
        break;
      }
    }
  }

  await paymentDetailRepo.insBatchPaymentDetailV2(arrPaymentDetail)

  actionAuditService.saveActionAudit(
      debtAckContractNumber,
      { actionAuditType: 'REFUND_SUSPEND', actionCodeType: 'REFUND_SUSPEND' },
      { title: constant.PAYMENT_DETAIL_POST_AFFECT.REFUND }
  )

  await loanAccountV2Repo.updateLoanAccountSuspendAmtAndNonAllocationAmt({
    debtAckContractNumber,
    toCollect: suspendAmt + nonAllocateAmt,
    suspendAmt: -suspendAmt,
    nonAllocateAmt: -nonAllocateAmt,
  })
}

async function repaymentProcess(object, listPayRule = [], billOnDueCycle) {
  const { arrPaymentDetail, listPaymentRemainAmort, valueDate } = object
  const dueObj = {
    matchedPrinAmt: 0,
    matchedIntAmt: 0,
    matchedPreferentialIntAmt: 0,
    matchedLpiPrinAmt: 0,
    matchedLpiIrAmt: 0,
    matchedFeeAmt: 0,
    matchedFactoringFeeAmt: 0,
    matchedPenFeeAmt: 0
  }
  for (const payRule of listPayRule) {
    const listBillToPay = billOnDueCycle[payRule.type]
    if (!listBillToPay) {
      continue
    }
    for (const billObj of listBillToPay) {
      const rsIns = await installmentRepo.findById(global.poolWrite, { installmentId: billObj.installment_id })
      const insObj = rsIns.rows?.[0]
      let originAmt = insObj?.origin_amt || 0
      const billRemainAmount = billObj.remain_amount
      for (const paymentRemain of listPaymentRemainAmort) {
        const delta = Math.min(paymentRemain.non_allocated_amt, billObj.remain_amount)
        const postAffect = calListDues(dueObj, billObj.type, delta, insObj)
        if (delta) {
          arrPaymentDetail.push({
            bill_id: billObj.id,
            payment_id: paymentRemain.id,
            amount: delta,
            owner_id: billObj.owner_id || constant.config.ownerId,
            is_testing: constant.config.isTesting,
            created_by: constant.config.createdBy,
            post_affect: postAffect,
            debt_ack_contract_number: billObj.debt_ack_contract_number,
            created_date: new Date(),
            updated_date: new Date(),
            value_date: valueDate
          })
          const plUpdateRemainPay = {
            paymentId: paymentRemain.id,
            deltaAmort: delta
          }
          await paymentRepo.updateRemainAmort(global.poolWrite, plUpdateRemainPay)
          actionAuditService.saveActionAudit(
            billObj.debt_ack_contract_number,
            { actionAuditType: 'MATCHING_ALLOCATION', actionCodeType: 'ALLOCATED_MONEY_ON' },
            { title: postAffect }
          )
        }
        billObj.remain_amount -= delta
        paymentRemain.non_allocated_amt -= delta
        if (billObj.remain_amount == 0) {
          billObj.payment_status = constant.BILL_ON_DUE.PAYMENT_STATUS.DONE
          originAmt = 0
        } else {
          billObj.payment_status = constant.BILL_ON_DUE.PAYMENT_STATUS.NOT_DONE
          originAmt -= delta
        }
      }

      const plUpdate = {
        remainPrinAmount: billObj.remain_amount,
        paymentStatus: billObj.payment_status,
        installmentId: billObj.installment_id,
        billId: billObj.id,
        originAmt: [constant.INSTALLMENT.TYPE.LPI_PRIN, constant.INSTALLMENT.TYPE.LPI_INT].includes(insObj?.type)
          ? originAmt
          : undefined,
        status: constant.BILL_ON_DUE.STATUS.ACTIVE
      }

      if (billObj.is_annex == constant.BILL_ON_DUE.IS_ANNEX.TRUE) {
        const remainAmountIns = insObj?.remain_amount
        const plUpdateInsm = {
          remainPrinAmount: billRemainAmount,
          paymentStatus: remainAmountIns - billObj.amount == 0 ? constant.BILL_ON_DUE.PAYMENT_STATUS.DONE : 2,
          installmentId: billObj.installment_id
        }
        await Promise.all([
          installmentRepo.updateInsmRemainAmt(global.poolWrite, plUpdateInsm),
          billOnDueRepo.updateBillWithPaymentStatusAndRemainAmt(global.poolWrite, plUpdate)
        ])
      } else {
        await Promise.all([
          installmentRepo.updatePaymentStatusAndRemainAmt(global.poolWrite, plUpdate),
          billOnDueRepo.updateBillWithPaymentStatusAndRemainAmt(global.poolWrite, plUpdate)
        ])
      }
    }
  }
  return {
    arrPaymentDetail,
    listPaymentRemainAmort,
    dueObj,
    valueDate
  }
}

async function repaymentProcessFactoring(object, listPayRule = [], billOnDueCycle) {
  const { arrPaymentDetail, listPaymentRemainAmort, valueDate, isTerminateFactoring, loanAccObj } = object
  const dueObj = {
    matchedPrinAmt: 0,
    matchedIntAmt: 0,
    matchedPreferentialIntAmt: 0,
    matchedLpiPrinAmt: 0,
    matchedLpiIrAmt: 0,
    matchedFeeAmt: 0,
    matchedFactoringFeeAmt: 0,
    matchedPenFeeAmt: 0
  }
  for (const payRule of listPayRule) {
    const listBillToPay = billOnDueCycle[payRule.type]
    if (!listBillToPay) {
      continue
    }
    for (const billObj of listBillToPay) {
      const rsIns = await installmentRepo.findById(global.poolWrite, { installmentId: billObj.installment_id })
      const insObj = rsIns.rows?.[0]
      let originAmt = insObj?.origin_amt || 0
      const billRemainAmount = billObj.remain_amount
      for (const paymentRemain of listPaymentRemainAmort) {
        const delta = Math.min(paymentRemain.non_allocated_amt, billObj.remain_amount)
        const postAffect = calListDues(dueObj, billObj.type, delta, insObj)
        if (delta) {
          arrPaymentDetail.push({
            bill_id: billObj.id,
            payment_id: paymentRemain.id,
            amount: delta,
            owner_id: billObj.owner_id || constant.config.ownerId,
            is_testing: constant.config.isTesting,
            created_by: constant.config.createdBy,
            post_affect: postAffect,
            debt_ack_contract_number: billObj.debt_ack_contract_number,
            created_date: new Date(),
            updated_date: new Date(),
            value_date: valueDate
          })
          const plUpdateRemainPay = {
            paymentId: paymentRemain.id,
            deltaAmort: delta
          }
          await paymentRepo.updateRemainAmort(global.poolWrite, plUpdateRemainPay)
          actionAuditService.saveActionAudit(
            billObj.debt_ack_contract_number,
            { actionAuditType: 'MATCHING_ALLOCATION', actionCodeType: 'ALLOCATED_MONEY_ON' },
            { title: postAffect }
          )
        }
        billObj.remain_amount -= delta
        paymentRemain.non_allocated_amt -= delta
        if (billObj.remain_amount == 0) {
          billObj.payment_status = constant.BILL_ON_DUE.PAYMENT_STATUS.DONE
          originAmt = 0
        } else {
          billObj.payment_status = constant.BILL_ON_DUE.PAYMENT_STATUS.NOT_DONE
          originAmt -= delta
        }
      }

      const plUpdate = {
        remainPrinAmount: billObj.remain_amount,
        paymentStatus: billObj.payment_status,
        installmentId: billObj.installment_id,
        billId: billObj.id,
        originAmt: [constant.INSTALLMENT.TYPE.PRIN, constant].includes(insObj?.type)
          ? undefined
          : originAmt,
        status: constant.BILL_ON_DUE.STATUS.ACTIVE
      }

      if (billObj.is_annex == constant.BILL_ON_DUE.IS_ANNEX.TRUE) {
        const remainAmountIns = insObj?.remain_amount
        const plUpdateInsm = {
          remainPrinAmount: billRemainAmount,
          paymentStatus: remainAmountIns - billObj.amount == 0 ? constant.BILL_ON_DUE.PAYMENT_STATUS.DONE : 2,
          installmentId: billObj.installment_id
        }
        await Promise.all([
          installmentRepo.updateInsmRemainAmt(global.poolWrite, plUpdateInsm),
          billOnDueRepo.updateBillWithPaymentStatusAndRemainAmt(global.poolWrite, plUpdate)
        ])
      } else {
        await Promise.all([
          installmentRepo.updatePaymentStatusAndRemainAmt(global.poolWrite, plUpdate),
          billOnDueRepo.updateBillWithPaymentStatusAndRemainAmt(global.poolWrite, plUpdate)
        ])
      }

      let isOverDue = billObj.isOverDue
      let isOnDue = billObj.isOnDue
      let isNotDue = billObj.isNotDue
      if (isOverDue) {
        object = await suspendProcess(object, 0, billObj.debt_ack_contract_number, billObj.num_cycle, true, new Date(valueDate))
      } else if (isOnDue || isNotDue) {
        // nếu có cờ isTerminateFactoring thì chỉ xử lý tk treo ở sau cùng
        if (!isTerminateFactoring) {
          // logic xử lý tk treo và gạch nợ tiếp
          const [allActiveBill, loanAccountOrder] = await Promise.all([
            billOnDueRepo.findAllBillActiveWithNumCycle(
                billObj.debt_ack_contract_number,
                billObj.num_cycle
            ),
            loanAccountOrderRepo.getLoanAccountOrderByDebtAckContractNumberAndNumCycle(global.poolRead, {
              debtAckContractNumber: billObj.debt_ack_contract_number,
              numCycle: billObj.num_cycle
            })
          ])

          const hasRemainAmount = allActiveBill.some(bill => bill.remain_amount > 0);

          if (!hasRemainAmount) {
            const totalBillAmt = lodash.sumBy(allActiveBill, function (payment) {
              return Number(payment.amount)
            })

            const orderAmount = loanAccountOrder?.rows?.[0]?.order_amt || 0

            let totalNonAllAmt = lodash.sumBy(listPaymentRemainAmort, function (payment) {
              return Number(payment.non_allocated_amt)
            })

            if (totalNonAllAmt <= 0) {
              // hiện tại thì không có gì đặc biệt
            } else if (totalBillAmt < orderAmount) {
              let refundAmt = orderAmount - totalBillAmt
              let suspendAmt = 0

              let isFullSuspend = false
              if (refundAmt > totalNonAllAmt) {
                suspendAmt = totalNonAllAmt
              } else {
                let remainAmt = totalNonAllAmt - refundAmt
                let debt_tolerance_amt = loanAccObj.debt_tolerance_amt ? Number(loanAccObj.debt_tolerance_amt) : 0

                if (remainAmt <= debt_tolerance_amt) {
                  suspendAmt = totalNonAllAmt
                } else {
                  suspendAmt = refundAmt
                }

                isFullSuspend = true
              }

              object = await suspendProcess(object, suspendAmt, billObj.debt_ack_contract_number, billObj.num_cycle, isFullSuspend, new Date(valueDate))
            }
          }
        }
      }
    }
  }
  return {
    arrPaymentDetail,
    listPaymentRemainAmort,
    dueObj,
    valueDate,
    isTerminateFactoring,
    loanAccObj,
  }
}

async function repaymentProcessSuspend(object, listPayRule = [], billOnDueCycle) {
  let { arrPaymentDetail, totalNonAllAmt, valueDate, loanAccObj } = object
  const dueObj = {
    matchedPrinAmt: 0,
    matchedIntAmt: 0,
    matchedPreferentialIntAmt: 0,
    matchedLpiPrinAmt: 0,
    matchedLpiIrAmt: 0,
    matchedFeeAmt: 0,
    matchedFactoringFeeAmt: 0,
    matchedPenFeeAmt: 0
  }
  for (const payRule of listPayRule) {
    const listBillToPay = billOnDueCycle[payRule.type]
    if (!listBillToPay) {
      continue
    }
    for (const billObj of listBillToPay) {
      const rsIns = await installmentRepo.findById(global.poolWrite, { installmentId: billObj.installment_id })
      const insObj = rsIns.rows?.[0]
      let originAmt = insObj?.origin_amt || 0
      const billRemainAmount = billObj.remain_amount
      while (totalNonAllAmt > 0 && billObj.remain_amount > 0) {
        const delta = Math.min(totalNonAllAmt, billObj.remain_amount)
        const postAffect = calListDues(dueObj, billObj.type, delta, insObj)
        if (delta) {
          arrPaymentDetail.push({
            bill_id: billObj.id,
            payment_id: null,
            amount: delta,
            owner_id: billObj.owner_id || constant.config.ownerId,
            is_testing: constant.config.isTesting,
            created_by: constant.config.createdBy,
            post_affect: postAffect,
            debt_ack_contract_number: billObj.debt_ack_contract_number,
            created_date: new Date(),
            updated_date: new Date(),
            value_date: valueDate
          })
          arrPaymentDetail.push({
            bill_id: billObj.id,
            payment_id: null,
            amount: -delta,
            owner_id: billObj.owner_id || constant.config.ownerId,
            is_testing: constant.config.isTesting,
            created_by: constant.config.createdBy,
            post_affect: constant.PAYMENT_DETAIL_POST_AFFECT.SUSPEND,
            debt_ack_contract_number: billObj.debt_ack_contract_number,
            created_date: new Date(),
            updated_date: new Date(),
            value_date: valueDate
          })
          actionAuditService.saveActionAudit(
            billObj.debt_ack_contract_number,
            { actionAuditType: 'MATCHING_ALLOCATION', actionCodeType: 'ALLOCATED_MONEY_ON' },
            { title: postAffect }
          )
        }
        billObj.remain_amount -= delta
        totalNonAllAmt -= delta
        if (billObj.remain_amount == 0) {
          billObj.payment_status = constant.BILL_ON_DUE.PAYMENT_STATUS.DONE
          originAmt = 0
        } else {
          billObj.payment_status = constant.BILL_ON_DUE.PAYMENT_STATUS.NOT_DONE
          originAmt -= delta
        }

        const plUpdateLoan = {
          suspendAmtPaid: delta,
          loanId: loanAccObj.loan_id
        }
        await debtAckContractRepo.updateSuspendAmtPaid(plUpdateLoan)
      }

      const plUpdate = {
        remainPrinAmount: billObj.remain_amount,
        paymentStatus: billObj.payment_status,
        installmentId: billObj.installment_id,
        billId: billObj.id,
        originAmt: [constant.INSTALLMENT.TYPE.PRIN, constant].includes(insObj?.type)
            ? undefined
            : originAmt,
        status: constant.BILL_ON_DUE.STATUS.ACTIVE
      }

      if (billObj.is_annex == constant.BILL_ON_DUE.IS_ANNEX.TRUE) {
        const remainAmountIns = insObj?.remain_amount
        const plUpdateInsm = {
          remainPrinAmount: billRemainAmount,
          paymentStatus: remainAmountIns - billObj.amount == 0 ? constant.BILL_ON_DUE.PAYMENT_STATUS.DONE : 2,
          installmentId: billObj.installment_id
        }
        await Promise.all([
          installmentRepo.updateInsmRemainAmt(global.poolWrite, plUpdateInsm),
          billOnDueRepo.updateBillWithPaymentStatusAndRemainAmt(global.poolWrite, plUpdate)
        ])
      } else {
        await Promise.all([
          installmentRepo.updatePaymentStatusAndRemainAmt(global.poolWrite, plUpdate),
          billOnDueRepo.updateBillWithPaymentStatusAndRemainAmt(global.poolWrite, plUpdate)
        ])
      }
    }
  }
  return {
    arrPaymentDetail,
    totalNonAllAmt,
    dueObj,
    valueDate,
    loanAccObj,
  }
}
function calListDues(dueObj, type, amt, insObj) {
  let postAffect = ''
  if (type == constant.BILL_ON_DUE.TYPE.PRIN) {
    dueObj.matchedPrinAmt += Number(amt)
    postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.RFIN
  } else if (type == constant.BILL_ON_DUE.TYPE.INT) {
    dueObj.matchedIntAmt += Number(amt)
    postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.DBRS
  } else if (type == constant.BILL_ON_DUE.TYPE.PREFERENTIAL_INT) {
    dueObj.matchedPreferentialIntAmt += Number(amt)
    postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.PDBRS
  } else if (type == constant.BILL_ON_DUE.TYPE.FACTORING_FEE) {
    dueObj.matchedFactoringFeeAmt += Number(amt)
    postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.FRHONO
  } else if (type == constant.BILL_ON_DUE.TYPE.LPI_PRIN) {
    dueObj.matchedLpiPrinAmt += Number(amt)
    postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.RENRH
  } else if (type == constant.BILL_ON_DUE.TYPE.LPI_INT) {
    dueObj.matchedLpiIrAmt += Number(amt)
    postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.RENRH
  } else if (type == constant.BILL_ON_DUE.TYPE.FEE) {
    dueObj.matchedFeeAmt += Number(amt)
    postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.RHONO
    if (insObj.description == 'Phí phạt tất toán sớm') {
      postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.DP
    }
  } else if (type == constant.BILL_ON_DUE.TYPE.PEN_FEE) {
    dueObj.matchedFeeAmt += Number(amt)
    postAffect = constant.PAYMENT_DETAIL_POST_AFFECT.DP
  }
  return postAffect
}
async function checkTerminated(loanAccObj, paymentDate, valueDate, totalNonAllAmt, originalTotalNonAllAmt = 0) {
  const rsData = await installmentRepo.findByDebtAckContractNumberAndPaymentStatusNotCompleted({
    debtAckContractNumber: loanAccObj.debt_ack_contract_number
  })

  const isTerminated = rsData.rowCount == 0
  const newStatus = (isTerminated && loanAccObj.status == constant.LOAN_ACC_STATUS.ACT ) ? constant.LOAN_ACC_STATUS.TER : loanAccObj.status
  const paymentStatus = isTerminated ? constant.PAYMENT_STATUS.DONE : constant.PAYMENT_STATUS.PROCESSING
  const terminationDate = isTerminated ? valueDate : null

  const payloadUpdateDebtAck = {
    debtAckContractId: loanAccObj.loan_id,
    paymentStatus,
    status: newStatus,
    terminationDate
  }

  if (loanAccObj.status !== newStatus) {
    handleStatusUpdate(loanAccObj, payloadUpdateDebtAck, paymentDate, totalNonAllAmt)

    let checkLoanAccObj = await LoanAccountRepository.findOne({ where: { debt_ack_contract_number: loanAccObj.debt_ack_contract_number } })

    if (newStatus == constant.LOAN_ACC_STATUS.TER && helper.isSendSmsPartnerCode(checkLoanAccObj.partner_code)) {
      let smsContent = ''
      if (totalNonAllAmt > 0) {
        smsContent = global.config?.data?.sms?.smsOverPaidTerminate?.replace('${totalPaymentAmount}', originalTotalNonAllAmt)
      } else {
        const custObj = await crmService.getCustomerInfo(
            global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + checkLoanAccObj.cust_id,
            {}
        )

        smsContent = global.config?.data?.sms?.smsTerminate?.replace('${contractNumber}', checkLoanAccObj.debt_ack_contract_number)
            .replace('${customerName}', custObj?.fullName)
            .replace('${terminationDate}', common.formatDate({ date: terminationDate }))
      }

      smsService.sendSMS({
        phoneNumber: checkLoanAccObj.phone_number,
        content: smsContent,
      })
    }

    if (common.isFactoringLoanChannel(checkLoanAccObj.partner_code)
        && newStatus == constant.LOAN_ACC_STATUS.TER
        && (Number(checkLoanAccObj.suspend_amt || 0) + Number(checkLoanAccObj.non_allocation_amt || 0)) > 0) {
      let rsPayment = await paymentRepo.getAllByDebtAckContractNumber(global.poolRead, {
        debtAckContractNumber: checkLoanAccObj.debt_ack_contract_number
      })
      const sumPaidAmt = lodash.sumBy(rsPayment.rows, function (item) {
        return Number(item.installment_amort)
      })

      losService.callReceivedRefundRequest({
        contract_number: checkLoanAccObj.debt_ack_contract_number,
        cust_id: checkLoanAccObj.cust_id,
        amount: checkLoanAccObj.apr_limit_amt,
        start_date: checkLoanAccObj.start_date,
        end_date: checkLoanAccObj.end_date,
        lpi: checkLoanAccObj.lpi_amt,
        int_amount: checkLoanAccObj.int_amt,
        fee_amount: Number(checkLoanAccObj.fee_amt || 0) + Number(checkLoanAccObj.factoring_fee_amt || 0),
        invoice_value: checkLoanAccObj.order_amt,
        refund_value: Number(checkLoanAccObj.suspend_amt || 0) + Number(checkLoanAccObj.non_allocation_amt || 0),
        created_by: 'system',
        paid_amount: sumPaidAmt,
      })
    }
  }
}
function handleStatusUpdate(loanAccObj, payloadUpdateDebtAck, paymentDate, totalNonAllAmt) {
  if (payloadUpdateDebtAck.status === constant.LOAN_ACC_STATUS.TER) {
    crmService.terminationDebt(global.crmServiceLink, loanAccObj.debt_ack_contract_number)
    losService.callDeactiveKunn(loanAccObj, payloadUpdateDebtAck.terminationDate)
    backendMobileService.callUpdateStatusBE(
      loanAccObj.debt_ack_contract_number,
      constant.DEBT_ACK_STATUS.TERMINATED_STR
    )
    if (helper.isLosUnitedPartnerCode(loanAccObj.partner_code)) {
      crmService.updateTerminate(loanAccObj, paymentDate, totalNonAllAmt)
    }
  }
  loanStatusHstService.insertNewAndUpdateOldRecord(loanAccObj.debt_ack_contract_number, payloadUpdateDebtAck.status)
  loanAccountV2Repo.updateLoanStatusAndPaymentStatus(payloadUpdateDebtAck)
}
async function handleUpdateStatusAnnexInsm(annexNumber, payload, loanAccObj) {
  const [maxCycleAnnexInstallment, annex] = await Promise.all([
    installmentRepo.findInstallmentIsAnnex(payload.debtAckContractNumber),
    loanAnnexRepo.findLoanAnnexByAnnexNumber(annexNumber)
  ])
  const plArchiveIns = {
    newStatus: constant.INSTALLMENT.STATUS.ARCHIVE,
    debtAckContractNumber: payload.debtAckContractNumber,
    status: constant.INSTALLMENT.STATUS.ACTIVE,
    closed: constant.INSTALLMENT.CLOSE.FALSE,
    annexNumber,
    isPaymentStatus: 1
  }
  if (
    maxCycleAnnexInstallment &&
    annex?.annex_type == constant.ANNEX.TYPE.EARLY_TERMINATION &&
    loanAccObj.partner_code != constant.PARTNER_CODE.VOUCHER
  ) {
    plArchiveIns.maxNumcycle = maxCycleAnnexInstallment.num_cycle
  }
  if (annex?.annex_type == constant.ANNEX.TYPE.FULL_EARLY_TERMINATION) {
    plArchiveIns.isPaymentStatus = 0
  }
  console.log('plArchiveIns', JSON.stringify(plArchiveIns))
  const listInsmArchive = await installmentRepo.updateStatusByDebtAckContractAndPaymentNotClt(plArchiveIns)

  const plActiveInsAnnex = {
    newStatus: constant.INSTALLMENT.STATUS.ACTIVE,
    debtAckContractNumber: payload.debtAckContractNumber,
    status: constant.INSTALLMENT.STATUS.DEACTIVE,
    annexNumber
  }
  const listInsmActive = await installmentRepo.updateStatusByDebtAckContract(global.poolWrite, plActiveInsAnnex)

  const plUpdateLoanAnnex = {
    annexStatusNew: constant.ANNEX.STATUS.DONE,
    annexNumber,
    annexStatusOld: constant.ANNEX.STATUS.INIT
  }
  actionAuditService.saveActionAudit(
    payload.debtAckContractNumber,
    { actionAuditType: 'TERMINATION', actionCodeType: 'ACTIVE_NEW_CLIENT_REQUEST' },
    { title: 'Update status annex ACT', createdUser: annex.created_by }
  )
  loanAnnexRepo.updateStatusLoanAnnex(global.poolWrite, plUpdateLoanAnnex)
  await loanAmortRepo.updateAmortFlagActive(
    payload.debtAckContractNumber,
    constant.FLAG_ACTIVE,
    constant.FLAG_NOT_ACTIVE
  )
  await loanAmortRepo.updateAmortFlagActiveByAnnexNumber(annexNumber, constant.FLAG_ACTIVE)
  const listPromotionDeductionInit = await installmentDeductionRepo.findListInstallmentDeduction(
    payload.debtAckContractNumber,
    constant.FLAG_ACTIVE_INIT
  )
  if (listPromotionDeductionInit.length) {
    await Promise.all(
      listInsmArchive.map(async (item) => {
        await installmentDeductionRepo.updateStatusByInstallmentId(
          constant.FLAG_NOT_ACTIVE,
          constant.FLAG_ACTIVE,
          item.id
        )
      })
    )
    await Promise.all(
      listInsmActive.map(async (item) => {
        await installmentDeductionRepo.updateStatusByInstallmentId(
          constant.FLAG_ACTIVE,
          constant.FLAG_ACTIVE_INIT,
          item.id
        )
      })
    )
    const listActivePromotionDeduction = await installmentDeductionRepo.findListInstallmentDeduction(
      payload.debtAckContractNumber
    )

    const promotion = await promotionRepo.findPromotionByDebtAckContractNumber(payload.debtAckContractNumber)
    const sumDeductionAmount = lodash.sumBy(listActivePromotionDeduction, function (item) {
      return Number(item.deduction_amount)
    })
    const revenueAmount = Number(promotion.total_promotion_amount) - sumDeductionAmount
    const deltaRevenueAmount = revenueAmount - Number(promotion.revenue_amount)
    promotionRepo.updatePromotion({
      debtAckContractNumber: payload.debtAckContractNumber,
      revenueAmount: deltaRevenueAmount
    })
  }
}
async function handleUpdatePaidAmtAndReduntAmt(loanAccObj, dues, object) {
  const plUpdateAmtPaid = {
    prinPaid: 0,
    intPaid: 0,
    feePaidAmt: 0,
    lpiPaid: 0,
    toCollect: 0,
    loanId: loanAccObj.loan_id
  }
  for (const dueObj of dues) {
    plUpdateAmtPaid.prinPaid += dueObj.matchedPrinAmt
    plUpdateAmtPaid.intPaid += dueObj.matchedIntAmt
    plUpdateAmtPaid.feePaidAmt += dueObj.matchedFeeAmt
    plUpdateAmtPaid.lpiPaid += Number(dueObj.matchedLpiPrinAmt) + Number(dueObj.matchedLpiIrAmt)
    const sumDueMatchedAmount =
      dueObj.matchedPrinAmt +
      dueObj.matchedIntAmt +
      dueObj.matchedFeeAmt +
      Number(dueObj.matchedLpiPrinAmt) +
      Number(dueObj.matchedLpiIrAmt)
    if (dueObj.flagAnnex) {
      plUpdateAmtPaid.toCollect += sumDueMatchedAmount
    }
  }
  if (
    plUpdateAmtPaid.prinPaid ||
    plUpdateAmtPaid.intPaid ||
    plUpdateAmtPaid.feePaidAmt ||
    plUpdateAmtPaid.lpiPaid ||
    plUpdateAmtPaid.toCollect
  ) {
    debtAckContractRepo.updateAmtPaid(plUpdateAmtPaid)
  }
  const totalNonAllAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
    return Number(payment.non_allocated_amt)
  })

  if (plUpdateAmtPaid.prinPaid > 0) {
    merchantLimitRepo.updateRemainLimit(global.poolWrite, {
      amount: plUpdateAmtPaid.prinPaid,
      contractNumber: loanAccObj.contract_number
    })
    //callback update to los if sme product
    if(!helper.isLosUnitedPartnerCode(loanAccObj.partner_code))
    {
      const contractNumber = loanAccObj.contract_number;
      const kunnNumber = loanAccObj.debt_ack_contract_number;
      losService.callUpdatePayment({kunnNumber,contractNumber});
    }

  }
  const plUpdateLoan = {
    nonAllocationAmt: totalNonAllAmt,
    loanId: loanAccObj.loan_id
  }
  debtAckContractRepo.updateLoanAcc(global.poolWrite, plUpdateLoan)
}

async function handleUpdatePaidAmtAndReduntAmtFactoring(loanAccObj, dues, object) {
  const plUpdateAmtPaid = {
    prinPaid: 0,
    intPaid: 0,
    normalIntPaid: 0,
    preferentialIntPaid: 0,
    feePaidAmt: 0,
    factoringFeePaidAmt: 0,
    lpiPaid: 0,
    toCollect: 0,
    loanId: loanAccObj.loan_id
  }
  for (const dueObj of dues) {
    plUpdateAmtPaid.prinPaid += dueObj.matchedPrinAmt
    plUpdateAmtPaid.intPaid += dueObj.matchedIntAmt
    plUpdateAmtPaid.normalIntPaid += dueObj.matchedIntAmt

    plUpdateAmtPaid.intPaid += dueObj.matchedPreferentialIntAmt
    plUpdateAmtPaid.preferentialIntPaid += dueObj.matchedPreferentialIntAmt

    plUpdateAmtPaid.feePaidAmt += dueObj.matchedFeeAmt
    plUpdateAmtPaid.factoringFeePaidAmt += dueObj.matchedFactoringFeeAmt
    plUpdateAmtPaid.lpiPaid += Number(dueObj.matchedLpiPrinAmt) + Number(dueObj.matchedLpiIrAmt)

    const sumDueMatchedAmount =
        dueObj.matchedPrinAmt +
        dueObj.matchedIntAmt +
        dueObj.matchedPreferentialIntAmt +
        dueObj.matchedFeeAmt +
        dueObj.matchedFactoringFeeAmt +
        Number(dueObj.matchedLpiPrinAmt) +
        Number(dueObj.matchedLpiIrAmt)
    if (dueObj.flagAnnex) {
      plUpdateAmtPaid.toCollect += sumDueMatchedAmount
    }
  }
  if (
      plUpdateAmtPaid.prinPaid ||
      plUpdateAmtPaid.intPaid ||
      plUpdateAmtPaid.normalIntPaid ||
      plUpdateAmtPaid.preferentialIntPaid ||
      plUpdateAmtPaid.feePaidAmt ||
      plUpdateAmtPaid.factoringFeePaidAmt ||
      plUpdateAmtPaid.lpiPaid ||
      plUpdateAmtPaid.toCollect
  ) {
    debtAckContractRepo.updateAmtPaid(plUpdateAmtPaid)
  }
  const totalNonAllAmt = lodash.sumBy(object.listPaymentRemainAmort, function (payment) {
    return Number(payment.non_allocated_amt)
  })

  if (plUpdateAmtPaid.prinPaid > 0) {
    merchantLimitRepo.updateRemainLimit(global.poolWrite, {
      amount: plUpdateAmtPaid.prinPaid,
      contractNumber: loanAccObj.contract_number
    })
    //callback update to los if sme product
    if(!helper.isLosUnitedPartnerCode(loanAccObj.partner_code))
    {
      const contractNumber = loanAccObj.contract_number;
      const kunnNumber = loanAccObj.debt_ack_contract_number;
      losService.callUpdatePayment({kunnNumber,contractNumber});
    }

  }
  const plUpdateLoan = {
    nonAllocationAmt: totalNonAllAmt,
    loanId: loanAccObj.loan_id
  }
  debtAckContractRepo.updateLoanAcc(global.poolWrite, plUpdateLoan)
}

module.exports = {
  doRepayment,
  refundProcess,
  doRepaymentSuspend,
  checkTerminateFactoring,
}
