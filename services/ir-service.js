const common = require('../utils/common')
const irRepo = require('../repositories/ir-repo')
const installmentRepo = require('../repositories/installment-repo')
const debtActContractRepo = require('../repositories/debt-ack-contract-repo')
const irChargeRepo = require('../repositories/ir-charge-repo')
const billOnDueRepo = require('../repositories/bill-on-due-repo')
const constant = require('../utils/constant')
const crmService = require('../other-services/crm-service')
const productService = require('../other-services/product-service')
const tranLogRepo = require('../repositories/tran-log-repo')
const contractRiskGrpRepo = require('../repositories/contract-risk-grp-repo')
const moment = require('moment')
const loanAccountRepo = require('../repositories/loan-account-repo')
const paymentRepo = require('../repositories/payment-repo')
const _ = require('lodash')
const { getDueDateCalLpi } = require('../utils/helper')
const { Add } = require('../utils/set-operator')
const { InstallmentRepository, BillOnDueRepository, IrRepository, LoanAccountRepository} = require('../repositories-v2')
const loanAmortRepo = require('../repositories/loan-amort-repo')
const feeRepo = require('../repositories/fees-repo')
const irFactoringService = require("./ir-factoring-service");
const Decimal = require('decimal.js');

const doCalcuIrEveryDay = async function (payload) {
  const { debtAckContractNumber } = payload

  if (!debtAckContractNumber) {
    return null
  }

  const loanAccountObj = await LoanAccountRepository.findOne({ where: {
      debt_ack_contract_number: debtAckContractNumber
    } });

  if (common.isFactoringLoanChannel(loanAccountObj?.partner_code)) {
    return irFactoringService.calcuIrEveryDayRefactoring(payload)
  } else {
    return calcuIrEveryDay(payload)
  }
}

/**
 * Ham tinh lai hang ngay
 * @param {*} req
 * @param {*} res
 */
const calcuIrEveryDay = async function (payload) {
  try {
    common.log('req body calcuIrEveryDay: ', JSON.stringify(payload))
    if (payload.debtAckContractNumber == undefined || payload.irDate == undefined) {
      return {
        statusCode: 200,
        code: 1,
        message: '[MC-LMS] Phai truyen day du thong tin contractNumber va irDate'
      }
    }
    // kiem tra xem ngay irDate da duoc tinh lai hay chua
    const rsIrDate = await irRepo.findByContractNumberAndIrDate(payload)
    if (rsIrDate.rowCount > 0) {
      return {
        statusCode: 200,
        code: 1,
        message: '[LMS-MC] Ngay irDate truyen vao da duoc tinh lai'
      }
    }
    payload.paymentStatus = 1
    payload.status = 1
    const [listInstallment, listDebtActContract] = await Promise.all([
      installmentRepo.findByContractNumberAndPaymentStatusNotCompleted(global.poolRead, {
        debtAckContractNumber: payload.debtAckContractNumber
      }),
      debtActContractRepo.findByContractNumberAndStatus(global.poolRead, {
        debtAckContractNumber: payload.debtAckContractNumber,
        status: constant.LOAN_ACC_STATUS.ACT
      })
    ])
    if (listInstallment.rowCount == 0 || listDebtActContract.rowCount == 0) {
      return {
        statusCode: 200,
        code: 1,
        message: '[MC-LMS] Khong tim thay KUNN da giai ngan de tinh lai'
      }
    }
    if(common.isFactoringLoanChannel(listDebtActContract?.rows[0]?.partner_code)){
      return {
        statusCode: 200,
        code: 1,
        message: '[MC-LMS] Khong tinh lai lai hang ngay voi KUNN factoring'
      }
    }
    const payloadIr = []
    const debtAckContractObj = listDebtActContract.rows[0]
    payload.ownerId = debtAckContractObj.owner_id || constant.config.ownerId
    payload.createdBy = debtAckContractObj.created_by || constant.config.createdBy
    payload.isTesting = debtAckContractObj.is_testing || constant.config.isTesting

    const irDate = new Date(payload.irDate)
    const payloadIrCharge = {
      debtAckContractNumber: debtAckContractObj.debt_ack_contract_number,
      productCode: debtAckContractObj.product_code
    }
    const listIr = await irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, payloadIrCharge)
    let irRate, irRatePrinOverdue, irRateIrOverdue
    for (const irObj of listIr.rows) {
      if (irObj.ir_type == constant.IR_CHARGE_TYPE.ON_DUE_PRIN) {
        irRate = irObj
      } else if (irObj.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_PRIN) {
        irRatePrinOverdue = irObj
      } else if (irObj.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_INTEREST) {
        irRateIrOverdue = irObj
      }
    }
    if (!irRate || !irRatePrinOverdue) {
      return {
        statusCode: 200,
        code: 1,
        message: '[MC-LMS] Missing data ircharge'
      }
    }
    const currentPrinObj = listInstallment.rows.find(
      (item) => item.type == constant.INSTALLMENT.TYPE.PRIN && irDate > item.start_date && irDate <= item.end_date
    )
    const currentIrObj = listInstallment.rows.find(
      (item) => item.type == constant.INSTALLMENT.TYPE.INT && irDate > item.start_date && irDate <= item.end_date
    )

    const payloadUpdateDebtAck = {
      lpiAmt: 0,
      toCollect: 0,
      loanId: debtAckContractObj.loan_id
    }
    for (const installment of listInstallment.rows) {
      const dueDateCalLpi = getDueDateCalLpi(installment.end_date, installment.due_date)
      const irDateString = common.formatDate({ date: irDate })
      if (
        irDateString > dueDateCalLpi &&
        installment.type == constant.INSTALLMENT.TYPE.PRIN &&
        irRatePrinOverdue &&
        Number(irRatePrinOverdue?.ir_value) > 0
      ) {
        await calculateLpiInstallment({
          installment,
          payloadUpdateDebtAck,
          irRateOverdueObj: irRatePrinOverdue,
          currentInsmObj: currentPrinObj,
          payload,
          payloadIr,
          lpiType: constant.INSTALLMENT.TYPE.LPI_PRIN
        })
      } else if (
        irDateString > dueDateCalLpi &&
        installment.type == constant.INSTALLMENT.TYPE.INT &&
        irRateIrOverdue &&
        Number(irRateIrOverdue?.ir_value) > 0
      ) {
        await calculateLpiInstallment({
          installment,
          payloadUpdateDebtAck,
          irRateOverdueObj: irRateIrOverdue,
          currentInsmObj: currentIrObj,
          payload,
          payloadIr,
          lpiType: constant.INSTALLMENT.TYPE.LPI_INT
        })
      }
    }
    if (payloadUpdateDebtAck.lpiAmt || payloadUpdateDebtAck.toCollect) {
      await debtActContractRepo.updateDataIrJobAmt(payloadUpdateDebtAck)
    }

    if (payloadIr.length > 0) {
      await IrRepository.save(payloadIr)
      return {
        statusCode: 200,
        code: 0,
        message: '[MC-LMS] Thuc thi tinh lai theo ngay thanh cong'
      }
    }

    return {
      statusCode: 200,
      code: 1,
      message: '[MC-LMS] Hop dong moi chua phat sinh lai'
    }
  } catch (error) {
    console.log(error)
    console.error('Error while ', error.message)
    return { statusCode: 500, code: 99, message: error.message }
  }
}
const createProvision = async function (req, res) {
  try {
    const pl = req.body
    pl.irType = 1
    let totalIr = 0
    let totalPrin = 0
    let instalNum = 0
    const rsPrin = await installmentRepo.findByDebtAckContractNumberAndPaymentStatusNotCompleted(pl)
    if (rsPrin.rowCount > 0) {
      for (const i in rsPrin.rows) {
        if (rsPrin.rows[i].type == 1) {
          totalPrin += Number(rsPrin.rows[i].remain_amount)
        }
        if (
          rsPrin.rows[i].type == 2 &&
          rsPrin.rows[i].ir_num_cycle == 1 &&
          moment(pl.toDate).toDate() <= moment(rsPrin.rows[i].end_date).toDate()
        ) {
          instalNum = 1
        }
      }
    }
    const rsLoanAcc = await debtActContractRepo.getLoanAccByDebtAckContract(global.poolRead, pl)
    const loanAccountObj = rsLoanAcc.rows[0]
    pl.fromDate = common.convertDatetoString(
      moment(pl.toDate)
        .toDate()
        .setDate(loanAccountObj.bill_day + 1),
      'yyyy-mm-dd'
    )

    const rsIr = await irRepo.findIrByDebtAckContractNumberAndFromToIrDate(global.poolRead, pl)
    if (rsIr.rowCount > 0) {
      for (const i in rsIr.rows) {
        totalIr += Number(rsIr.rows[i].ir_amount)
      }
    }
    const tranLogObj = await insertTranLog(req, loanAccountObj, totalPrin + totalIr)
    const custObj = await crmService.getCustomerInfo(
      global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccountObj.cust_id
    )
    const rsRiskGroup = await contractRiskGrpRepo.findContractRiskGrp(global.poolRead, pl)
    let riskGrpObj = {}
    if (rsRiskGroup.rowCount > 0) riskGrpObj = rsRiskGroup.rows[0]
    res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Lay thong tin thanh cong',
        data: {
          tenor: loanAccountObj.tenor,
          valueDate: moment(pl.toDate).toDate(),
          prinAmt: totalPrin,
          intAmt: common.roundUp(totalIr, global.calcuCfg.scale, loanAccountObj.partner_code),
          instalNum,
          custId: custObj.custId,
          custIdType: custObj.idType,
          custIdNo: custObj.idNumber,
          custName: custObj.fullName,
          contractId: loanAccountObj.debt_ack_contract_number,
          tranId: tranLogObj.tran_id,
          loanId: loanAccountObj.loan_id,
          tranDate: tranLogObj.tran_date,
          contractNo: loanAccountObj.debt_ack_contract_number,
          currency: loanAccountObj.ccycd,
          tranType: tranLogObj.tran_type,
          riskGroup: riskGrpObj.dpd_risk_grp,
          userReq: global.createdBy,
          startDate: loanAccountObj.start_date
        }
      })
    )
  } catch (err) {
    console.log(err)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}
const createIcne = async function (req, res) {
  try {
    const pl = req.body
    pl.irType = 1

    let totalIr = 0
    let totalPrin = 0
    const rsPrin = await installmentRepo.findByDebtAckContractNumberAndPaymentStatusNotCompleted(pl)
    if (rsPrin.rowCount > 0) {
      for (const i in rsPrin.rows) {
        if (rsPrin.rows[i].type == 1) {
          totalPrin += Number(rsPrin.rows[i].remain_amount)
        }
      }
    }
    const rsLoanAcc = await debtActContractRepo.getLoanAccByDebtAckContract(global.poolRead, pl)
    const loanAccountObj = rsLoanAcc.rows[0]

    const fromDateNew = new Date(pl.queryDate)
    fromDateNew.setMonth(fromDateNew.getMonth() - 1)
    fromDateNew.setDate(loanAccountObj.bill_day + 1)
    const toDateNew = new Date(pl.queryDate)
    toDateNew.setDate(0)
    pl.fromDate = fromDateNew
    pl.toDate = toDateNew

    const rsIr = await irRepo.findIrByDebtAckContractNumberAndFromToIrDate(global.poolRead, pl)
    if (rsIr.rowCount > 0) {
      for (const i in rsIr.rows) {
        totalIr += Number(rsIr.rows[i].ir_amount)
      }
    }
    const tranLogObj = await insertTranLog(req, loanAccountObj, totalPrin + totalIr)
    const custObj = await crmService.getCustomerInfo(
      global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + loanAccountObj.cust_id
    )
    const rsRiskGroup = await contractRiskGrpRepo.findContractRiskGrp(global.poolRead, pl)
    let riskGrpObj = {}
    if (rsRiskGroup.rowCount > 0) riskGrpObj = rsRiskGroup.rows[0]
    res.status(200).json(
      (res.body = {
        code: 0,
        message: '[LMS-MC] Lay thong tin thanh cong',
        data: {
          tenor: loanAccountObj.tenor,
          valueDate: moment(pl.queryDate).toDate(),
          prinAmt: totalPrin,
          intAmt: common.roundUp(totalIr, global.calcuCfg.scale, loanAccountObj.partner_code),
          custId: custObj.custId,
          custIdType: custObj.idType,
          custIdNo: custObj.idNumber,
          custName: custObj.fullName,
          contractId: loanAccountObj.debt_ack_contract_number,
          tranId: tranLogObj.tran_id,
          loanId: loanAccountObj.loan_id,
          tranDate: tranLogObj.tran_date,
          contractNo: loanAccountObj.debt_ack_contract_number,
          currency: loanAccountObj.ccycd,
          tranType: tranLogObj.tran_type,
          riskGroup: riskGrpObj.dpd_risk_grp,
          userReq: global.createdBy
        }
      })
    )
  } catch (err) {
    console.log(err)
    res.status(err.statusCode || 500).json((res.body = { code: 99, message: err.message }))
  }
}
async function insertTranLog(req, loanAccObj, amt) {
  const dateNow = moment().toDate()
  const pl = {
    contractNumber: loanAccObj.contract_number,
    loanId: loanAccObj.loan_id,
    tranType: '',
    amtNumber: amt,
    tranDate: dateNow,
    valueDate: dateNow,
    tranDesc: '',
    createdUser: global.createdBy,
    sessionId: dateNow.getTime(),
    ownerId: constant.config.ownerId
  }
  const rs = await tranLogRepo.insTranLog(global.poolWrite, pl)
  if (rs.rowCount > 0) {
    return rs.rows[0]
  } else {
    return {}
  }
}

async function getSimulationLpi({ simulationDate, debtAckContractNumber }) {
  if (!debtAckContractNumber || !simulationDate) {
    return {
      statusCode: 400,
      code: 1,
      message: 'Input khong hop le'
    }
  }
  simulationDate = moment(simulationDate).format(constant.DATE_FORMAT.YYYYMMDD2)

  const findLoanAcc = await loanAccountRepo.findLoanAccByDebtAckContractNumber(debtAckContractNumber)

  if (!findLoanAcc.length) {
    return {
      statusCode: 400,
      code: 1,
      message: 'Khong tim thay KUNN'
    }
  }
  const loanAccountObj = findLoanAcc[0]
  const [listAllInsm, listIrCharge, listIr, listPayment] = await Promise.all([
    installmentRepo.findListActiveInstallment({ debtAckContractNumber, simulationDate }),
    irChargeRepo.findByDebtAckContractNumberAndProductCodeV2(debtAckContractNumber),
    irRepo.findLpiIrByDebtAckContractNumberAndFromIrDate({ debtAckContractNumber, toDate: simulationDate }),
    paymentRepo.getListPaymentDetailByDebtAckContractNumber({ debtAckContractNumber, toDate: simulationDate })
  ])
  const listInsm = listAllInsm.filter((item) =>
    [constant.INSTALLMENT.TYPE.PRIN, constant.INSTALLMENT.TYPE.INT].includes(item.type)
  )
  const listDueDate = _.uniqBy(listAllInsm, (insm) => moment(insm.due_date).format(constant.DATE_FORMAT.YYYYMMDD2)).map(
    (item) => moment(item.due_date).format(constant.DATE_FORMAT.YYYYMMDD2)
  )

  const irRatePrinOverdue = listIrCharge.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_PRIN)
  const irRateIrOverdue = listIrCharge.find((item) => item.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_INTEREST)
  let result = []
  for (const installment of listInsm) {
    console.log('start installment id', installment.id)
    const compareDueDateEndDate = installment.due_date.getTime() == installment.end_date.getTime()
    let dueDate = moment(installment.due_date)
    if (compareDueDateEndDate) {
      dueDate.add(1, 'day')
    }
    dueDate = dueDate.format(constant.DATE_FORMAT.YYYYMMDD2)

    let irType = constant.IR_CHARGE_TYPE.OVER_DUE_PRIN
    let irValue = Number(irRatePrinOverdue.ir_value)
    if (installment.type == constant.INSTALLMENT.TYPE.INT) {
      irType = constant.IR_CHARGE_TYPE.OVER_DUE_INTEREST
      irValue = Number(irRateIrOverdue.ir_value)
    }
    const listIrFilterByType = listIr.filter((item) => item.ir_type == irType && item.installment_id == installment.id)
    const lpiObjByPrin = {}
    const listPrinRate = []
    const tempResult = []

    for (const irRecord of listIrFilterByType) {
      let prinAmount = Number(irRecord.prin_amount)
      if (!prinAmount) {
        prinAmount = common.round(
          Number(irRecord.ir_amount) / (irRecord.ir_rate / constant.CALCUCFG.totalDayOfYear),
          constant.CALCUCFG.scale
        )
      }
      const prinRateKey = `${irRecord.installment_id}_${prinAmount}_${irRecord.ir_charge_id}`
      if (!listPrinRate.includes(prinRateKey)) {
        listPrinRate.push(prinRateKey)
        lpiObjByPrin[prinRateKey] = {
          installmentId: installment.id,
          numCycle: installment.ir_num_cycle || installment.num_cycle,
          base: prinAmount,
          irFromDate: moment(irRecord.ir_date).format(constant.DATE_FORMAT.YYYYMMDD2),
          irToDate: moment(irRecord.ir_date).format(constant.DATE_FORMAT.YYYYMMDD2),
          numberOfDays: 1,
          rate: irRecord.ir_rate,
          lpiAmt: Number(irRecord.ir_amount),
          matchedLpiAmt: 0,
          remainingLpiAmt: Number(irRecord.ir_amount)
        }
      } else {
        const irDate = moment(irRecord.ir_date).format(constant.DATE_FORMAT.YYYYMMDD2)
        if (listDueDate.includes(irDate)) {
          lpiObjByPrin[prinRateKey].lpiAmt = common.roundUp(lpiObjByPrin[prinRateKey].lpiAmt, -3)
          lpiObjByPrin[prinRateKey].remainingLpiAmt = common.roundUp(lpiObjByPrin[prinRateKey].remainingLpiAmt, -3)
        }
        lpiObjByPrin[prinRateKey].lpiAmt += Number(irRecord.ir_amount)
        lpiObjByPrin[prinRateKey].remainingLpiAmt += Number(irRecord.ir_amount)
        lpiObjByPrin[prinRateKey].irToDate = irDate
        lpiObjByPrin[prinRateKey].numberOfDays += 1
      }
    }
    let maxIrDateCalculated
    for (const prinRateKey of listPrinRate) {
      !maxIrDateCalculated && (maxIrDateCalculated = lpiObjByPrin[prinRateKey].irToDate)
      maxIrDateCalculated =
        lpiObjByPrin[prinRateKey].irToDate >= maxIrDateCalculated
          ? lpiObjByPrin[prinRateKey].irToDate
          : maxIrDateCalculated

      const listLpiInsm = listAllInsm.filter((item) => item.ir_on_prin == lpiObjByPrin[prinRateKey].installmentId)
      if (listLpiInsm.length) {
        for (const lpiInsm of listLpiInsm) {
          const listPaymentLpi = listPayment.filter((item) => Number(item.installment_id) == Number(lpiInsm.id))
          for (const paymentLpi of listPaymentLpi) {
            const matchedAmount = Math.min(Number(paymentLpi.amount), lpiObjByPrin[prinRateKey].remainingLpiAmt)
            lpiObjByPrin[prinRateKey].matchedLpiAmt += matchedAmount
            lpiObjByPrin[prinRateKey].remainingLpiAmt -= matchedAmount
          }
        }
      }
      tempResult.push(lpiObjByPrin[prinRateKey])
    }
    if (Number(installment.remain_amount) == 0) {
      result = [...result, ...tempResult]
      continue
    }
    console.log('maxIrDateCalculated', maxIrDateCalculated)

    const checkPaymentAndNotCalIr = listPayment.find(
      (item) => moment(item.payment_date).format(constant.DATE_FORMAT.YYYYMMDD2) == maxIrDateCalculated
    )
    if ((checkPaymentAndNotCalIr && simulationDate > maxIrDateCalculated) || !tempResult.length) {
      const irFromDate = !tempResult.length ? dueDate : maxIrDateCalculated
      const lpiLastObj = {
        installmentId: installment.id,
        numCycle: installment.ir_num_cycle || installment.num_cycle,
        base: Number(installment.remain_amount),
        irFromDate,
        irToDate: irFromDate,
        numberOfDays: 0,
        rate: irValue,
        lpiAmt: 0,
        matchedLpiAmt: 0,
        remainingLpiAmt: 0
      }
      tempResult.push(lpiLastObj)
    }
    let lastLpiObj = tempResult[0]
    for (const tempElement of tempResult) {
      if (tempElement.irFromDate >= lastLpiObj.irToDate || tempElement.base < lastLpiObj.base) {
        lastLpiObj = tempElement
      }
    }
    if (simulationDate > lastLpiObj.irToDate) {
      const startDate = lastLpiObj.irToDate
      console.log('startDate', startDate)
      console.log('simulationDate', simulationDate)
      const numDayCalLpi = common.getDifferencesDays(startDate, simulationDate)
      const bonusLpiFromNow =
        (irValue / constant.CALCUCFG.totalDayOfYear) * Number(installment.remain_amount) * numDayCalLpi
      lastLpiObj.lpiAmt += Number(bonusLpiFromNow)
      lastLpiObj.remainingLpiAmt += Number(bonusLpiFromNow)
      lastLpiObj.irToDate = simulationDate
      console.log('numDayCalLpi', numDayCalLpi)

      lastLpiObj.numberOfDays += numDayCalLpi
    }
    result = [...result, ...tempResult]
  }
  const totalOverdueInterest = _.sumBy(result, function (res) {
    return common.roundUp(res.remainingLpiAmt, -3)
  })
  for (let i = 0; i < result.length; i++) {
    if (i === 0) {
      result[i].accRemainingLpiAmt = result[i].remainingLpiAmt
    } else {
      if (result[i].installmentId == result[i - 1].installmentId) {
        result[i].accRemainingLpiAmt = result[i].remainingLpiAmt + result[i - 1].accRemainingLpiAmt
      } else {
        result[i].accRemainingLpiAmt = result[i].remainingLpiAmt + common.roundUp(result[i - 1].accRemainingLpiAmt, -3)
      }
    }
  }
  for (const res of result) {
    res.originRemainingLpiAmt = res.remainingLpiAmt
    res.originLpiAmt = res.lpiAmt
    res.accRemainingLpiAmt = common.roundUp(res.accRemainingLpiAmt, -3)
    res.remainingLpiAmt = common.roundUp(res.remainingLpiAmt, -3)
    res.matchedLpiAmt = common.roundUp(res.matchedLpiAmt, -3)
    res.lpiAmt = common.roundUp(res.lpiAmt, -3)
    res.invoiceNumber = res.installmentId
    res.fromDate = res.irFromDate
    res.toDate = res.irToDate
    res.numberOfDay = res.numberOfDays
    let rateValue = new Decimal(res.rate).times(100);
    res.rate = rateValue.toNumber()
  }
  return {
    statusCode: 200,
    code: 0,
    message: 'Simulation LPI thanh cong',
    data: {
      lpiAmt: common.roundUp(totalOverdueInterest, 2, loanAccountObj.partner_code),
      priAmt: Number(loanAccountObj.apr_limit_amt),
      outStandingAmt: Number(loanAccountObj.prin_amt),
      lpis: result,
      simulationDate
    }
  }
}

function checkActiveDateToAdjustInterest(activeDate, adjustDate) {
  adjustDate = moment(adjustDate).format(constant.DATE_FORMAT.YYYYMMDD2)
  const diffMonths = Math.abs(moment(activeDate).diff(moment(adjustDate), 'month', true))
  if (diffMonths < 6) {
    return false
  }
  if (diffMonths > 6 && diffMonths % 3) {
    return false
  }
  return true
}
async function adjustInterestRateKunn(loanAccObj, newIrRate, adjustDate = new Date()) {
  try {
    adjustDate = moment(adjustDate).format(constant.DATE_FORMAT.YYYYMMDD2)
    const dateCalInterest = new Date(adjustDate)
    dateCalInterest.setDate(dateCalInterest.getDate() - 1)
    // if (!checkActiveDateToAdjustInterest(loanAccObj.active_date, adjustDate)) {
    //   return {
    //     code: 1,
    //     debtAckContractNumber: loanAccObj.debt_ack_contract_number
    //   }
    // }
    const newIrPrinOnDue = newIrRate.find((item) => item.rateType == constant.IR_PRIN_NORMAL_RATE_TYPE)
    const newIrPrinOnDueValue = newIrPrinOnDue.intRateVal / 100
    const newIrPrinOverdue = newIrRate.find((item) => item.rateType == constant.IR_PRIN_OVERDUE_RATE_TYPE)
    const newIrPrinOverdueValue = newIrPrinOverdue.intRateVal / 100
    const listIrCharge = await irChargeRepo.findByDebtAckContractNumberAndProductCodeV2(
      loanAccObj.debt_ack_contract_number
    )

    const currentPrinOnDueIrRate = listIrCharge.find(
      (item) => item.ir_type == constant.IR_CHARGE_TYPE.ON_DUE_PRIN
    )?.ir_value
    const currentPrinOverDueIrRate = listIrCharge.find(
      (item) => item.ir_type == constant.IR_CHARGE_TYPE.OVER_DUE_PRIN
    )?.ir_value
    if (!currentPrinOnDueIrRate || !currentPrinOverDueIrRate) {
      return {
        code: 1,
        debtAckContractNumber: loanAccObj.debt_ack_contract_number
      }
    }
    if (newIrPrinOnDueValue == currentPrinOnDueIrRate && newIrPrinOverdueValue == currentPrinOverDueIrRate) {
      return {
        code: 1,
        debtAckContractNumber: loanAccObj.debt_ack_contract_number
      }
    }

    console.log(`KUNN ${loanAccObj.debt_ack_contract_number} cập nhật lãi suất`)

    const recordsIr = []

    recordsIr.push([
      loanAccObj.debt_ack_contract_number,
      undefined,
      loanAccObj.product_code,
      undefined,
      'Lãi xuất gốc',
      constant.IR_CHARGE_TYPE.ON_DUE_PRIN,
      newIrPrinOnDueValue,
      constant.IR_CHARGE_STATUS.ACTIVE,
      constant.config.ownerId,
      constant.config.isTesting,
      constant.config.createdBy,
      null,
      null,
      null,
      null
    ])

    recordsIr.push([
      loanAccObj.debt_ack_contract_number,
      undefined,
      loanAccObj.product_code,
      undefined,
      'Lãi xuất gốc quá hạn',
      constant.IR_CHARGE_TYPE.OVER_DUE_PRIN,
      newIrPrinOverdueValue,
      constant.IR_CHARGE_STATUS.ACTIVE,
      constant.config.ownerId,
      constant.config.isTesting,
      constant.config.createdBy,
      null,
      null,
      null,
      null
    ])

    // const listInstallment =
    //   await installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
    //     debtAckContractNumber: loanAccObj.debt_ack_contract_number,
    //     closed: constant.INSTALLMENT.CLOSE.FALSE
    //   })
    // const listInsCompleteted = await installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
    //   debtAckContractNumber: loanAccObj.debt_ack_contract_number,
    //   closed: constant.INSTALLMENT.CLOSE.TRUE,
    //   isNotCompleted: false
    // })
    const [listInstallment,listInsCompleteted,loanAmort] = await Promise.all([

      installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
        debtAckContractNumber: loanAccObj.debt_ack_contract_number,
        closed: constant.INSTALLMENT.CLOSE.FALSE
      }),
      installmentRepo.findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2({
        debtAckContractNumber: loanAccObj.debt_ack_contract_number,
        closed: constant.INSTALLMENT.CLOSE.TRUE,
        isNotCompleted: false
      }),
      loanAmortRepo.findCurrentAmort(loanAccObj.debt_ack_contract_number),
    ])
    const loanAmortNew = await loanAmortRepo.insertLoanAmort({
      debtAckContractNumber: loanAmort.debt_ack_contract_number,
      amtAmort: loanAmort.amt_amort,
      intRate: newIrPrinOnDueValue,
      startDate: loanAmort.start_date,
      endDate: loanAmort.end_date,
      tenor: loanAmort.tenor,
      prevAmortId: loanAmort.amort_id
    },constant.FLAG_ACTIVE_INIT);

    let sumIntAmt = listInsCompleteted.filter(instal => instal.type == constant.INSTALLMENT.TYPE.INT).reduce((acc, instal) => acc + Number(instal.amount||0), 0)
    const recordsInsAnnex = []
    const listIds = []
    const recordsInsNew = []
    const recordsInsOld = []
    for (const installment of listInstallment) {
      if (installment.type == constant.INSTALLMENT.TYPE.INT) {
        // const effectLongTime = new Date(adjustDate).getTime()
        const effectLongTime = moment(adjustDate).toDate().getTime()

        const beginDate = installment.ir_from_date || installment.start_date
        const afterDate = installment.ir_to_date || installment.end_date
        const strBeginDate = common.convertDatetoString(beginDate, 'yyyy-mm-dd')
        const strAfterDate = common.convertDatetoString(afterDate, 'yyyy-mm-dd')
        const numDayInCycle = common.dateDiff(afterDate, beginDate).days()
        const outStandingPrin = Number(installment.outstanding_prin)

        const dataInsertInstallment = {
          debt_ack_contract_id: installment.debt_ack_contract_id,
          num_cycle: installment.num_cycle,
          amount: installment.amount,
          remain_amount: installment.remain_amount,
          cycle_date: installment.cycle_date,
          type: installment.type,
          payment_status: installment.payment_status,
          owner_id: installment.owner_id,
          is_testing: installment.is_testing,
          created_by: installment.created_by,
          contract_number: installment.contract_number,
          debt_ack_contract_number: installment.debt_ack_contract_number,
          start_date: installment.start_date,
          end_date: installment.end_date,
          due_date: installment.due_date,
          ir_num_cycle: installment.ir_num_cycle,
          is_annex: installment.is_annex,
          ir_from_date: installment.ir_from_date,
          ir_to_date: installment.ir_to_date,
          ir_on_prin: installment.ir_on_prin,
          status: constant.INSTALLMENT.STATUS.ACTIVE,
          closed: installment.closed,
          outstanding_prin: outStandingPrin,
          description: installment.description,
          ir_rate: installment.ir_rate,
          amort_id: loanAmortNew
        }

        if (effectLongTime > beginDate.getTime() && effectLongTime < afterDate.getTime()) {
          const numDayToEffectFirst = common.dateDiff(new Date(adjustDate), new Date(strBeginDate)).days()
          // console.log('numDayToEffectFirst', numDayToEffectFirst)
          const irFromStartDateToEffectDate = common.roundUp(
            (outStandingPrin / constant.CALCUCFG.totalDayOfYear) * numDayToEffectFirst * currentPrinOnDueIrRate,
            constant.CALCUCFG.scale,
            loanAccObj.partner_code
          )
          if (irFromStartDateToEffectDate > 0) {
            sumIntAmt += irFromStartDateToEffectDate
            recordsInsAnnex.push({
              ...dataInsertInstallment,
              amount: irFromStartDateToEffectDate,
              remain_amount: irFromStartDateToEffectDate,
              ir_from_date: strBeginDate,
              ir_to_date: adjustDate,
              description: `Điều chỉnh lãi ngày ${adjustDate}`
            })
          }
          const numDayToEffectSecond = common.dateDiff(new Date(strAfterDate), new Date(adjustDate)).days()
          // console.log('numDayToEffectSecond', numDayToEffectSecond)

          const irFromEffectDateToEndDate = common.roundUp(
            (outStandingPrin / constant.CALCUCFG.totalDayOfYear) * numDayToEffectSecond * newIrPrinOnDueValue,
            constant.CALCUCFG.scale
          )
          if (irFromEffectDateToEndDate > 0) {
            sumIntAmt += irFromEffectDateToEndDate
            recordsInsAnnex.push({
              ...dataInsertInstallment,
              amount: irFromEffectDateToEndDate,
              remain_amount: irFromEffectDateToEndDate,
              ir_from_date: adjustDate,
              ir_to_date: strAfterDate,
              description: `Điều chỉnh lãi ngày ${adjustDate}`,
              ir_rate: newIrPrinOnDueValue
            })
          }
          listIds.push(installment.id)
        } else if (effectLongTime <= beginDate.getTime()) {
          const irNew = common.roundUp(
            (outStandingPrin / constant.CALCUCFG.totalDayOfYear) * numDayInCycle * newIrPrinOnDueValue,
            constant.CALCUCFG.scale,
            loanAccObj.partner_code
          )
          sumIntAmt += irNew
          recordsInsAnnex.push({
            ...dataInsertInstallment,
            amount: irNew,
            remain_amount: irNew,
            ir_from_date: beginDate,
            ir_to_date: afterDate,
            description: `Điều chỉnh lãi ngày ${adjustDate}`,
            ir_rate: newIrPrinOnDueValue
          })
          listIds.push(installment.id)
        }else{
          const { id, ...restInstallment } = installment;
          recordsInsNew.push({...restInstallment,
            outstanding_prin: installment.outstanding_prin || Number(installment.outstanding_prin),
            ir_rate: newIrPrinOnDueValue,
            amort_id: loanAmortNew})
          recordsInsOld.push(installment.id)
          //tinh lai tong lai
          sumIntAmt += Number(installment.amount || 0)
        }
      }else{
        const { id, ...restInstallment } = installment;
        recordsInsNew.push({...restInstallment,
          outstanding_prin: installment.outstanding_prin || Number(installment.outstanding_prin),
          ir_rate: newIrPrinOnDueValue,
          amort_id: loanAmortNew})
        recordsInsOld.push(installment.id)
      }
    }

    await Promise.all([
      irChargeRepo.updateStatusIrCharge(loanAccObj.debt_ack_contract_number),
      installmentRepo.updateStatusByListId(constant.INSTALLMENT.STATUS.ARCHIVE, listIds),
      loanAccountRepo.updateIntLoanAccount(loanAccObj.debt_ack_contract_number, sumIntAmt || loanAccObj.int_amt),
      loanAmortRepo.updateAmortFlagActiveByAmortId(loanAmortNew, constant.FLAG_ACTIVE),
      loanAmortRepo.updateAmortFlagActiveByAmortId(loanAmort.amort_id, constant.FLAG_NOT_ACTIVE),
      installmentRepo.updateStatusByListId(constant.INSTALLMENT.STATUS.ARCHIVE, recordsInsOld),
    ])

    await Promise.all([
      irChargeRepo.insBatchIrCharge(recordsIr),
      installmentRepo.insertBatchInsmAnnexV2(recordsInsAnnex),
      installmentRepo.insertBatchInsmAnnexV2(recordsInsNew),
    ])
    return {
      code: 0,
      data: {
        debtAckContractNumber: loanAccObj.debt_ack_contract_number,
        currentPrinOnDueIrRate,
        newIrPrinOnDueValue,
        currentPrinOverDueIrRate,
        newIrPrinOverdueValue
      }
    }
  } catch (error) {
    console.log(`ERROR adjustInterestRateKunn ${loanAccObj.debt_ack_contract_number}`, JSON.stringify(error))
    return {
      code: 1,
      debtAckContractNumber: loanAccObj.debt_ack_contract_number
    }
  }
}
async function adjustInterestRateBatchJob({adjustDate = new Date(),adjustRate, debtAckContractNumber}) {
  try {
    // console.log('adjustDate', adjustDate)
    const listLoanAccount = await loanAccountRepo.getListActiveLoanAccByPartnerCodeAndContractType(
      constant.PARTNER_CODE.MISA,
      constant.CONTRACT_TYPE.CREDITLINE,
      debtAckContractNumber
    )
    const mapProductCodeObj = {}
    const listKunnUpdateIr = []
    for (const loanAccObj of listLoanAccount) {
      const { product_code } = loanAccObj
      if (!mapProductCodeObj[product_code]) {
        mapProductCodeObj[product_code] = await productService.getProduct(product_code)
      }
      const res = await adjustInterestRateKunn(
        loanAccObj,
        adjustRate ?? mapProductCodeObj[product_code].rate,
        moment(adjustDate).format(constant.DATE_FORMAT.YYYYMMDD2)
      )
      if (res.code == 0) {
        listKunnUpdateIr.push(res.data)
      }
    }
    return {
      statusCode: 200,
      code: 0,
      message: 'Cập nhật lãi suất thành công',
      listKunnUpdateIr
    }
  } catch (error) {
    console.log('ERROR adjustInterestRateBatchJob', error.message)
    return {
      statusCode: 500,
      code: 0,
      message: error.message
    }
  }
}
async function runIrToDate({ debtAckContractNumber, toDate, fromDate }) {
  try {
    common.log(`Run lpi from ${fromDate} to ${toDate}`)

    const startDate = new Date(fromDate)
    while (startDate <= new Date(toDate)) {
      const bodyIr = { debtAckContractNumber, irDate: common.formatDate({ date: startDate }) }
      const result = await calcuIrEveryDay(bodyIr)
      common.log(`RESPONSE Calcu LPI :${JSON.stringify(result)}`)
      startDate.setDate(startDate.getDate() + 1)
    }

    common.log('END calcuIrEveryDayBatchJob')
    return { statusCode: 200, code: 0, message: 'Success calcuIrEveryDayBatchJob' }
  } catch (error) {
    return { statusCode: 500, code: -1, message: error.message }
  }
}

async function deactiveLpi({ debtAckContractNumber, irDate }) {
  await Promise.all([
    irRepo.deActiveByDebtAckContractNumberMoreDate(debtAckContractNumber, irDate),
    installmentRepo.deActiveInsmLpiByDebtAckContractNumberMoreDate(debtAckContractNumber, irDate),
    billOnDueRepo.deActiveLpiDueByDebtAckContractNumberMoreDate(debtAckContractNumber, irDate)
  ])
}

async function calculateLpiInstallment({
  installment,
  payloadUpdateDebtAck,
  irRateOverdueObj,
  payloadIr,
  payload,
  currentInsmObj,
  lpiType
}) {
  const calcuCfg = constant.CALCUCFG
  const insCycleDate = common.formatDate({ date: installment.cycle_date })
  const insStartDate = common.formatDate({ date: installment.start_date })
  const insEndDate = common.formatDate({ date: installment.end_date })
  const insDueDate = common.formatDate({ date: installment.due_date })
  const irAmtExpire = common.roundUp(
    (irRateOverdueObj?.ir_value / calcuCfg.totalDayOfYear) * installment.remain_amount,
    calcuCfg.scaleOfDay
  )
  payloadIr.push({
    ir_charge_id: irRateOverdueObj.id,
    installment_id: installment.id,
    ir_date: payload.irDate,
    ir_rate: irRateOverdueObj?.ir_value,
    ir_type: irRateOverdueObj?.ir_type,
    ir_amount: irAmtExpire,
    owner_id: payload.ownerId,
    is_testing: payload.isTesting,
    created_by: payload.createdBy,
    contract_number: payload.contractNumber,
    debt_ack_contract_number: payload.debtAckContractNumber,
    prin_amount: installment.remain_amount
  })

  const insObjNow = currentInsmObj || installment
  const lpiAtCycle = insObjNow.ir_num_cycle || insObjNow.num_cycle
  const plUpdateRemainPrinExpire = {
    remainAmount: irAmtExpire,
    debtAckContractNumber: installment.debt_ack_contract_number,
    numCycleOfType: installment.num_cycle,
    lpiAtCycle,
    type: lpiType
  }
  const insExistObj = await InstallmentRepository.findOne({
    where: {
      ir_on_prin: installment.id,
      lpi_at_cycle: lpiAtCycle,
      type: lpiType
    }
  })
  let reAmt = 0
  if (insExistObj) {
    const reAmtDelta = common.roundUp(Number(insExistObj.origin_amt) + irAmtExpire, calcuCfg.scale)
    plUpdateRemainPrinExpire.originAmt = irAmtExpire
    reAmt = reAmtDelta - Number(insExistObj.remain_amount)
    plUpdateRemainPrinExpire.remainAmount = reAmt
    plUpdateRemainPrinExpire.irToDate = payload.irDate
    plUpdateRemainPrinExpire.installmentId = insExistObj.id

    const rsUpdateInsm = await InstallmentRepository.update({
      where: { id: insExistObj.id },
      data: {
        remain_amount: Add(plUpdateRemainPrinExpire.remainAmount),
        amount: Add(plUpdateRemainPrinExpire.remainAmount),
        origin_amt: Add(plUpdateRemainPrinExpire.originAmt),
        ir_to_date: payload.irDate,
        payment_status: constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE
      }
    })
    if (rsUpdateInsm.length > 0) {
      const objUpdateInsm = rsUpdateInsm[0]
      await BillOnDueRepository.update({
        where: { installment_id: objUpdateInsm.id },
        data: {
          amount: Add(reAmt),
          remain_amount: Add(reAmt),
          payment_status: constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE
        }
      })
    }
  } else {
    reAmt = common.roundUp(irAmtExpire, calcuCfg.scale)
    const objInsInsm = await InstallmentRepository.save({
      debt_ack_contract_id: installment.debt_ack_contract_id,
      num_cycle: installment.num_cycle,
      amount: reAmt,
      remain_amount: reAmt,
      cycle_date: insCycleDate,
      type: lpiType,
      payment_status: constant.INSTALLMENT.PAYMENT_STATUS.ACTIVE,
      owner_id: payload.ownerId,
      is_testing: payload.isTesting,
      created_by: payload.createdBy,
      contract_number: insObjNow.contract_number,
      debt_ack_contract_number: insObjNow.debt_ack_contract_number,
      start_date: insStartDate,
      end_date: insEndDate,
      due_date: insDueDate,
      of_num_cycle: insObjNow.num_cycle,
      lpi_at_cycle: lpiAtCycle,
      closed: constant.INSTALLMENT.CLOSE.TRUE,
      origin_amt: irAmtExpire,
      ir_from_date: payload.irDate,
      ir_to_date: payload.irDate,
      ir_on_prin: installment.id,
      ir_num_cycle: installment.ir_num_cycle
    })

    if (objInsInsm) {
      await BillOnDueRepository.save({
        contract_number: objInsInsm.contract_number,
        debt_ack_contract_number: objInsInsm.debt_ack_contract_number,
        amount: objInsInsm.amount,
        remain_amount: objInsInsm.remain_amount,
        type: objInsInsm.type,
        num_cycle: objInsInsm.num_cycle,
        on_due_date: insEndDate,
        payment_status: constant.BILL_ON_DUE.PAYMENT_STATUS.ACTIVE,
        start_date: insStartDate,
        end_date: insEndDate,
        due_date: insDueDate,
        owner_id: payload.ownerId,
        created_by: payload.createdBy,
        installment_id: objInsInsm.id,
        payment_priority: objInsInsm.payment_priority
      })
    }
  }
  payloadUpdateDebtAck.lpiAmt += reAmt
  payloadUpdateDebtAck.toCollect += reAmt
}

module.exports = {
  calcuIrEveryDay,
  createProvision,
  createIcne,
  getSimulationLpi,
  adjustInterestRateBatchJob,
  doCalcuIrEveryDay,
  adjustInterestRateKunn,
  runIrToDate,
  deactiveLpi
}
