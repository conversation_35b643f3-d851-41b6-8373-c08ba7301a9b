const loanStatusHstRepo = require('../repositories/loan-status-hst-repo')
const constant = require('../utils/constant')
const actionAuditService = require('../other-services/action-audit-service')

async function insertNewAndUpdateOldRecord(debtAckContractNumber, newStatus, statusDate = new Date()) {
  try {
    if (newStatus != constant.LOAN_ACC_STATUS.SIG) {
      await loanStatusHstRepo.updateLoanStatusHstFlagActive(
        debtAckContractNumber,
        constant.FLAG_ACTIVE,
        constant.FLAG_NOT_ACTIVE
      )
    }
    await loanStatusHstRepo.insertLoanStatusHst({
      debtAckContractNumber,
      status: constant.LOAN_ACC_STATUS.codeToStatus[newStatus],
      statusDate
    })
    let actionAuditType, actionCodeType
    if (newStatus == constant.LOAN_ACC_STATUS.ACT) {
      actionAuditType = 'ACTIVATE'
      actionCodeType = 'ACTIVATED'
    } else if (newStatus == constant.LOAN_ACC_STATUS.TER) {
      actionAuditType = 'TERMINATION'
      actionCodeType = 'TERMINATED'
    }
    if (actionAuditType && actionCodeType) {
      actionAuditService.saveActionAudit(debtAckContractNumber, { actionAuditType, actionCodeType }, {})
    }

    return { code: 200, message: '[LMS-MCC] insertNewAndUpdateOldRecord' }
  } catch (err) {
    console.log(err)
    return { code: 500, message: err.message }
  }
}

module.exports = {
  insertNewAndUpdateOldRecord
}
