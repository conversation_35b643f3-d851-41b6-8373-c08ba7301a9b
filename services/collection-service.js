const loanAccountRepo = require('../repositories/loan-account-repo')
const moment = require('moment')
const CONSTANT = require('../utils/constant')
const loanAnnexRepo = require('../repositories/loan-annex-repo')
const irChargeRepo = require('../repositories/ir-charge-repo')
const contractRiskGroupRepo = require('../repositories/contract-risk-grp-repo')
const installmentRepo = require('../repositories/installment-repo')
const debtAckContractRepo = require('../repositories/debt-ack-contract-repo')
// const producerService = require('../services/producer')
const common = require('../utils/common')
const constant = require('../utils/constant')
const installmentService = require('../services/installment-service')
const { InsuranceRepository } = require('../repositories-v2')
const { mappingInsuranceCompany, isPrinEveryCycle } = require('../utils/helper')

async function getContractAppendix(payload) {
  try {
    if (!payload.contractNumber) return { statusCode: 400, code: 2, message: 'contractNumber is required' }
    const func = await Promise.all([
      loanAccountRepo.findLoanAccByDebtAckContractNumber(payload.contractNumber),
      loanAnnexRepo.findAnnexByDebtAck(global.poolRead, { debtAckContractNumber: payload.contractNumber }),
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, {
        debtAckContractNumber: payload.contractNumber,
        irType: CONSTANT.IR_CHARGE_TYPE.ON_DUE_PRIN
      }),
      installmentRepo.getTotalTenorPassed(payload.contractNumber),
      contractRiskGroupRepo.findContractRiskGrpByDebtAckContractNumber(payload.contractNumber),
      InsuranceRepository.findOne({ where: { debt_ack_contract_number: payload.contractNumber } })
    ])
    const accountObj = func[0]?.[0] || {}
    const loanAnnexObj = func[1].rows?.[0] || {}
    const irChargeObj = func[2].rows?.[0] || {}
    const installmentObj = func[3].rows?.[0] || {}
    const contractRiskGroupObj = func[4]?.[0] || {}
    const insuranceObj = func[5]

    if (!accountObj.debt_ack_contract_number) {
      return { statusCode: 200, code: 1, message: 'contractNumber not found' }
    }
    const releaseAmt = Number(accountObj.apr_limit_amt || 0) - Number(insuranceObj?.insur_amt || 0)

    const data = {
      customerId: accountObj.cust_id,
      contractNumber: accountObj.contract_number,
      startDate: accountObj.start_date
        ? moment(accountObj.start_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY)
        : undefined,
      activationDate: accountObj.active_date
        ? moment(accountObj.active_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY)
        : undefined,
      endDate: accountObj.end_date ? moment(accountObj.end_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY) : undefined,
      interestRate: Number(irChargeObj.ir_value || 0),
      contractStatus: accountObj.status == 1 && accountObj.payment_status != 0 ? 'ACTIVATED' : 'TERMINATED',
      totalOutstandingAmt: Number(accountObj.prin_amt || 0),
      approvalAmount: Number(accountObj.apr_limit_amt || 0),
      releaseAmount: releaseAmt,
      tenor: Number(accountObj.tenor),
      channel: accountObj.partner_code || '',
      startInstalDate: accountObj.active_date
        ? moment(accountObj.active_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY)
        : undefined,
      productCode: accountObj.product_code || '',
      paymentAccountNumber: '',
      insuranceCompany: mappingInsuranceCompany(insuranceObj),
      terminationDate: loanAnnexObj.termination_date
        ? moment(loanAnnexObj.termination_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY)
        : undefined,
      totalTenorPassed: Number(installmentObj.total || 0),
      isDebtStructure: 'Không',
      dpdRiskGroup: Number(contractRiskGroupObj.dpd_risk_grp || 0),
      userRiskGroup: Number(contractRiskGroupObj.user_risk_grp || 0),
      contractRiskGroup: Number(contractRiskGroupObj.cont_risk_grp || 0),
      individualRiskGroup: Number(contractRiskGroupObj.individual_risk_grp || 0),
      finalRiskGroup: Number(contractRiskGroupObj.final_risk_grp || 0)
    }

    return {
      statusCode: 200,
      code: 0,
      message: 'Success',
      data
    }
  } catch (error) {
    common.log('Error at getContractAppendix: ' + error.message, 'error')
    throw error
  }
}

async function getContractAppendixV2(payload) {
  try {
    if (!payload.contractNumber) return { statusCode: 400, code: 2, message: 'contractNumber is required' }
    const func = await Promise.all([
      loanAccountRepo.findLoanAccByDebtAckContractNumber(payload.contractNumber),
      loanAnnexRepo.findAnnexByDebtAck(global.poolRead, { debtAckContractNumber: payload.contractNumber }),
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, {
        debtAckContractNumber: payload.contractNumber,
        irType: CONSTANT.IR_CHARGE_TYPE.ON_DUE_PRIN
      }),
      installmentRepo.getTotalTenorPassed(payload.contractNumber),
      contractRiskGroupRepo.findContractRiskGrpByDebtAckContractNumber(payload.contractNumber),
      InsuranceRepository.findOne({ where: { debt_ack_contract_number: payload.contractNumber } })
    ])
    const accountObj = func[0]?.[0] || {}
    const loanAnnexObj = func[1].rows?.[0] || {}
    const irChargeObj = func[2].rows?.[0] || {}
    const installmentObj = func[3].rows?.[0] || {}
    const contractRiskGroupObj = func[4]?.[0] || {}
    const insuranceObj = func[5]

    if (!accountObj.debt_ack_contract_number) {
      return { statusCode: 200, code: 1, message: 'contractNumber not found' }
    }
    const releaseAmt = Number(accountObj.apr_limit_amt || 0) - Number(insuranceObj?.insur_amt || 0)

    const data = {
      customerId: accountObj.cust_id,
      contractNumber: accountObj.contract_number,
      startDate: accountObj.start_date
        ? moment(accountObj.start_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY)
        : undefined,
      activationDate: accountObj.active_date
        ? moment(accountObj.active_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY)
        : undefined,
      endDate: accountObj.end_date ? moment(accountObj.end_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY) : undefined,
      interestRate: Math.round(irChargeObj.ir_value * 10000 || 0) / 100,
      contractStatus: accountObj.status == 1 && accountObj.payment_status != 0 ? 'ACTIVATED' : 'TERMINATED',
      totalOutstandingAmt: Number(accountObj.prin_amt || 0),
      approvalAmount: Number(accountObj.apr_limit_amt || 0),
      releaseAmount: releaseAmt,
      tenor: Number(accountObj.tenor),
      channel: accountObj.partner_code || '',
      startInstalDate: accountObj.active_date
        ? moment(accountObj.active_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY)
        : undefined,
      productCode: accountObj.product_code || '',
      paymentAccountNumber: '',
      insuranceCompany: mappingInsuranceCompany(insuranceObj),
      terminationDate: loanAnnexObj.termination_date
        ? moment(loanAnnexObj.termination_date).format(CONSTANT.DATE_FORMAT.DDMMYYYY)
        : undefined,
      totalTenorPassed: Number(installmentObj.total || 0),
      isDebtStructure: 'Không',
      dpdRiskGroup: Number(contractRiskGroupObj.dpd_risk_grp || 0),
      userRiskGroup: Number(contractRiskGroupObj.user_risk_grp || 0),
      contractRiskGroup: Number(contractRiskGroupObj.cont_risk_grp || 0),
      individualRiskGroup: Number(contractRiskGroupObj.individual_risk_grp || 0),
      finalRiskGroup: Number(contractRiskGroupObj.final_risk_grp || 0)
    }

    return {
      statusCode: 200,
      code: 0,
      message: 'Success',
      data
    }
  } catch (error) {
    common.log('Error at getContractAppendixV2: ' + error.message, 'error')
    throw error
  }
}

const recallMcDisbursement = async (debtAckContractNumber) => {
  try {
    const loanAccs = (
      await debtAckContractRepo.findByContractNumberAndStatusMQueue(global.poolRead, { debtAckContractNumber })
    ).rows
    if (loanAccs.length == 0) {
      return {
        statusCode: 200,
        code: 0,
        message: 'KUNN does not exist'
      }
    }

    const loanAccountObj = loanAccs[0]
    const plCallPartner = {
      contractNumber: loanAccountObj.contract_number,
      debtAckContractNumber: loanAccountObj.debt_ack_contract_number,
      status: constant.DISBURSEMENT.TRANS_STATUS.ACTIVATED
    }
    const results = await Promise.all([
      installmentRepo.getInsmInfo(global.poolRead, plCallPartner),
      irChargeRepo.findByDebtAckContractNumberAndProductCode(global.poolRead, {
        debtAckContractNumber: plCallPartner.debtAckContractNumber,
        irType: 1
      }),
      installmentRepo.findByDebtAckContractNumberForResendQueue(global.poolRead, plCallPartner, false),
      debtAckContractRepo.findByContractNumberAndStatusMQueue(global.poolRead, plCallPartner),
      installmentRepo.findByDebtAckContractNumberMQueue(global.poolRead, plCallPartner, true)
    ])
    const rsData = results[0]
    const rsIrCharge = results[1]
    const installmantData = results[2].rows[0]
    const handleData = installmentService.processListInstallment(
      rsData,
      rsIrCharge.rows?.[0]?.ir_value,
      isPrinEveryCycle(loanAccountObj)
    )
    const dataReturn = []
    for (const element of handleData) {
      const obj = {
        instal_id: element.instalId,
        amort_id: loanAccountObj.loan_id,
        loan_id: loanAccountObj.loan_id,
        debtAckContractNumber: installmantData.debt_ack_contract_number,
        is_invoiced: element.invoiced,
        instal_num: element.numCycle,
        start_instal_date: element.startDate,
        end_instal_date: element.endDate,
        outs_prin_amt_instal: element.remainPrinAmount,
        prin_amt_instal: element.capitalRefunded,
        int_amt_instal: element.remainIrAmount,
        fee_amt_instal: element.remainFee,
        min_pay_amt: null,
        flag_active: '1',
        due_id_prin: null,
        due_id_int: null,
        created_date: installmantData.created_date,
        updated_date: installmantData.updated_date,
        created_user: 'system',
        owner_id: 1,
        is_delete: null
      }
      dataReturn.push(obj)
    }

    return {
      statusCode: 200,
      code: 0,
      message: 'Success'
    }
  } catch (error) {
    console.log('send mq activeDebtAckContract error:', error?.message)
    throw error
  }
}

module.exports = {
  getContractAppendix,
  recallMcDisbursement,
  getContractAppendixV2
}
