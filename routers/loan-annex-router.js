const express = require('express')
const router = express.Router()
const loanAnnexService = require('../services/loan-annex-service')
const cronJobService = require('../services/cronjob-service')
const aaaService = require('./../utils/aaa')
const billService = require('../services/bill-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

router.post('/v1/loan-annex/create', aaaService.authenInternal, function (req, res) {
  loanAnnexService.createLoanAnnexMc(req, res)
})
router.post('/v1/loan-annex/cancel', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CANCEL_ANNEX
    const payload = req.body
    const response = await loanAnnexService.cancelAnnex(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /loan-annex/cancel: ', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/v1/loan-annex/cancel-draft', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CANCEL_ANNEX_DRAFT
    const payload = req.body
    const response = await loanAnnexService.cancelAnnexDraft(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /loan-annex/cancel: ', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.get('/v1/loan-annex/simulation', function (req, res) {
  loanAnnexService.simulaLoanAnnexMc(req, res)
})
router.get('/v1/loan-annex/detail', function (req, res) {
  loanAnnexService.detailLoanAnnexMc(req, res)
})
router.get('/v1/loan-annex/simulation-v2', async function (req, res) {
  const payload = req.query
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.SIMULATION_ANNEX
    const result = await loanAnnexService.simulationLoanAnnexV2(payload)
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})
router.post('/v1/loan-annex/create-v2', aaaService.authenInternal, async function (req, res) {
  const payload = req.body
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CREATE_ANNEX
    console.log('payload', payload)
    const result = await loanAnnexService.simulationLoanAnnexV2({ ...payload, isCreated: true })
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    console.log(err)
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.post('/v1/loan-annex/create-full-v2', aaaService.authenInternal, async function (req, res) {
  const payload = req.body
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CREATE_ANNEX
    console.log('payload', payload)
    const result = await loanAnnexService.simulationLoanAnnexFull({ ...payload, isCreated: true })
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    console.log(err)
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/loan-annex/annex-history', async function (req, res) {
  try {
    const payload = req.query
    const result = await loanAnnexService.getAnnexHistory(payload.debtAckContractNumber)
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (e) {
    console.error('Error at /loan-annex/annex-history', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.get('/v1/loan-annex/new-annex-filter', async function (req, res) {
  try {
    const payload = req.query
    const result = await loanAnnexService.getNewAnnexFilter(payload.debtAckContractNumber, payload.date)
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (e) {
    console.error('Error at /loan-annex/new-annex-filter', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/v1/loan-annex/cancel/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CANCEL_ANNEX_JOB
    const response = await cronJobService.cancelAnnexBatchJob(req.query.date)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /loan-annex/cancel/batch-job', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

router.get('/v1/termination/annex', async function (req, res) {
  loanAnnexService.terminationAnnex(req, res)
})

router.get('/v1/loan-annex/get-prepayment-fee', function (req, res) {
  loanAnnexService.getPrepaymentFee(req, res)
})

router.post('/v1/loan-annex/payment', async function (req, res) {
  const body = req.body
  try {
    const result = await billService.billPayment(body)
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.post('/v1/loan-annex/confirm-annex', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.CONFIRM_ANNEX_PENDING
    const payload = req.body
    const response = await loanAnnexService.confirmAnnexPending(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /loan-annex/active-pending', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

router.post('/v1/loan-annex/active-pending', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ACTIVE_ANNEX_PENDING
    const payload = req.body
    const response = await loanAnnexService.activePendingAnnex(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /loan-annex/active-pending', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

router.post('/v1/loan-annex/active-pending/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.ACTIVE_ANNEX_PENDING_JOB
    const body = req.body
    const response = await loanAnnexService.activePendingAnnexBatchJob(body.scanDate)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /loan-annex/active-pending/batch-job', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

router.post('/v1/loan-annex/noti-annex-pending/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.NOTIFY_ANNEX_PENDING_JOB
    const response = await loanAnnexService.nofityInitAnnexBatchJob()
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /loan-annex/noti-annex-pending/batch-job', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
module.exports = router
