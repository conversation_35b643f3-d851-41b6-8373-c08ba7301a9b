const express = require('express')
const router = express.Router()
const loanAccountService = require('../services/loan-account-service')
const common = require('../utils/common')
const collectionService = require('../services/collection-service')

router.get('/collection/total-list', function (req, res) {
  loanAccountService.getCollectionTotalList(req, res)
})
router.get('/collection/contract-appendix', async (req, res) => {
  try {
    const payload = req.query
    common.log('req query contract-appendix: ' + JSON.stringify(payload))
    const response = await collectionService.getContractAppendix(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    common.log('Error at /contract-appendix : ' + e.message, 'error')
    console.log(e)
    res.status(500).json({ code: -1, message: 'System error' })
  }
})

router.get('/collection/contract-appendix-v2', async (req, res) => {
  try {
    const payload = req.query
    common.log('req query contract-appendix-v2: ' + JSON.stringify(payload))
    const response = await collectionService.getContractAppendixV2(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    common.log('Error at /contract-appendix-v2 : ' + e.message, 'error')
    console.log(e)
    res.status(500).json({ code: -1, message: 'System error' })
  }
})

router.get('/collection/recall-mc-disbursement', async (req, res) => {
  try {
    const debtAckContractNumber = req.query.contractNumber
    const response = await collectionService.recallMcDisbursement(debtAckContractNumber)
    res.status(response.statusCode).json({
      code: response.code,
      message: response.message
    })
  } catch (e) {
    common.log('Error at /recall-mc-disbursement: ' + e.message, 'error')
    res.status(500).json({ code: -1, message: 'System error' })
  }
})

module.exports = router
