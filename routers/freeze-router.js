const express = require('express')
const router = express.Router()
const freezeService = require('../services/freeze-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

router.post('/freeze', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_FREEZE
    const payload = req.body
    const response = await freezeService.updateFreeze(payload)
    res.status(response.statusCode).json((res.body = response))
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

router.get('/freeze-hst', async function (req, res) {
  try {
    const payload = req.query
    const response = await freezeService.getFreezeHst(payload.debtAckContractNumber)
    res.status(response.statusCode).json((res.body = response))
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

module.exports = router
