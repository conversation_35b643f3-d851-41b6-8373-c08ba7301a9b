const express = require('express')
const router = express.Router()
const micService = require('../other-services/mic-service')
const irService = require('../services/ir-service')
router.get('/healthcheck', function (request, response) {
  // console.log('request.createdBy: ',request.createdBy)
  console.log('Check server info')
  response.json((response.body = { message: 'LMS-MC service is alive' }))
})

router.get('/check-connect-mic', async function (request, response) {
  try {
    await micService.callGetToken()
    response.json((response.body = { code: 0, message: 'Connect ok' }))
  } catch (e) {
    response.status(500).json({ code: -1, message: e.message })
  }
})

module.exports = router
