const express = require('express')
const router = express.Router()
const installmentService = require('../services/installment-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

router.get('/v1/installment/get', async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getInsmInfo(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})
router.get('/v1/installment/get-by-contract-limit', async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getInstallmentByContractLimit(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/installment/prin-amount', async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getPrinAmountByCustId(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})


router.get('/v1/installment/upcoming', async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getNextInsmInfoByContractNumber(payload.contractNumber, payload.infoMonth)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/installment/misa', async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getInstallmentInfoMisa(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.post('/v1/installment/export-data', async function (request, response) {
  const payload = request.query
  console.log(payload)
  try {
    request.isLog = true
    response.eventType = REQUEST_EVENT_NAME.EXPORT_INSTALLMENT
    const result = await installmentService.exportDataInstallment(payload.debtAckContractNumber)
    response.status(result.statusCode).json((response.body = result))
  } catch (err) {
    response.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/installment/paid-history', async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getHistoryPaidSme(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.post('/v1/installment/simulation', async function (req, res) {
  const payload = req.body
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.SIMULATION_INSTALLMENT
    const result = await installmentService.simulationVoucherInstallment(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})
router.get('/v1/mobile-sma/installment', async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getInstallmentSMA(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.post('/v1/installment/merchant/simulation', async function (req, res) {
  try {
    const result = installmentService.getInstallmentSimulationMerchant(req.body)
    res.status(result.statusCode || 200).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

module.exports = router
