const express = require('express')
const router = express.Router()
const merchantLimitService = require('../services/merchant-limit-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

router.post('/merchant-limit/create', function (req, res) {
  req.isLog = true
  req.eventType = REQUEST_EVENT_NAME.CREATE_LOAN_CONTRACT_LIMIT
  merchantLimitService.createMcCreditLimit(req, res)
})
router.post('/merchant-limit/unlock', function (req, res) {
  merchantLimitService.unlockMcLimitV2(req, res)
})
router.post('/merchant-limit/lock', function (req, res) {
  merchantLimitService.lockMcLimitV2(req, res)
})
router.post('/merchant-limit/available', function (req, res) {
  merchantLimitService.checkAvalibleLimit(req, res)
})

module.exports = router
