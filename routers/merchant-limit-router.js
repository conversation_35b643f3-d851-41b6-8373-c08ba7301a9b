const express = require('express')
const router = express.Router()
const merchantLimitService = require('../services/merchant-limit-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

router.post('/merchant-limit/create', function (req, res) {
  req.isLog = true
  req.eventType = REQUEST_EVENT_NAME.CREATE_LOAN_CONTRACT_LIMIT
  merchantLimitService.createMcCreditLimit(req, res)
})

router.post('/merchant-limit/available', function (req, res) {
  merchantLimitService.checkAvalibleAmount(req, res)
})
router.post('/merchant-limit/remain', function (req, res) {
  merchantLimitService.checkRemainLimit(req, res)
})
router.put('/merchant-limit/:mcLimitId/unlock', function (req, res) {
  merchantLimitService.unlockMcLimit(req, res)
})
router.put('/merchant-limit/:mcLimitId/lock', function (req, res) {
  merchantLimitService.lockMcLimit(req, res)
})
router.delete('/merchant-limit/:mcLimitId/remove', function (req, res) {
  merchantLimitService.deleteMcLimit(req, res)
})

router.put('/merchant-limit/update', async function (req, res) {
  const payload = req.body
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_REVIEW_LIMIT
    const result = await merchantLimitService.updateReviewMcLimit(payload)
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

module.exports = router
