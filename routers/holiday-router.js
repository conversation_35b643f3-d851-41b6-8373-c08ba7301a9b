const express = require('express')
const router = express.Router()
const holidayService = require('../services/holiday-service')
const { REQUEST_EVENT_NAME, CALCUCFG } = require('../utils/constant')

router.post('/holiday/import', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.IMPORT_HOLIDAY
    const payload = req.body
    const response = await holidayService.importHoliday(payload)
    res.status(response.statusCode).json((res.body = response))
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

router.get('/holiday/config', async function (req, res) {
  try {
    const response = {
      listHolidayConfig: global.listHoliday,
      tenorConfig: global.tenorConfig,
      CALCUCFG
    }
    res.status(200).json((res.body = response))
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

module.exports = router
