const express = require('express')
const router = express.Router()
const welcomePackageService = require('../services/welcome-package-service')

router.get('/v1/welcome-package/load', function (req, res) {
  welcomePackageService.getWelcomePackage(req, res)
})
router.get('/v1/mobile-sma/welcome-package', function (req, res) {
  welcomePackageService.getWelcomePackageSMA(req, res)
})
router.get('/v2/welcome-package/load', function (req, res) {
  welcomePackageService.getWelcomePackageV2(req, res)
})
module.exports = router
