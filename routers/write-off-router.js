const express = require('express')
const router = express.Router()
const writeOffService = require('./../services/write-off-service')

router.post('/update-writing-off', async function (request, response) {
  const payload = request.body
  console.log(payload)
  try {
    response.json(await writeOffService.updateWriteOff(payload))
  } catch (err) {
    response.status(err.status || 500).json({ message: err.message })
  }
})

module.exports = router
