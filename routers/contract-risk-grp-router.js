const express = require('express')
const router = express.Router()
const contractRiskGrpService = require('../services/contract-risk-grp-service')
const cronJobService = require('../services/cronjob-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

router.post('/dpd-risk-grp/usage', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_DPD
    const payload = req.body
    const response = await contractRiskGrpService.calDpbRiskGrp(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /dpd-risk-grp/usage: ', e.message)
    console.log(e)
    res.status(500).json({ code: -1, message: e.message })
  }
})
router.get('/dpd-risk-grp/info', function (req, res) {
  contractRiskGrpService.getDpdInfo(req, res)
})
router.get('/user-risk-grp/get', function (req, res) {
  contractRiskGrpService.getUserRiskList(req, res)
})
router.post('/user-risk-grp/save', function (req, res) {
  req.isLog = true
  req.eventType = REQUEST_EVENT_NAME.SAVE_USER_RISKGROUP
  contractRiskGrpService.saveUserRiskGrp(req, res)
})
router.post('/cic-risk-grp/import-file', function (req, res) {
  req.isLog = true
  req.eventType = REQUEST_EVENT_NAME.IMPORT_CIC_FILE
  contractRiskGrpService.importCicRiskGrp(req, res)
})
router.get('/cic-risk-grp/get-history', function (req, res) {
  contractRiskGrpService.getHistoryImport(req, res)
})
router.post('/dpd-risk-grp/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_DPD_JOB
    cronJobService.calDpbRiskGrpBatchJob(req.query.date)
    res.status(200).json((res.body = { statusCode: 200, code: 0, message: 'Success calDpbRiskGrpBatchJob' }))
  } catch (e) {
    console.error('Error at /dpd-risk-grp/batch-job: ', e.message)
    console.log(e)
    res.status(500).json({ code: -1, message: e.message })
  }
})

router.get('/dpd-info/history', async function (req, res) {
  const payload = req.query
  try {
    const response = await contractRiskGrpService.getHistoryDpdInfo(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /dpd/history: ', e.message)
    console.log(e)
    res.status(500).json({ code: -1, message: e.message })
  }
})

module.exports = router
