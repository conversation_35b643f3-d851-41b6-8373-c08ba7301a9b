const express = require('express')
const router = express.Router()
const billService = require('../services/bill-service')
const cronJobService = require('../services/cronjob-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

router.post('/bill-on-due/create', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.LOAD_BILL
    const payload = req.body

    const response = await billService.doCreateBillOnDue(payload)
    
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /bill-on-due/batch-job: ', e.message)
    console.log(e)
    res.status(500).json({ code: -1, message: e.message })
  }
})

router.post('/bill-on-due/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.LOAD_BILL_JOB
    cronJobService.createBillOnDueBatchJob(req.query.date)
    res.status(200).json((res.body = { statusCode: 200, code: 0, message: 'Success createBillOnDueBatchJob' }))
  } catch (e) {
    console.error('Error at /bill-on-due/batch-job: ', e.message)
    console.log(e)
    res.status(500).json({ code: -1, message: e.message })
  }
})

router.post('/bill-on-due-factoring/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.LOAD_BILL_JOB
    cronJobService.createBillOnDueFactoringBatchJob(req.query.date)
    res.status(200).json((res.body = { statusCode: 200, code: 0, message: 'Success createBillOnDueBatchJob' }))
  } catch (e) {
    console.error('Error at /bill-on-due-factoring/batch-job: ', e.message)
    console.log(e)
    res.status(500).json({ code: -1, message: e.message })
  }
})

module.exports = router
