const express = require('express')
const router = express.Router()
const irService = require('../services/ir-service')
const cronJobService = require('../services/cronjob-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')
const irFactoringService = require('../services/ir-factoring-service')
const repaymentService = require('../services/repayment-service')
const common = require("../utils/common");
const {doCreateBillOnDue} = require("../services/bill-service");

router.post('/interest/every-day', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_IR
    const response = await irService.calcuIrEveryDay(req.body)
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/interest-factoring/every-day', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_IR

    const {debtAckContractNumber, irDate } = req.body
    let checkTerminateFactoring = await repaymentService.checkTerminateFactoring({
      debtAckContractNumber,
      valueDate: irDate
    })

    if (checkTerminateFactoring) {
      const dateRunJob = common.formatDate({ date: irDate })
      let result = await doCreateBillOnDue({ debtAckContractNumber, onDueDate: dateRunJob })
      if (result.code == 0) {
        await repaymentService.doRepayment({
          debtAckContractNumber,
          paymentDate: dateRunJob,
          isTerminateFactoring: true,
        })
      }
    }

    const response = await irFactoringService.calcuIrEveryDayRefactoring(req.body)
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/interest/test', async function (req, res) {
  try {
    const response = await irService.runIrToDate(req.body)
    res.status(response.statusCode).json(response)
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

router.post('/interest/create-provision', function (req, res) {
  irService.createProvision(req, res)
})
router.post('/interest/create-icne', function (req, res) {
  irService.createIcne(req, res)
})
router.post('/interest/every-day/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_IR_JOB
    cronJobService.calcuIrEveryDayBatchJob(req.query.date)
    res.status(200).json((res.body = { code: 0, message: 'Success calcuIrEveryDayBatchJob' }))
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

router.get('/interest/simulation-lpi', async function (req, res) {
  try {
    const payload = req.query
    const response = await irService.getSimulationLpi(payload)
    res.status(response.statusCode).json((res.body = response))
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/interest-factoring/every-day/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.UPDATE_IR_JOB_FACTORING
    cronJobService.calcuIrFactoringEveryDayBatchJob(req.query.date)
    res.status(200).json((res.body = { code: 0, message: 'Success calcuIrFactoringEveryDayBatchJob' }))
  } catch (e) {
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})

module.exports = router
