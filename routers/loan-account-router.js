const express = require('express')
const router = express.Router()
const loanAccountService = require('../services/loan-account-service')
const debtAckContractService = require('../services/debt-ack-contract-service')
const auth = require('../utils/aaa')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

router.get('/v1/collection/info', function (req, res) {
  loanAccountService.getCollectionInfo(req, res)
})
router.get('/v1/loan-account/get', function (req, res) {
  debtAckContractService.getListLoanActive(req, res)
})
router.get('/v1/welcome-package', auth.authenticate, function (req, res) {
  loanAccountService.getWelcomePackage(req, res)
})
router.get('/v1/loan-contract', function (req, res) {
  loanAccountService.getLoanContract(req, res)
})
router.get('/v1/loan-account/case-summary', async function (req, res) {
  const payload = req.query
  try {
    const result = await loanAccountService.getLoanInfomationCaseSummary(payload.debtAckContractNumber)
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/loan-account/statement', async function (req, res) {
  const payload = req.query
  try {
    const result = await loanAccountService.getAccountStatementInfo(
      payload.contractNumber,
      payload.fromDate,
      payload.toDate,
      payload.page,
      payload.limit
    )
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/loan-account/statement/kunn', async function (req, res) {
  const payload = req.query
  try {
    const result = await loanAccountService.getStatementInfoByKunnNumber(payload.debtAckContractNumber)
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.post('/v1/loan-account/statement/export', async function (req, res) {
  const payload = req.body
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.EXPORT_STATEMENT
    const result = await loanAccountService.exportAccountStamentInfo(
      payload.contractNumber,
      payload.fromDate,
      payload.toDate,
      payload.isBlockLimit
    )
    res.body = result
    res.status(result.statusCode).json(result)
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/loan-contract-ezbiz', function (req, res) {
  loanAccountService.getLoanContractDetailByContractNumber(req, res)
})

router.post('/v1/loan-account/refund-money', async function (request, response) {
  try {
    request.isLog = true
    request.eventType = REQUEST_EVENT_NAME.REFUND_MONEY
    const body = request.body
    const refundMoneyResponse = await loanAccountService.refundMoney(body)
    response.status(refundMoneyResponse.statusCode).json((response.body = refundMoneyResponse))
  } catch (err) {
    response.status(err.status || 500).json((response.body = { message: err.message, command: err }))
  }
})

router.get('/v1/loan-account/list-contract', async function (request, response) {
  try {
    const query = request.query
    const responseLoanContract = await loanAccountService.getLoanContractDataByContractNumber(query.contractNumber)
    response.status(responseLoanContract.statusCode).json((response.body = responseLoanContract))
  } catch (err) {
    response.status(err.status || 500).json((response.body = { message: err.message, command: err }))
  }
})

router.get('/v1/loan-account/get/by-kunn', async function (req, res) {
  try {
    const rs = await loanAccountService.getLoanAccountByKunnNumber(req.query?.kunnNumber)
    return res.status(rs.statusCode).json((res.body = rs))
  } catch (err) {
    return res.status(err.status || 500).json((res.body = { message: err?.message, command: err }))
  }
})

router.get('/v1/loan-account/list-kunn', async function (request, response) {
  try {
    const query = request.query
    const res = await loanAccountService.getListKunnByCustId(query)
    response.status(res.statusCode).json((response.body = res))
  } catch (err) {
    response.status(err.status || 500).json((response.body = { message: err.message, command: err }))
  }
})

router.get('/v1/loan-account/detail-kunn', async function (request, response) {
  try {
    const query = request.query
    const res = await loanAccountService.getDetailKunnInfo(query.debtAckContractNumber)
    response.status(res.statusCode).json((response.body = res))
  } catch (err) {
    response.status(err.status || 500).json((response.body = { message: err.message, command: err }))
  }
})
router.get('/v1/mobile-sma/loan-account', async function (request, response) {
  try {
    const input = request.query
    const res = await loanAccountService.getLoanContractSMA(input)
    response.status(res.statusCode).json((response.body = res))
  } catch (err) {
    response.status(err.status || 500).json((response.body = { message: err.message, command: err }))
  }
})

router.get('/v1/loan-account/notify', async function (request, response) {
  try {
    request.isLog = true
    request.eventType = REQUEST_EVENT_NAME.GET_LOAN_NOTIFY
    const query = request.query
    const res = await loanAccountService.getLoanInfoToNotify(query.debtAckContractNumber)
    response.status(res.statusCode).json((response.body = res))
  } catch (err) {
    response.status(err.status || 500).json((response.body = { message: err.message, command: err }))
  }
})
router.get('/v1/loan-account/amortization', async function (request, response) {
  try {
    const res = await loanAccountService.getAmortization(request.query)
    response.status(res.statusCode).json((response.body = res))
  } catch (err) {
    console.log(err)
    response.status(err.status || 500).json((response.body = { message: err.message, command: err }))
  }
})
module.exports = router
