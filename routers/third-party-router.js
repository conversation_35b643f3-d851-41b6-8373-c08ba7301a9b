const express = require('express')
const router = express.Router()
const installmentService = require('../services/installment-service')
const aaaService = require('../utils/aaa')

router.get('/3rd/installment', aaaService.authenticateOauth2, async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getInsmInfo(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/3rd/installment/misa', aaaService.authenticateOauth2, async function (req, res) {
  const payload = req.query
  try {
    const result = await installmentService.getInstallmentInfoMisa(payload)
    res.status(result.statusCode).json((res.body = result))
  } catch (err) {
    res.status(err.status || 500).json({ message: err.message })
  }
})

module.exports = router
