const express = require('express')
const router = express.Router()
const paymentService = require('../services/payment-service')
const reconciliService = require('../services/reconciliation-service')
const { REQUEST_EVENT_NAME } = require('../utils/constant')
const aaaService = require('./../utils/aaa')

router.post('/v1/payment/usage', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.PAYMENT
    const payload = req.body
    const response = await paymentService.usagePayment(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /payment/usage', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/v1/payment-suspend/usage', aaaService.authenInternal, async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.PAYMENT_SUSPEND
    const payload = req.body
    const response = await paymentService.usageSuspendPayment(payload)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /payment-suspend/usage', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.get('/v1/repayment', function (req, res) {
  paymentService.getRepaymentHistory(req, res)
})

router.get('/v1/repayment/detail', function (req, res) {
  paymentService.getRepaymentHistoryDetail(req, res)
})

router.post('/v1/payment/info', function (req, res) {
  paymentService.checkRepayment(req, res)
})

router.get('/v1/payment/payment-history', function (req, res) {
  paymentService.getPaymentHistory(req, res)
})

router.get('/v2/payment/payment-history', function (req, res) {
  paymentService.getPaymentHistoryV2(req, res)
})

router.post('/v1/payment/info-v2', function (req, res) {
  paymentService.checkRepaymentV2(req, res)
})

router.post('/v1/payment/info-v3', function (req, res) {
  paymentService.checkRepaymentV3(req, res)
})
router.get('/v1/repayment/check', function (req, res) {
  paymentService.checkRepaymentByCustId(req, res)
})
router.get('/v1/reconciliation/list-transaction', function (req, res) {
  reconciliService.getListTransaction(req, res)
})

router.put('/v1/reconciliation/reconciled-result', function (req, res) {
  reconciliService.reconciledResult(req, res)
})
router.post('/v1/payment/usage/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.REPAYMENT_JOB
    const response = await paymentService.usagePaymentBatchJob(req.query.date)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /payment/usage/batch-job', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/v1/suspend-payment/usage/batch-job', async function (req, res) {
  try {
    req.isLog = true
    req.eventType = REQUEST_EVENT_NAME.REPAYMENT_JOB
    const response = await paymentService.usageSuspendPaymentBatchJob(req.query.date)
    res.body = response
    res.status(response.statusCode).json(response)
  } catch (e) {
    console.error('Error at /suspend-payment/usage/batch-job', e.message)
    console.log(e)
    res.status(500).json((res.body = { code: -1, message: e.message }))
  }
})
router.post('/v1/payment/case-to-case-transfer', aaaService.authenInternal, async function (request, response) {
  const payload = request.body
  try {
    request.isLog = true
    request.eventType = REQUEST_EVENT_NAME.TRANSFER_CASE
    const result = await paymentService.transferCaseToCase(payload)
    response.status(result.statusCode).json(result)
  } catch (err) {
    response.status(err.status || 500).json({ message: err.message })
  }
})

router.post('/v1/payment/cancel-payment', aaaService.authenInternal, async function (request, response) {
  const payload = request.body
  try {
    request.isLog = true
    request.eventType = REQUEST_EVENT_NAME.CANCEL_PAYMENT
    const result = await paymentService.cancelPayment(payload)
    response.body = result
    response.status(result.statusCode).json(result)
  } catch (err) {
    response.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/repayment-v2', async function (request, response) {
  const payload = request.query
  try {
    const result = await paymentService.getRepaymentHistoryV2(payload)
    response.body = result
    response.status(result.statusCode).json(result)
  } catch (err) {
    response.status(err.status || 500).json({ message: err.message })
  }
})

router.get('/v1/cc-tool/check-s3', async function (request, response) {
  const { kunnId } = request.query
  try {
    const responseData = {
      isSituation3: false,
      debtAckContractNumber: kunnId,
      data: []
    }
    if (!kunnId) {
      return response.status(400).json({ code: 1, message: 'Input Invalid', data: responseData })
    }
    const sqlPayment = 'select * from payment pay where non_allocated_amt > 0 and debt_ack_contract_number = $1'
    const rsPayment = await request.poolRead.query(sqlPayment, [kunnId])
    if (rsPayment.rowCount > 0) {
      ;(rsPayment.rows ?? []).forEach((x) => {
        responseData.data.push({
          amount: x.installment_amort,
          paymentDate: x.payment_date
        })
      })
      const sqlBillOnDue =
        'select * from bill_on_due bod where remain_amount > 0 and status = 1 and debt_ack_contract_number = $1'
      const rsBillOnDue = await request.poolRead.query(sqlBillOnDue, [kunnId])
      if (rsBillOnDue.rowCount > 0) {
        responseData.isSituation3 = true
      }
    }
    return response.status(200).json({ code: 0, message: 'check situation 3 success', data: responseData })
  } catch (err) {
    response.status(err.status || 500).json({ code: -1, message: err.message })
  }
})
router.get('/v1/mobile-sma/repayment', async function (request, response) {
  const payload = request.query
  try {
    const result = await paymentService.getRepaymentHistoryV2(payload)
    response.body = result
    response.status(result.statusCode).json(result)
  } catch (err) {
    response.status(err.status || 500).json({ message: err.message })
  }
})
router.get('/v1/payment/list-debt-number', function (req, res) {
  paymentService.getDataDebtReminderCollection(req, res)
})
router.post('/v1/payment/info-debt-sms', function (req, res) {
  paymentService.getPaymentInfoDebtSms(req, res)
})
router.post('/v1/payment/info-debt-email', function (req, res) {
  paymentService.getPaymentInfoDebtEmail(req, res)
})
module.exports = router
