// const stompit = require('stompit')
// const common = require('../utils/common')
// const { ACTIVE_MQ_WARNING } = require('../utils/constant')

// const MAX_RECONNECT_NUMBER = 10
// let reconnectCounter = 0

// const createConnection = function () {
//   try {
//     const connectOptions = {
//       host: global.config.data?.queue?.host,
//       port: global.config.data?.queue?.port,
//       resetDisconnect: true,
//       connectHeaders: {
//         host: '/',
//         login: global.config.data?.queue?.login,
//         passcode: global.config.data?.queue?.passcode,
//         'heart-beat': global.config.data?.queue?.heart_beat
//       },
//       ssl: true
//     }

//     const connectOptions2 = {
//       host: global.config.data?.queue?.host2,
//       port: global.config.data?.queue?.port,
//       resetDisconnect: true,
//       connectHeaders: {
//         host: '/',
//         login: global.config.data?.queue?.login,
//         passcode: global.config.data?.queue?.passcode,
//         'heart-beat': global.config.data?.queue?.heart_beat
//       },
//       ssl: true
//     }

//     const connectOptionsArr = [connectOptions, connectOptions2]
//     const reconnectOptions = { maxReconnects: MAX_RECONNECT_NUMBER }

//     const manager = new stompit.ConnectFailover(connectOptionsArr, reconnectOptions)
//     manager.connect((error, client, reconnect) => {
//       if (error) {
//         console.log(`[MC-ACTIVEMQ] An error has occurred while connecting to ActiveMQ: ${error?.message}`)
//         reconnectCounter++
//         handleReconnect(createConnection, false, reconnectCounter, error)
//         return
//       }

//       client.on('error', (error) => {
//         console.log(`[MC-ACTIVEMQ] Service is disconnected to ActiveMQ: ${error?.message}`)
//         reconnectCounter++
//         handleReconnect(reconnect, true, reconnectCounter, error)
//       })

//       reconnectCounter = 0
//       global.activeMqClient = client
//       console.log('[MC-ACTIVEMQ] Service is connected to ActiveMQ successfully')
//     })
//   } catch (error) {
//     console.log(`[MC-ACTIVEMQ] Exception while connecting to ActiveMQ: ${error?.message}`)
//   }
// }

// const handleReconnect = function (callback, useReconnectCallback, reconnectCounter, error) {
//   if (reconnectCounter > MAX_RECONNECT_NUMBER) {
//     console.log(`[MC-ACTIVEMQ] Service is reconnected more than ${MAX_RECONNECT_NUMBER} times`)
//     const htmlContent =
//       `<p>An error has occurred while connecting to ActiveMQ:</p>
//       <ul>
//         <li>Service: lms-mc-service</li>
//         <li>Error message: ${error?.message}</li>
//         <li>Occurrence time: ${common.convertDatetoString(new Date(), 'yyyy-mm-dd HH:MM:ss.l')}</li>
//         <li>Enviroment: ${process.env.NODE_ENV}</li>
//       </ul>
//       <p>Please check and try again!</p>`

//     const bssEmailUrl = global.config.basic.bssEmailService[global.env] + global.config.basic.bssEmailService.service
//     const reqBodyEmail = {
//       receiver: ACTIVE_MQ_WARNING.LIST_EMAIL,
//       subject: ACTIVE_MQ_WARNING.SUBJECT,
//       html: htmlContent
//     }

//     common.postAPI(bssEmailUrl, reqBodyEmail, undefined, { 'Content-Type': 'application/json;charset=UTF-8' }).catch(err => {
//       console.log(`[MC-ACTIVEMQ] Error when send email warning about ActiveMQ: ${err?.message}`)
//     })
//   } else {
//     console.log(`[MC-ACTIVEMQ] Service is reconnecting to ActiveMQ time ${reconnectCounter}`)
//     if (useReconnectCallback) {
//       setTimeout(callback, 1000)
//     } else {
//       setTimeout(callback, reconnectCounter * 30000)
//     }
//   }
// }

// module.exports = {
//   createConnection
// }
