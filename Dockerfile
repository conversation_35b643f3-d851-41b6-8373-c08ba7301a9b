# FROM els-registry.evnfc.vn/common/docker-image-common-alpine
# FROM node:14.16.0-alpine3.10
# FROM 446567516155.dkr.ecr.ap-southeast-1.amazonaws.com/docker-image-common:debian-v3
FROM node:14.15.4

ENV TZ Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
# where available (npm@5+)
COPY package*.json ./

RUN npm install
# RUN npm ci --only=production

# Bundle app source
COPY . .

# EXPOSE 1000

CMD [ "node", "main.js" ]
      