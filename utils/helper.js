const constant = require('../utils/constant')
const lodash = require('lodash')
const moment = require('moment')
const { formatDate } = require('./common')
const fs = require('fs');
const CryptoJs = require('crypto-js');
const common = require('../utils/common')

function isBillCanPayment(billOnDueObj, paymentDate, partnerCode) {
  const billEndDate = formatDate({ date: billOnDueObj.end_date })

  if (common.isFactoringLoanChannel(partnerCode)) {
    if (billOnDueObj.accept_payment_date) {
      const acceptPaymentDate = formatDate({ date: billOnDueObj.accept_payment_date })

      if (paymentDate >= acceptPaymentDate) {
        return true
      }
    }
  }

  return paymentDate >= billEndDate
}

// phục vụ các sản phẩm cho phép gạch nợ ngay khi chưa đến due
function checkIsNotDue(billOnDueObj, paymentDate, partnerCode) {
  const billEndDate = formatDate({ date: billOnDueObj.end_date })

  if (!isBillCanPayment(billOnDueObj, paymentDate, partnerCode)) {
    return false
  }

  return paymentDate < billEndDate && billOnDueObj.is_annex == constant.BILL_ON_DUE.IS_ANNEX.FALSE
}

function checkIsOnDue(billOnDueObj, paymentDate, partnerCode) {
  const billDueDate = formatDate({ date: billOnDueObj.due_date })
  const billEndDate = formatDate({ date: billOnDueObj.end_date })
  const isDiffDueDateEndDate = billDueDate != billEndDate

  return paymentDate >= billEndDate && billOnDueObj.is_annex == constant.BILL_ON_DUE.IS_ANNEX.FALSE &&
      ((paymentDate < billDueDate && isDiffDueDateEndDate) || (paymentDate <= billEndDate && !isDiffDueDateEndDate))
}

function checkIsOverDue(billOnDueObj, paymentDate) {
  const billDueDate = formatDate({ date: billOnDueObj.due_date })
  const billEndDate = formatDate({ date: billOnDueObj.end_date })
  const isDiffDueDateEndDate = billDueDate != billEndDate

  return (paymentDate >= billDueDate && isDiffDueDateEndDate) || (paymentDate > billDueDate && !isDiffDueDateEndDate)
}

function getMapBillOnDue({
  listBillOnDue,
  listBillOnDueNotActive,
  paymentDate,
  listPayAllExpire,
  listPayAllNotExpire,
  ruleAllocate,
  totalNonAmt
}) {
  const mapAllBillOnDue = {}
  let isOnDue = false
  let isOverDue = false
  let isAnnex = false
  for (const billOnDueObj of listBillOnDue.rows) {
    const billDueDate = formatDate({ date: billOnDueObj.due_date })
    const billEndDate = formatDate({ date: billOnDueObj.end_date })
    const bodType = billOnDueObj.type
    if (paymentDate < billEndDate) {
      continue
    }
    const isDiffDueDateEndDate = billDueDate != billEndDate
    const checkOnDue =
      paymentDate >= billEndDate &&
      billOnDueObj.is_annex == constant.BILL_ON_DUE.IS_ANNEX.FALSE &&
      ((paymentDate < billDueDate && isDiffDueDateEndDate) || (paymentDate <= billEndDate && !isDiffDueDateEndDate))
    const checkOverDue =
      (paymentDate >= billDueDate && isDiffDueDateEndDate) || (paymentDate > billDueDate && !isDiffDueDateEndDate)

    let key = ''
    if (ruleAllocate == constant.BILL_ON_DUE.RULE_ALLOCATE.HORIZONTAL) {
      key = billOnDueObj.on_due_date.getTime()
    } else if (ruleAllocate == constant.BILL_ON_DUE.RULE_ALLOCATE.VERTICAL && checkOnDue) {
      key = listPayAllNotExpire.find((item) => item.type == bodType)?.idx
    } else if (ruleAllocate == constant.BILL_ON_DUE.RULE_ALLOCATE.VERTICAL && checkOverDue) {
      key = listPayAllExpire.find((item) => item.type == bodType)?.idx
    }
    if (checkOnDue || checkOverDue) {
      !mapAllBillOnDue[key] &&
        (mapAllBillOnDue[key] = {
          data: {}
        })
      !mapAllBillOnDue[key].data[bodType] && (mapAllBillOnDue[key].data[bodType] = [])

      if (checkOnDue) {
        mapAllBillOnDue[key].type = constant.BILL_ON_DUE.RULE_PAYMENT.NOT_EXPIRED
        isOnDue = true
      } else if (checkOverDue) {
        mapAllBillOnDue[key].type = constant.BILL_ON_DUE.RULE_PAYMENT.EXPIRED
        isOverDue = true
      }
      mapAllBillOnDue[key].data[bodType].push(billOnDueObj)
    }
  }
  const totalRemainAmtBillNotActive = lodash.sumBy(listBillOnDueNotActive, function (billNotActive) {
    return Number(billNotActive.remain_amount)
  })
  for (const billOnDueObj of listBillOnDueNotActive) {
    const billDueDate = formatDate({ date: billOnDueObj.due_date })
    const bodType = billOnDueObj.type

    const key = billOnDueObj.on_due_date.getTime()

    !mapAllBillOnDue[key] &&
      (mapAllBillOnDue[key] = {
        data: {}
      })
    !mapAllBillOnDue[key].data[bodType] && (mapAllBillOnDue[key].data[bodType] = [])
    if (
      paymentDate == billDueDate &&
      billOnDueObj.is_annex == constant.BILL_ON_DUE.IS_ANNEX.TRUE &&
      totalNonAmt >= totalRemainAmtBillNotActive
    ) {
      mapAllBillOnDue[key].type = constant.BILL_ON_DUE.RULE_PAYMENT.ANNEX
      isAnnex = true
      mapAllBillOnDue[key].data[bodType].push(billOnDueObj)
    }
  }

  return {
    mapAllBillOnDue,
    isOnDue,
    isOverDue,
    isAnnex
  }
}
function getMapBillOnDueFactoring({
                           listBillOnDue,
                           listBillOnDueNotActive,
                           paymentDate,
                           listPayAllExpire,
                           listPayAllNotExpire,
                                    listPayAllNotDue,
                           totalNonAmt,
                           partnerCode
                         }) {
  const mapAllBillOnDue = {}
  let isAnnex = false
  for (const billOnDueObj of listBillOnDue.rows) {
    const bodType = billOnDueObj.type
    if (!isBillCanPayment(billOnDueObj, paymentDate, partnerCode)) {
      continue
    }

    const checkNotDue = checkIsNotDue(billOnDueObj, paymentDate, partnerCode)
    const checkOnDue = checkIsOnDue(billOnDueObj, paymentDate, partnerCode)
    const checkOverDue = checkIsOverDue(billOnDueObj, paymentDate)

    let key = ''

    let eps = 100000
    // BTT quá hạn là vertical, trong hạn và chưa đến han là horizontal
    if (checkNotDue) {
      key = Number(billOnDueObj.num_cycle) + (listPayAllNotDue.find((item) => item.type == bodType)?.idx) / eps
    } else if (checkOnDue) {
      key = Number(billOnDueObj.num_cycle) + (listPayAllNotExpire.find((item) => item.type == bodType)?.idx) / eps
    } else if (checkOverDue) {
      key = (listPayAllExpire.find((item) => item.type == bodType)?.idx) / eps
    }
    if (checkNotDue || checkOnDue || checkOverDue) {
      !mapAllBillOnDue[key] &&
      (mapAllBillOnDue[key] = {
        data: {},
      })
      !mapAllBillOnDue[key].data[bodType] && (mapAllBillOnDue[key].data[bodType] = [])

      if (checkOnDue) {
        mapAllBillOnDue[key].type = constant.BILL_ON_DUE.RULE_PAYMENT.NOT_EXPIRED
        billOnDueObj.isOnDue = true
      } else if (checkOverDue) {
        mapAllBillOnDue[key].type = constant.BILL_ON_DUE.RULE_PAYMENT.EXPIRED
        billOnDueObj.isOverDue = true
      } else if (checkNotDue) {
        mapAllBillOnDue[key].type = constant.BILL_ON_DUE.RULE_PAYMENT.NOT_DUE
        billOnDueObj.isNotDue = true
      }
      mapAllBillOnDue[key].data[bodType].push(billOnDueObj)
    }
  }
  const totalRemainAmtBillNotActive = lodash.sumBy(listBillOnDueNotActive, function (billNotActive) {
    return Number(billNotActive.remain_amount)
  })
  for (const billOnDueObj of listBillOnDueNotActive) {
    const billDueDate = formatDate({ date: billOnDueObj.due_date })
    const bodType = billOnDueObj.type

    const key = billOnDueObj.on_due_date.getTime()

    !mapAllBillOnDue[key] &&
    (mapAllBillOnDue[key] = {
      data: {},
    })
    !mapAllBillOnDue[key].data[bodType] && (mapAllBillOnDue[key].data[bodType] = [])
    if (
        paymentDate == billDueDate &&
        billOnDueObj.is_annex == constant.BILL_ON_DUE.IS_ANNEX.TRUE &&
        totalNonAmt >= totalRemainAmtBillNotActive
    ) {
      mapAllBillOnDue[key].type = constant.BILL_ON_DUE.RULE_PAYMENT.ANNEX
      isAnnex = true
      mapAllBillOnDue[key].data[bodType].push(billOnDueObj)
    }
  }

  return {
    mapAllBillOnDue,
    isAnnex
  }
}
function getPenaltiesRateByDate(numInstallment, loanAccount, listPenaltiesRate) {
  const filterByTenor = listPenaltiesRate.filter(
    (item) => item.tenor_from <= loanAccount.tenor && item.tenor_to >= loanAccount.tenor
  )
  filterByTenor.sort(function (a, b) {
    return a.instal_to - b.instal_to
  })
  for (const penaltyObj of filterByTenor) {
    if (numInstallment <= penaltyObj.instal_to) {
      return Number(penaltyObj.ir_value)
    }
  }
  return filterByTenor.length ? Number(filterByTenor[filterByTenor.length - 1].ir_value) : 0
}

const getDueDateCalLpi = (endDate, dueDate) => {
  const billDueDate = formatDate({ date: dueDate })
  const billEndDate = formatDate({ date: endDate })
  if (billDueDate > billEndDate) {
    return moment(billEndDate).subtract(1, 'day').format(constant.DATE_FORMAT.YYYYMMDD2)
  }

  return billDueDate
}

const mappingInsuranceCompany = (insuranceObj) => {
  if (!insuranceObj) {
    return ''
  }
  let partner = 'MIC'
  if (insuranceObj.insur_name.includes('MIC')) {
    partner = 'MIC'
  } else if (insuranceObj.insur_name.includes('VAS')) {
    partner = 'VAS'
  } else if (insuranceObj.insur_name.includes('BMI')) {
    partner = 'BMI'
  } else if (insuranceObj.insur_name.includes('GIC')) {
    partner = 'GIC'
  }
  return constant.INSURANCE_COMPANY[partner]
}

const isPrinEveryCycle = (objLoan) => {
  if (isCashLoanPartnerCode(objLoan.partner_code) || objLoan.tenor != objLoan.periodicity) {
    return 1
  }
  return 0
}

const isCashLoanPartnerCode = (partnerCode) => {
  if (global.listCashLoanPartnerCode.includes(partnerCode)) {
    return true
  }
  return false
}

const isLosUnitedPartnerCode = (partnerCode) => {
  return isCashLoanPartnerCode(partnerCode) || [constant.PARTNER_CODE.DNSE, constant.PARTNER_CODE.VUIAPP].includes(partnerCode);
}

const isSendSmsPartnerCode = (partnerCode) => {
  return [constant.PARTNER_CODE.VUIAPP].includes(partnerCode);
}

const validateEmail = (email) => {
  return String(email)
    .toLowerCase()
    .match(
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    )
}

const joinAddress = (addressArray) => addressArray.join(', ');

const getCustomerPerAddress = (customer) => {
    const isNewAddress = !!customer.permanentNewWard;
    if( isNewAddress)
        return joinAddress([customer.addressPer, customer.permanentNewWard, customer.permanentNewProvince]);
    return joinAddress([customer.addressPer ?? '', customer.wardPer ?? '', customer.districtPer ?? '', customer.provincePer ?? '']);
}
const getCustomerCurAddress = (customer) => {
    const isNewAddress = !!customer.currentNewWard;
    if( isNewAddress)
        return joinAddress([customer.addressCur, customer.currentNewWard, customer.currentNewProvince]);
    return joinAddress([customer.addressCur ?? '', customer.wardCur ?? '', customer.districtCur ?? '', customer.provinceCur ?? '']);
}

const getCustomerPhoneNumber = (customer) => {
  return customer.phoneNumber1 || customer.phoneNumber2 || customer.phoneNumber3
}

const checkHaveEarlyTerminationFee = (loanAccount, numInstallment) => {
  if (global.config.data.noTerminationFee.listProductCode.split(',').includes(loanAccount.product_code)) {
    return false
  }
  if (global.config?.data?.mobile?.listPartnerCodeSync.split(',').includes(loanAccount.partner_code)) {
    return false
  }
  if (numInstallment != 1) {
    return false
  }
  if (
    !isCashLoanPartnerCode(loanAccount.partner_code) &&
    !global.config?.data?.mobile?.listChannel.split(',').includes(loanAccount.channel)
  ) {
    return false
  }
  return true
}

const readTemplateFile = (fileName)=>{
  try {
    const fileData = fs.readFileSync(`./templates/${fileName}`);
    return fileData.toString();
  } catch (error) {
    console.log(`[readTemplateHtml] path: ${fileName}, error ${error}`);
    return '';
  }
}

const hashMD5 = (text) => {
  return CryptoJs.MD5(text).toString();
};

module.exports = {
  hashMD5,
  getMapBillOnDue,
  getMapBillOnDueFactoring,
  getPenaltiesRateByDate,
  getDueDateCalLpi,
  mappingInsuranceCompany,
  isPrinEveryCycle,
  isCashLoanPartnerCode,
  isLosUnitedPartnerCode,
  validateEmail,
  getCustomerPerAddress,
  getCustomerCurAddress,
  getCustomerPhoneNumber,
  checkHaveEarlyTerminationFee,
  readTemplateFile,
  isSendSmsPartnerCode,
}
