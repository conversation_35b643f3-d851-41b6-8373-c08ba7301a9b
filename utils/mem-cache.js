function getCache(key) {
  if (key) {
    return global.repaymentMemCache[key]
  }
  return global.repaymentMemCache
}
function checkCache(key) {
  if (global.repaymentMemCache[key]) {
    return true
  }
  return false
}
function setCache(key, value = 1) {
  global.repaymentMemCache[key] = value
}

function clearCache(key) {
  delete global.repaymentMemCache[key]
}
module.exports = {
  getCache,
  setCache,
  checkCache,
  clearCache
}
