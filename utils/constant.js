const DISBURSEMENT = {
  TRANS_STATUS: {
    INIT: '0',
    DISBURMENT_IN_PROGESSING: '2',
    ACTIVATED: '1',
    FAILED: '3',
    codeToStatus: {
      '0': 'INIT',
      '2': 'DISBURMENT_IN_PROGESSING',
      '1': 'ACTIVATED',
      '3': 'FAILED'
    }
  },
  ACC_TYPE: {
    ACC: 'ACC',
    PAN: 'PAN'
  },
  TYPE: {
    CASH: '1',
    TRANSFER: '2'
  },
  RESP_CODE:{
    SUCCESS: "200",
    FAILED: "400"
  }
}
const PAYMENT_STATUS = {
  DONE: 0,
  INIT: 1,
  PROCESSING: 2
}
const INSTALLMENT = {
  TYPE: {
    PRIN: 1,
    INT: 2,
    LPI_PRIN: 3,
    LPI_INT: 4,
    PREF_INT: 8,
    FACTORING_FEE: 7,
    FEE: 5,
    PEN_FEE: 6
  },
  CLOSE: {
    TRUE: 1,
    FALSE: 0
  },
  IS_ANNEX: {
    TRUE: 1,
    FALSE: 0
  },
  STATUS: {
    ACTIVE: 1,
    DEACTIVE: 0,
    ARCHIVE: 2
  },
  PAYMENT_STATUS: {
    ACTIVE: 1,
    DONE: 0
  }
}
const BILL_ON_DUE = {
  TYPE: {
    PRIN: 1,
    INT: 2,
    LPI_PRIN: 3,
    LPI_INT: 4,
    FEE: 5,
    PEN_FEE: 6,
    FACTORING_FEE: 7,
    PREFERENTIAL_INT: 8,
  },
  IS_ANNEX: {
    TRUE: 1,
    FALSE: 0
  },
  STATUS: {
    ACTIVE: 1,
    NOT_ACTIVE: 0,
    CANCEL: 2
  },
  PAYMENT_STATUS: {
    ACTIVE: 1,
    DONE: 0,
    NOT_DONE: 2
  },
  RULE_PAYMENT: {
    NOT_EXPIRED: 0,
    ANNEX: 1,
    EXPIRED: 2,
    NOT_DUE: 3,
  },
  RULE_ALLOCATE: {
    VERTICAL: 'vertical', // gạch theo chiều dọc - VD: Quá hạn gốc 1 => Qúa hạn gốc 2=> Quá hạn lãi 1 => Qúa hạn lãi 2 ...
    HORIZONTAL: 'horizontal' // gạch theo chiều ngang  - VD: Quá hạn gốc 1 => Qúa hạn lãi 1=> Quá hạn gốc 2 => Qúa hạn lãi 2 ...
  }
}
const PAYMENT_DETAIL_POST_AFFECT = {
  RFIN: 'RFIN',
  DBRS: 'DBRS',
  PDBRS: 'PDBRS',
  RENRH: 'RENRH',
  RHONO: 'RHONO',
  FRHONO: 'FRHONO',
  DP: 'DP',
  REFUND: 'REFUND',
  SUSPEND: 'SUSPEND',
}
const ANNEX = {
  TYPE: {
    FULL_EARLY_TERMINATION: 'FET',
    EARLY_TERMINATION: 'ET'
  },
  STATUS: {
    CALCULATE: -1,
    INIT: 0,
    DONE: 1,
    CANCEL: 2,
    codeToStatus: {
      '-1': 'UNDER_CONSTRUCTION',
      0: 'ACCEPTED',
      1: 'ACTIVATED',
      2: 'CANCELED'
    }
  },
  STATUS_COMMON: {
    INIT: 'ACC',
    DONE: 'ACT',
    CANCEL: 'ANN'
  }
}
const WELCOME_PACKAGE = {
  LIST_EMAIL: '<EMAIL>'
}
const ACTIVE_MQ_WARNING = {
  SUBJECT: 'WARNING: LMS-MC SERVICE CANNOT CONNECT TO ACTIVE-MQ',
  LIST_EMAIL: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
}
const EVENT_OPSCODE = {
  LOAN_ACTIVATION: 'LOAN_ACTIVATION',
  PAYMENT_ALLOCATION: 'PAYMENT_ALLOCATION',
  PAYMENT_RECONCILIATION: 'PAYMENT_RECONCILIATION',
  ACCURAL_INTEREST: 'ACCURAL_INTEREST',
  PROVISON: 'PROVISON2',
  OVERDUE_PRINCIPAL: 'OVERDUE_PRINCIPAL',
  COOLING_OFF: 'COOLING_OFF',
  ICNE: 'ICNE',
  MATCHING: 'MATCHING',
  PROFIT: 'PROFIT',
  RECONCILIATION: 'RECONCILIATION'
}
const ENV = {
  INTERNAL: 'internalLb',
  LOCALHOST: 'localhost'
}
const DATE_FORMAT = {
  YYYYMMDD_HHMMSS: 'yyyy-mm-dd HH:mm:ss',
  YYYYMMDD: 'yyyy-mm-dd',
  // DDMMYYYY: 'dd-mm-yyyy',
  YYYYMMDD_HHmmss: 'YYYY-MM-DD HH:mm:ss',
  YYYYMMDD_HHmmssSSS: 'YYYY-MM-DD HH:mm:ss.SSS',
  DDMMYYYY2: 'dd/mm/yyyy',
  YYYYMMDD2: 'YYYY-MM-DD',
  YYYYMMDD3: 'YYYYMMDD',
  DDMMYYYY: 'DD/MM/YYYY',
  MMYYYY: 'MM-YYYY',
  HHMM: 'HH:mm',
}
const TRANS_TYPE = {
  IN: 'IN',
  OUT: 'OUT'
}
const CONTRACT_LIMIT_STATUS = {
  1: 'Active',
  0: 'Not Active',
  statusToCode: {
    ACTIVE: 1,
    NOT_ACTIVE: 0
  }
}
const DEBT_ACK_STATUS = {
  1: 'Đang hiệu lực',
  0: 'Chờ hiệu lực',
  3: 'Đã tất toán',
  2: 'Đã huỷ',
  ACTIVE: 1,
  SIG: 0,
  CANCEL: 2,
  TERMINATED: 3,
  ACTIVE_STR: 'ACT',
  SIG_STR: 'SIG',
  CANCEL_STR: 'ANN',
  TERMINATED_STR: 'TER',
  codeToStatus: {
    1: 'ACTIVATED',
    0: 'PENDING',
    3: 'TERMINATED'
  },
  codeToStatusCommon: {
    0: 'SIG',
    1: 'ACT',
    2: 'ANN',
    3: 'TER'
  }
}
const LOAN_ACC_STATUS = {
  ACT: 1,
  SIG: 0,
  ANN: 2,
  TER: 3,
  codeToStatus: {
    1: 'ACT',
    0: 'SIG',
    2: 'ANN',
    3: 'TER'
  }
}
const RISK_GROUP = {
  DPD_RG: 'DPD',
  OBSER_RG: 'OBS',
  USER_RG: 'USE',
  CONTRACT_RG: 'CON',
  INDIVIDUAL_RG: 'IND',
  CIC_RG: 'CIC',
  FINAL_RG: 'FIN'
}
const IS_WRITE_OFF = 2
const DEFAULT_RG = 1
const DEFAULT_DPD = 0
const PAYMENT = {
  PAY_TYPE: {
    IN: 'IN',
    OUT: 'OUT'
  },
  TRANS_STATUS: {
    ACT: 'ACT',
    TRANSFER: 'TRANSFER',
    RECEIVE: 'RECEIVE',
    REFUND: 'REFUND'
  },
  STATUS: {
    ACTIVE: 1,
    CANCELED: 2
  }
}
const FLAG_ACTIVE_INIT = 0
const FLAG_ACTIVE = 1
const FLAG_NOT_ACTIVE = 2
const CALCUCFG = {
  totalDayOfYear: 365,
  scale: -3,
  scaleVoucher: 0,
  scaleOfDay: 2,
  penalitiesRateAnnex: 0,
  defaultFee: 12000,
  minAnnex: 100000,
  minAnnexRate: 0.05,
  futureDateRePayment: 5
}
const CONTRACT_TYPE = {
  CASHLOAN: 'CASHLOAN',
  CREDITLINE: 'CREDITLINE'
}
const EARLY_TERMINATION_RATE_TYPE = 'EARLY_TERMINATION_RATE'
const IR_PRIN_NORMAL_RATE_TYPE = 'OFFER_NORMAL_RATE'
const IR_PRIN_OVERDUE_RATE_TYPE = 'OVER_DUE_RATE'
const config = {
  ownerId: 1,
  createdBy: 'system',
  isTesting: 0,
  createdUser: 1,
  isContractBreach: 1
}
const MAX_DIFFERENT_DAYS_BETWEEN_KUNN_CASH_LOAN = 30
const PARTNER_CODE = {
  MISA: 'MIS',
  VOUCHER: 'EVC',
  DNSE: 'DNSE',
  BIZZ: 'BIZZ',
  VUIAPP: 'VUIP',
  FINV: 'FINV',
}
const IR_CHARGE_STATUS = {
  ACTIVE: 1,
  NOT_ACTIVE: 0
}
const IR_CHARGE_TYPE = {
  ON_DUE_PRIN: 1,
  OVER_DUE_PRIN: 2,
  OVER_DUE_INTEREST: 3,
  PREFERENTIAL_RATE: 4,
}
const IR_NAME = {
  OFFER_NORMAL_RATE: 'OFFER_NORMAL_RATE',
  OVER_DUE_RATE: 'OVER_DUE_RATE',
  PREFERENTIAL_RATE: 'PREFERENTIAL_RATE'
}
const DISBURSEMENT_TYPE = {
  DISBURSEMENT: 'disbursement',
  REFUND: 'refund',
}
const MIN_PAID_TYPE = {
  PRIN: 'PRIN',
  INTEREST: 'INTEREST',
  FEE: 'FEE'
}
const PROMOTION_TYPE = {
  RATE: 'RATE',
  FIX_AMT: 'FIX_AMT'
}
const EVENT_DI_NAME = {
  TERMINATED: 'loan_terminated',
  EMI_CHANGED: 'emi_changed',
  LOAN_ACTIVED: 'loan_activated'
}
const KUNN_TYPE = {
  AMORT: 'AMORT',
  WITHDRAWAL: 'WITHDRAWAL'
}
const FEE_CODE = {
  MONTHY: 'FEE_M',
  ANNEX: 'FEE_ANNEX'
}
const ANNEX_PENDING_STATUS = {
  INIT: 0,
  ACTIVE: 1,
  PENDING: 2,
  NOTIFIED: 3,
  CANCEL: -1
}
const DPD_TRIGGER_TYPE = {
  CRON_JOB: 'CRON_JOB',
  UPDATE_CIC: 'UPDATE_CIC',
  OTHER: 'OTHER'
}

const REQUEST_EVENT_NAME = {
  CREATE_LOAN_CONTRACT_LIMIT: 'CREATE_LOAN_CONTRACT_LIMIT',
  UPDATE_REVIEW_LIMIT: 'UPDATE_REVIEW_LIMIT',
  CREATE_KUNN: 'CREATE_KUNN',
  ACTIVE_KUNN: 'ACTIVE_KUNN',
  ACTIVE_BACKDATE_KUNN: 'ACTIVE_BACKDATE_KUNN',
  REFUND_SUSPEND_FACTORING: 'REFUND_SUSPEND_FACTORING',
  CALLBACK_REFUND_SUSPEND: 'CALLBACK_REFUND_SUSPEND',
  TRANSFER_SUSPEND_FACTORING: 'TRANSFER_SUSPEND_FACTORING',
  CANCEL_KUNN: 'CANCEL_KUNN',
  ADJUST_INTEREST_JOB: 'ADJUST_INTEREST_JOB',
  LOAD_BILL: 'LOAD_BILL',
  LOAD_BILL_JOB: 'LOAD_BILL_JOB',
  PAYMENT: 'PAYMENT',
  PAYMENT_SUSPEND: 'PAYMENT_SUSPEND',
  REPAYMENT_JOB: 'REPAYMENT_JOB',
  CANCEL_PAYMENT: 'CANCEL_PAYMENT',
  TRANSFER_CASE: 'TRANSFER_CASE',
  UPDATE_DPD: 'UPDATE_DPD',
  UPDATE_DPD_JOB: 'UPDATE_DPD_JOB',
  SAVE_USER_RISKGROUP: 'SAVE_USER_RISKGROUP',
  IMPORT_CIC_FILE: 'IMPORT_CIC_FILE',
  IMPORT_HOLIDAY: 'IMPORT_HOLIDAY',
  SIMULATION_INSTALLMENT: 'SIMULATION_INSTALLMENT',
  EXPORT_INSTALLMENT: 'EXPORT_INSTALLMENT',
  UPDATE_IR: 'UPDATE_IR',
  UPDATE_IR_JOB: 'UPDATE_IR_JOB',
  UPDATE_IR_JOB_FACTORING: 'UPDATE_IR_JOB_FACTORING',
  REFUND_MONEY: 'REFUND_MONEY',
  EXPORT_STATEMENT: 'EXPORT_STATEMENT',
  GET_LOAN_NOTIFY: 'GET_LOAN_NOTIFY',
  CANCEL_ANNEX: 'CANCEL_ANNEX',
  CANCEL_ANNEX_DRAFT: 'CANCEL_ANNEX_DRAFT',
  CANCEL_ANNEX_JOB: 'CANCEL_ANNEX_JOB',
  SIMULATION_ANNEX: 'SIMULATION_ANNEX',
  CREATE_ANNEX: 'CREATE_ANNEX',
  CREATE_ANNEX_JOB: 'CREATE_ANNEX_JOB',
  CREATE_ANNEX_PENDING: 'CREATE_ANNEX_PENDING',
  CONFIRM_ANNEX_PENDING: 'CONFIRM_ANNEX_PENDING',
  ACTIVE_ANNEX_PENDING: 'ACTIVE_ANNEX_PENDING',
  ACTIVE_ANNEX_PENDING_JOB: 'ACTIVE_ANNEX_PENDING_JOB',
  NOTIFY_ANNEX_PENDING_JOB: 'NOTIFY_ANNEX_PENDING_JOB',
  CALL_RECEIVE_MONEY_BE: 'CALL_RECEIVE_MONEY_BE',
  CALL_UPDATE_STATUS_BE: 'CALL_UPDATE_STATUS_BE',
  CALL_NOTIFY_DPD_BE: 'CALL_NOTIFY_DPD_BE',
  CALL_CONFIRM_ANNEX_BE: 'CALL_CONFIRM_ANNEX_BE',
  CALL_UPDATE_STATUS_ANNEX_BE: 'CALL_UPDATE_STATUS_ANNEX_BE',
  DISBURSEMENT_REQUEST: 'DISBURSEMENT_REQUEST',
  CALLBACK_REFUND_REQUEST: 'CALLBACK_REFUND_REQUEST',
  CALLBACK_REFUND_REQUEST_STATUS: 'CALLBACK_REFUND_REQUEST_STATUS',
  REFUND_REQUEST: 'REFUND_REQUEST',
  CREATE_KUNN_CRM: 'CREATE_KUNN_CRM',
  TERMINATION_DEBT_CRM: 'TERMINATION_DEBT_CRM',
  ACTIVE_LOAN_CRM: 'ACTIVE_LOAN_CRM',
  UPDATE_DI_CRM: 'UPDATE_DI_CRM',
  UPDATE_TER_CRM: 'UPDATE_TER_CRM',
  ACTIVE_KUNN_LOS: 'ACTIVE_KUNN_LOS',
  UPDATE_TER_LOS: 'UPDATE_TER_LOS',
  UPDATE_ANN_LOS: 'UPDATE_ANN_LOS',
  QUEUE_DPD_RISKGRP_CRM: 'QUEUE_DPD_RISKGRP_CRM',
  CALL_GCN_MIC: 'CALL_GCN_MIC',
  CALL_GCN_BMI: 'CALL_GCN_BMI',
  ACTIVE_INSURANCE: 'ACTIVE_INSURANCE',
  SAVE_ACTION_AUDIT: 'SAVE_ACTION_AUDIT',
  UPDATE_FREEZE: 'UPDATE_FREEZE',
  UPDATE_DISBUR: 'UPDATE_DISBUR'
}
const FEE = {
  FEE_CAL_TYPE: {
    FIRST_TIME: 'FIRST_TIME',
    PICK_TIME: 'PICK_TIME',
    PERIODICAL: 'PERIODICAL'
  },
  TYPE_CALCULATE: {
    RECIPE: 'recipe',
    FIXED_AMOUNT: 'fixedAmount',
    REPAYMENT: 'REPAYMENT'
  }
}
const PAY_ALLOCATE = {
  NOT_DUE: 'PAY_ALLO_NOT_DUE', // hỗ trợ với các sản phẩm gạch ngay khi chưa đến kỳ due
  NOT_EXPIRE: 'PAY_ALLO_NOT_EXPIRE',
  EXPIRE: 'PAY_ALLO_EXPIRE'
}

const INSURANCE_COMPANY = {
  VAS: 'Bảo Hiểm Viễn Đông',
  MIC: 'Bảo Hiểm Quân Đội',
  BMI: 'Bảo Hiểm Bảo Minh',
  GIC: 'Bảo Hiểm Toàn Cầu'
}
const INSURANCE_PARTNER_CODE = {
  MIC: 'MIC',
  BMI: 'BMI',
}
const MAP_FREEZE_STATUS = {
  1: 'LOCK',
  0: 'UNLOCK'
}

const GROUP_ACC_TYPE = {
  MULTI_ACC:'MA',
  SINGLE_ACC:'SA'
}
const LOAN_ORDER_FEE = {
  STATUS: {
    ACTIVE: 1,
    INACTIVE: 0
  }
}

module.exports = {
  INSURANCE_PARTNER_CODE,
  PAY_ALLOCATE,
  FEE,
  FEE_CODE,
  PARTNER_CODE,
  DISBURSEMENT,
  EVENT_OPSCODE,
  PAYMENT_STATUS,
  INSTALLMENT,
  BILL_ON_DUE,
  WELCOME_PACKAGE,
  ACTIVE_MQ_WARNING,
  ENV,
  DATE_FORMAT,
  TRANS_TYPE,
  DEBT_ACK_STATUS,
  CONTRACT_LIMIT_STATUS,
  RISK_GROUP,
  IS_WRITE_OFF,
  DEFAULT_RG,
  CALCUCFG,
  EARLY_TERMINATION_RATE_TYPE,
  IR_PRIN_NORMAL_RATE_TYPE,
  IR_PRIN_OVERDUE_RATE_TYPE,
  config,
  ANNEX,
  CONTRACT_TYPE,
  PAYMENT,
  MAX_DIFFERENT_DAYS_BETWEEN_KUNN_CASH_LOAN,
  IR_CHARGE_STATUS,
  IR_CHARGE_TYPE,
  FLAG_ACTIVE_INIT,
  FLAG_ACTIVE,
  FLAG_NOT_ACTIVE,
  PAYMENT_DETAIL_POST_AFFECT,
  MIN_PAID_TYPE,
  PROMOTION_TYPE,
  EVENT_DI_NAME,
  KUNN_TYPE,
  REQUEST_EVENT_NAME,
  ANNEX_PENDING_STATUS,
  DPD_TRIGGER_TYPE,
  DEFAULT_DPD,
  LOAN_ACC_STATUS,
  INSURANCE_COMPANY,
  MAP_FREEZE_STATUS,
  GROUP_ACC_TYPE,
  LOAN_ORDER_FEE,
  IR_NAME,
  DISBURSEMENT_TYPE,
}
