/**
 * Thu vien su dung cac ham de goi API, lay configuration, ghi log, push notification
 */
const dateFormat = require('dateformat')
const axios = require('axios')
const DateDiff = require('date-diff')
const moment = require('moment')
const requestLogRepo = require('../repositories/request-log-repo')
const constant = require('../utils/constant')
const PizZip = require('pizzip')
const Docxtemplater = require('docxtemplater')
const fs = require('fs')
const libre = require('libreoffice-convert')
const pino = require('pino')

const logger = pino({
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true
    }
  }
})

let moduleName
/**
 * Ham set ten service cho common.
 * @param {*} name
 */
const setServiceName = function (name) {
  moduleName = name
}
/**
 * Ham ghi log
 * @param {*} event
 * @param {*} l
 */
const log = function (event) {
  const day = formatDate({ format: constant.DATE_FORMAT.YYYYMMDD_HHmmss })
  const str = day + '|' + moduleName + '|' + event
  logger.info(str)
}

const logError = function (event) {
  const day = formatDate({ format: constant.DATE_FORMAT.YYYYMMDD_HHmmss })
  const str = day + '|' + moduleName + '|' + event
  logger.error(str)
}

const getAPI = function (link, header, logConfig = { isLog: false }) {
  return new Promise(function (resolve, reject) {
    try {
      const requestTime = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS)
      const config = {
        method: 'get',
        url: link,
        headers: header
      }
      const { eventType, isLog } = logConfig
      axios(config)
        .then(function (response) {
          writeLogCallApi({
            eventType: eventType || link,
            api: link,
            serviceType: 2,
            method: 'GET',
            requestTime,
            resBody: response.data,
            responseCode: response.data.code || '0',
            statusCode: response.statusCode,
            reqHeaders: header,
            isLog
          })
          resolve(response.data)
        })
        .catch((error) => {
          if (error.response) {
            resolve(error.response.data)
          } else {
            reject(error)
          }
        })
    } catch (e) {
      reject(e)
    }
  })
}
const getAPIV2 = function (link, header, responseType, logConfig = { isLog: false }) {
  return new Promise(function (resolve, reject) {
    try {
      const requestTime = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS)
      const config = {
        method: 'get',
        url: link,
        headers: header
      }
      if (responseType) {
        config.responseType = responseType
      }
      const { eventType, isLog } = logConfig
      axios(config)
          .then(function (response) {
            writeLogCallApi({
              eventType: eventType || link,
              api: link,
              serviceType: 2,
              method: 'GET',
              requestTime,
              resBody: response.data,
              responseCode: response.data.code || '0',
              statusCode: response.statusCode,
              reqHeaders: header,
              isLog
            })
            resolve(response.data)
          })
          .catch((error) => {
            if (error.response) {
              resolve(error.response.data)
            } else {
              reject(error)
            }
          })
    } catch (e) {
      reject(e)
    }
  })
}
const postAPI = function (
  link,
  json,
  form,
  header = { 'Content-type': 'application/json' },
  logConfig = { isLog: false }
) {
  return new Promise(function (resolve, reject) {
    const { eventType, isLog, contractNumber } = logConfig
    try {
      const requestTime = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS)
      const qs = require('qs')
      const dataForm = qs.stringify(form)
      const dataJson = JSON.stringify(json)
      const config = {
        method: 'post',
        url: link,
        headers: header,
        data: json !== undefined ? dataJson : dataForm
      }
      axios(config)
        .then(function (response) {
          writeLogCallApi({
            api: link,
            eventType: eventType || link,
            contractNumber:
              json.debtAckContractNumber ||
              json.debt_ack_contract_number ||
              json.kunnNumber ||
              json.contractNumber ||
              json.ContractNumber ||
              json.contract_number ||
              contractNumber ||
              '',
            custId: json.custId || json.cust_id,
            serviceType: 2,
            method: 'POST',
            reqBody: json || form,
            requestTime,
            resBody: response.data,
            responseCode: response.data.code || 200,
            statusCode: '200',
            reqHeaders: header,
            isLog
          })
          resolve(response.data)
        })
        .catch((error) => {
          const responseError = error.response || {}
          writeLogCallApi({
            api: link,
            eventType: eventType || link,
            contractNumber:
              json.debtAckContractNumber ||
              json.debt_ack_contract_number ||
              json.contractNumber ||
              json.ContractNumber ||
              json.contract_number ||
              contractNumber ||
              '',
            custId: json.custId || json.cust_id,
            serviceType: 2,
            method: 'POST',
            reqBody: json || form,
            requestTime,
            resBody: responseError.data || error.message,
            responseCode: responseError.data ? responseError.data.code : -1,
            statusCode: responseError.status || '500',
            reqHeaders: header,
            isLog
          })
          reject(error.message)
        })
    } catch (e) {
      reject(e)
    }
  })
}

async function postApiV2(
  link,
  json,
  header = {
    'Content-Type': 'application/json'
  }
) {
  return new Promise(function (resolve, reject) {
    try {
      const requestTime = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS)
      const config = {
        method: 'post',
        url: link,
        headers: header,
        data: json
      }
      axios(config)
        .then(function (response) {
          writeLogCallApi({
            eventType: link,
            contractNumber:
              json.debtAckContractNumber ||
              json.debt_ack_contract_number ||
              json.contractNumber ||
              json.contract_number ||
              '',
            custId: json.custId || json.cust_id,
            serviceType: 2,
            method: 'POST',
            reqBody: json,
            requestTime,
            resBody: response.data,
            responseCode: response.data.code || 200,
            statusCode: '200',
            reqHeaders: header
          })
          resolve(response.data)
        })
        .catch(function (error) {
          const responseError = error.response || {}
          writeLogCallApi({
            eventType: link,
            contractNumber:
              json.debtAckContractNumber ||
              json.debt_ack_contract_number ||
              json.contractNumber ||
              json.contract_number ||
              '',
            custId: json.custId || json.cust_id,
            serviceType: 2,
            method: 'POST',
            reqBody: json,
            requestTime,
            resBody: responseError.data || error.message,
            responseCode: responseError.data ? responseError.data.code : -1,
            statusCode: responseError.status || '500',
            reqHeaders: header
          })
          reject(error.message)
        })
    } catch (e) {
      reject(e)
    }
  })
}

const putAPI = function (link, json, header, logConfig = { isLog: false }) {
  return new Promise(function (resolve, reject) {
    const requestTime = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS)
    const data = JSON.stringify(json)
    const config = {
      method: 'put',
      url: link,
      headers: {
        'Content-Type': 'application/json'
      },
      data
    }
    json = json || {}
    const { eventType, isLog, contractNumber } = logConfig

    axios(config)
      .then(function (response) {
        writeLogCallApi({
          api: link,
          eventType: eventType || link,
          contractNumber:
            json.debtAckContractNumber ||
            json.debt_ack_contract_number ||
            json.contractNumber ||
            json.ContractNumber ||
            json.contract_number ||
            contractNumber ||
            '',
          custId: json.custId || json.cust_id,
          serviceType: 2,
          method: 'PUT',
          reqBody: json,
          requestTime,
          resBody: response.data,
          responseCode: response.data.code || 200,
          statusCode: '200',
          reqHeaders: header,
          isLog
        })
        resolve(response.data)
      })
      .catch(function (error) {
        console.log(error.message)
        const responseError = error.response || {}
        writeLogCallApi({
          api: link,
          eventType: eventType || link,
          contractNumber:
            json.debtAckContractNumber ||
            json.debt_ack_contract_number ||
            json.contractNumber ||
            json.ContractNumber ||
            json.contract_number ||
            contractNumber ||
            '',
          custId: json.custId || json.cust_id,
          serviceType: 2,
          method: 'PUT',
          reqBody: json,
          requestTime,
          resBody: responseError.data || error.message,
          responseCode: responseError.data ? responseError.data.code : -1,
          statusCode: responseError.status || '500',
          reqHeaders: header,
          isLog
        })
        reject(error)
      })
  })
}

const convertDatetoString = function (date, format) {
  return dateFormat(date, format)
}
function makeId(length) {
  const result = []
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result.push(characters.charAt(Math.floor(Math.random() * charactersLength)))
  }
  return result.join('')
}

const calNextCycleDate = function (objDate) {
  const month30day = [4, 6, 9, 11]
  const month = Number(objDate.split('-')[1]) // months from 1-12
  let day = Number(objDate.split('-')[2])
  let year = Number(objDate.split('-')[0])
  const monthNext = month == 12 ? 1 : month + 1
  year = month == 12 ? year + 1 : year
  if (month30day.includes(monthNext) && day == 31) {
    day = 30
  } else if (monthNext == 2 && (day == 30 || day == 31 || day == 29)) {
    day = year % 4 == 0 ? 29 : 28
  }
  return year + '-' + monthNext + '-' + day
}
const getDayOfMonth = function (objDate, day) {
  const month30day = [4, 6, 9, 11]
  const month = Number(objDate.split('-')[1]) // months from 1-12
  // let day = Number(objDate.split("-")[2])
  const year = Number(objDate.split('-')[0])
  if (month30day.includes(month) && day == 31) {
    day = 30
  } else if (month == 2 && (day == 30 || day == 31 || day == 29)) {
    day = year % 4 == 0 ? 29 : 28
  }
  return year + '-' + month + '-' + day
}
/**
 * Ham tinh ngay tiep theo chu ky
 * @param {*} after khoang cach thang nhay buoc
 * @param {*} objDate
 * @returns
 */
const calNextCycleDate2 = function (after, objDate) {
  let current
  const month30day = [4, 6, 9, 11]
  let month = Number(objDate.split('-')[1]) // months from 1-12
  const day = Number(objDate.split('-')[2])
  let year = Number(objDate.split('-')[0])
  if (month == 12) {
    month = 1
    year = year + 1
  } else {
    month = month + 1
  }
  const monthString = `0${month}`.slice(-2)
  const dayString = `0${day}`.slice(-2)
  if (after == 1 && month30day.includes(month) && day == 31) {
    current = year + '-' + monthString + '-' + 30
  } else if (after == 1 && month == 2 && (day == 30 || day == 31 || day == 29)) {
    current = year % 4 == 0 ? year + '-' + monthString + '-' + 29 : year + '-' + monthString + '-' + 28
  } else {
    current = year + '-' + monthString + '-' + dayString
  }
  return after == 1 ? current : calNextCycleDate2(after - 1, current)
}

const calNextCycleDateV3 = function (after, objDate, format = constant.DATE_FORMAT.YYYYMMDD2) {
  const date = moment(objDate)
  date.add(after, 'month')
  return date.format(format)
}

function getFirstNextTenor(startDate, endDay, deltaMonth, tenor) {
  const endDate = new Date(startDate)
  endDate.setDate(1)
  endDate.setMonth(endDate.getMonth() + deltaMonth)
  const daysInMonth = moment(endDate).daysInMonth()
  if (tenor == 1 && endDay >= daysInMonth) {
    endDate.setDate(daysInMonth)
  } else {
    endDate.setDate(endDay)
  }
  return endDate
}
/**
 * Ham lay ngay tuong ung cua thang
 * @param {*} activateDate
 * @param {*} dayNum
 * @returns
 */
const calCycleDateOfMonth = function (activateDate, dayNum) {
  const month30day = [4, 6, 9, 11]
  const month = activateDate.getMonth() + 1
  const year = activateDate.getFullYear()
  if (month30day.includes(month) && dayNum == 31) {
    dayNum = 30
  } else if (month == 2 && (dayNum == 30 || dayNum == 31 || dayNum == 29)) {
    dayNum = year % 4 == 0 ? 29 : 28
  }

  return new Date(year + '-' + month + '-' + dayNum)
}
/**
 * Ham next thang
 * @param {*} after So thang muon cong them
 * @param {*} now ngay hien tai
 * @returns
 */
function addMonths(after = 1, now = new Date()) {
  let current
  if (now.getMonth() == 11) {
    current = new Date(now.getFullYear() + 1, 0, 1)
  } else {
    current = new Date(now.getFullYear(), now.getMonth() + 1, 1)
  }
  return after == 1 ? current : addMonths(after - 1, new Date(now.getFullYear(), now.getMonth() + 1, 1))
}

function roundUp(num, precision) {
  precision = Math.pow(10, precision)
  return Math.ceil(num * precision) / precision
}

function round(num, precision) {
  precision = Math.pow(10, precision)
  return Math.round(num * precision) / precision
}
function roundV2(num, precision, ep = 0.0) {
  precision = Math.pow(10, precision)
  return Math.round(num * precision + ep) / precision
}
function convertCurrency(num) {
  const strNum = num.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
  return strNum.replace('.00', '')
}
function dateDiff(date1, date2) {
  const diff = new DateDiff(date1, date2)
  return diff
}

function getDifferencesDays(firstDate, secondDate, isAbs = true) {
  const fromDate = moment(firstDate)
  const toDate = moment(secondDate)
  const differencesDays = fromDate.diff(toDate, 'days')
  return isAbs ? Math.abs(differencesDays) : differencesDays
}
function addDays(date, days) {
  const result = new Date(date)
  result.setDate(result.getDate() + days)
  return result
}
function isEndOfMonth(dateObj) {
  const date = new Date(dateObj)
  // console.log('a: ',moment(dateObj))
  // console.log('b: ',moment(new Date(date.getFullYear(), date.getMonth() + 1, 0)))
  if (moment(dateObj).unix() == moment(new Date(date.getFullYear(), date.getMonth() + 1, 0)).unix()) {
    return true
  }
  return false
}
function countMonth31Day(startDate, toDate) {
  const month31Day = [1, 3, 5, 7, 8, 10, 12]
  let result = 0
  const currentDayToDate = toDate.getDate()
  if (currentDayToDate == 31) {
    result++
  }
  let firstDayMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1)
  const firstDayMonthToDate = new Date(toDate.getFullYear(), toDate.getMonth(), 1)
  while (firstDayMonth < firstDayMonthToDate) {
    const dayMonth = firstDayMonth.getMonth() + 1
    if (month31Day.includes(dayMonth)) {
      result++
    }
    firstDayMonth = addMonths(1, firstDayMonth)
  }
  return result
}
function getRandomInt(min, max) {
  min = Math.ceil(min)
  max = Math.floor(max)
  return Math.floor(Math.random() * (max - min + 1)) + min
}
function writeLogCallApi(data) {
  try {
    if (data.isLog == false) {
      return
    }
    const now = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS)
    const sendData = {
      api: data.api,
      eventType: data.eventType,
      contractNumber:
        data.debtAckContractNumber || data.debt_ack_contract_number || data.contractNumber || data.contract_number,
      custId: data.custId || data.cust_id,
      serviceType: data.serviceType,
      tranTime: data.tranTime || now,
      method: data.method,
      requestBody: JSON.stringify(data.reqBody),
      requestTime: data.requestTime,
      createdDate: now,
      updatedDate: now,
      createdUser: 'system',
      ownerId: 1,
      response: JSON.stringify(data.resBody),
      responseCode: data.responseCode,
      statusCode: data.statusCode,
      responseTime: now,
      requestParams: JSON.stringify(data.reqParams),
      requestQuery: JSON.stringify(data.reqQuery),
      requestHeaders: JSON.stringify(data.reqHeaders)
    }
    if (global.poolWrite) requestLogRepo.insert(global.poolWrite, sendData)
  } catch (e) {
    console.log(e)
    log(`Error at Write log ${data.eventType}`, 'error')
  }
}
function getDifferenceInDays(date2, date1) {
  const _MS_PER_DAY = 1000 * 60 * 60 * 24
  date1 = new Date(date1)
  date2 = new Date(date2)
  const utc1 = Date.UTC(date1.getFullYear(), date1.getMonth(), date1.getDate())
  const utc2 = Date.UTC(date2.getFullYear(), date2.getMonth(), date2.getDate())

  return Math.floor((utc2 - utc1) / _MS_PER_DAY)
}

function getContractTypeByLoanAcc(loanAccObj) {
  const contractTypeList = []
  if (loanAccObj?.is_debt_sales == 1) {
    contractTypeList.push('DEBT SALE')
  }
  if (loanAccObj?.is_debt_structure == 1) {
    contractTypeList.push('CCN')
  }
  if (loanAccObj?.is_writing_off == 1) {
    contractTypeList.push('WO')
  }
  return contractTypeList.join(',')
}
async function callAxios(url, data = {}, method = 'POST', headerExtra = {}, timeout = 90000) {
  let result = null
  let headers = {
    Accept: 'application/json',
    'Content-type': 'application/json'
  }
  try {
    if (headerExtra) {
      headers = { ...headers, ...headerExtra }
    }
    await axios({
      url,
      method,
      headers,
      data: method === 'POST' || method === 'PUT' ? data : {},
      params: method === 'GET' ? data : {},
      timeout
    })
      .then((response) => {
        const status = response.status
        const data = response.data
        result = { status, data }
      })
      .catch((error) => {
        console.log('Error at callAxios: ', url, error.message)
        const status = error?.response?.status
        const data = error?.response?.data
        result = { status, data }
      })
    return result
  } catch (error) {
    console.log('Error callAxios', error.message)
    return {
      code: -1,
      message: error.message
    }
  }
}
const formatDateYYYYMMDDHHmmss = function (date) {
  try {
    if (!date) return null
    return moment(date).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss)
  } catch (error) {
    console.error('Error at formatDateYYYYMMDDHHmmss:', error.message)
    return null
  }
}

const getPaginationData = function (arrayData, page = 0, limit = 10) {
  page = Number(page)
  limit = Number(limit)
  if (page == -1) {
    return {
      page,
      limit,
      total: arrayData.length,
      result: arrayData
    }
  }
  const firstIndex = page * limit
  const endIndex = (page + 1) * limit > arrayData.length ? arrayData.length : (page + 1) * limit
  return {
    page,
    limit,
    total: arrayData.length,
    result: arrayData.slice(firstIndex, endIndex)
  }
}

async function fillDataAndCreatePdf(filePath, fillData) {
  try {
    const contractTemplatePath = filePath
    const fileBuffer = fs.readFileSync(contractTemplatePath, 'binary')
    const zip = new PizZip(fileBuffer)
    const doc = new Docxtemplater(zip)
    doc.setData(fillData)

    doc.render()
    const buf = doc.getZip().generate({ type: 'nodebuffer' })

    const buffer = await createPDF(buf)

    return buffer
  } catch (error) {
    console.log(error)
    return null
  }
}

const createPDF = (docBuffer) => {
  return new Promise((resolve, reject) => {
    libre.convert(docBuffer, '.pdf', undefined, (err, done) => {
      if (err) {
        console.log(`Error converting file: ${err}`)
        reject(err)
      }
      console.log('converting file done')
      // fs.writeFileSync('test.pdf', done)
      resolve(done)
    })
  })
}
function getCashString(amount) {
  if (!amount) {
    return ''
  }
  const split = `${Math.abs(amount)}`.split('')
  const arrayLength = split.length

  if (arrayLength <= 3) {
    return `${amount}`
  }
  const numberOfComma = Math.floor((arrayLength - 1) / 3)

  for (let i = 1; i <= numberOfComma; i++) {
    split.splice(arrayLength - 3 * i, 0, ',')
  }
  amount < 0 && split.unshift('-')
  return split.join('')
}

function getDpdRiskGroupByDpd(numDayDpd, totalRemain) {
  if (totalRemain <= 0 || (numDayDpd >= 1 && numDayDpd <= 9)) {
    return 1
  }
  if (numDayDpd >= 10 && numDayDpd <= 90) {
    return 2
  }
  if (numDayDpd >= 91 && numDayDpd <= 180) {
    return 3
  }
  if (numDayDpd >= 181 && numDayDpd <= 360) {
    return 4
  }
  if (numDayDpd >= 361 && totalRemain > 0) {
    return 5
  }
  return 1
}

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
async function retry({ func, funcBeforeRetry, retryCount = 20, retryInterval = 1000, retryBackoffFactor = 1 }) {
  try {
    return await func()
  } catch (e) {
    if (retryCount <= 0) {
      return
    }

    if (e) {
      e.message = `Retrying because of exception: ${e.message}`
    }
    console.log(`[ERROR RETRY] remain time ${retryCount}`, e?.message)

    await sleep(retryInterval * retryBackoffFactor)
    if (funcBeforeRetry) {
      await funcBeforeRetry()
    }
    const response = await retry({
      func,
      funcBeforeRetry,
      retryCount: retryCount - 1,
      retryInterval: retryInterval * retryBackoffFactor,
      retryBackoffFactor
    })
    return response
  }
}

const formatDate = ({ date, format = constant.DATE_FORMAT.YYYYMMDD2 }) => {
  if (!date) {
    return moment().format(format)
  }
  return moment(date).format(format)
}

const genAuthCode = (length = 8) => {
  let authCode = ''
  for (let i = 0; i < length; i++) {
    authCode += getRandomInt(0, 9)
  }
  return authCode
}

/**
 * same as postApi but use async/await
*/
const postAPIAsync = async function (
  link,
  json,
  form,
  header = { 'Content-type': 'application/json' },
  logConfig = { isLog: false }
) {
  const { eventType, isLog, contractNumber } = logConfig;
  const requestTime = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS);
  try {
    const qs = require('qs');
    const dataForm = qs.stringify(form);
    const config = {
      method: 'post',
      url: link,
      headers: header,
      data: json !== undefined ? json : dataForm
    };

    const response = await axios(config);
    await writeLogCallApi({
      api: link,
      eventType: eventType || link,
      contractNumber:
        json?.debtAckContractNumber ||
        json?.debt_ack_contract_number ||
        json?.kunnNumber ||
        json?.contractNumber ||
        json?.ContractNumber ||
        json?.contract_number ||
        contractNumber ||
        '',
      custId: json?.custId || json?.cust_id,
      serviceType: 2,
      method: 'POST',
      reqBody: json || form,
      requestTime,
      resBody: response.data,
      responseCode: response.data.code || 200,
      statusCode: '200',
      reqHeaders: header,
      isLog
    });
    return response.data;
  } catch (error) {
    const responseError = error.response || {};
    await writeLogCallApi({
      api: link,
      eventType: eventType || link,
      contractNumber:
        json?.debtAckContractNumber ||
        json?.debt_ack_contract_number ||
        json?.contractNumber ||
        json?.ContractNumber ||
        json?.contract_number ||
        contractNumber ||
        '',
      custId: json?.custId || json?.cust_id,
      serviceType: 2,
      method: 'POST',
      reqBody: json || form,
      requestTime,
      resBody: responseError.data || error.message,
      responseCode: responseError.data ? responseError.data.code : -1,
      statusCode: responseError.status || '500',
      reqHeaders: header,
      isLog
    });
    console.error(error)
    throw error.message;
  }
};

const getAPIV2Async = async function (link, header, responseType, logConfig = { isLog: false }) {
  const requestTime = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmssSSS);
  const config = {
    method: 'get',
    url: link,
    headers: header
  };
  if (responseType) {
    config.responseType = responseType;
  }
  const { eventType, isLog } = logConfig;

  try {
    const response = await axios(config);
    await writeLogCallApi({
      eventType: eventType || link,
      api: link,
      serviceType: 2,
      method: 'GET',
      requestTime,
      resBody: response.data,
      responseCode: response.data.code || '0',
      statusCode: response.statusCode,
      reqHeaders: header,
      isLog
    });
    return {
      status: response?.status,
      data: response?.data
    };
  } catch (error) {
    if (error.response) {
      return {
        status: error.response.status,
        data: error.response.data
      };
    } else {
      throw error;
    }
  }
};

const isFactoringLoanChannel = (channel) => {
  return [constant.PARTNER_CODE.BIZZ].includes(channel)
}

const isShareRiskGroupCustIdChannel = (channel) => {
  return ![constant.PARTNER_CODE.VUIAPP].includes(channel)
}

module.exports = {
  isShareRiskGroupCustIdChannel,
  fillDataAndCreatePdf,
  setServiceName,
  log,
  logError,
  postAPI,
  postApiV2,
  getAPI,
  getAPIV2,
  convertDatetoString,
  makeId,
  putAPI,
  calNextCycleDate,
  roundUp,
  round,
  calCycleDateOfMonth,
  addMonths,
  calNextCycleDate2,
  dateDiff,
  addDays,
  getDayOfMonth,
  isEndOfMonth,
  convertCurrency,
  countMonth31Day,
  getRandomInt,
  getDifferenceInDays,
  getDifferencesDays,
  calNextCycleDateV3,
  getContractTypeByLoanAcc,
  callAxios,
  formatDateYYYYMMDDHHmmss,
  getPaginationData,
  getCashString,
  roundV2,
  getDpdRiskGroupByDpd,
  getFirstNextTenor,
  retry,
  sleep,
  formatDate,
  genAuthCode,
  postAPIAsync,
  getAPIV2Async,
  isFactoringLoanChannel,
}
