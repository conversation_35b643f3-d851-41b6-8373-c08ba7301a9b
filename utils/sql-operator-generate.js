class SQLOperatorGenerator {
  constructor(condition) {
    this.condition = condition
  }

  setStrategy(value) {
    if (value instanceof BaseOperatorStrategy) {
      this.strategy = value
    } else {
      this.strategy = new EqualOperatorStrategy(value)
    }
  }

  generateQueryWhere(key) {
    const query = this.strategy.generateQuery(key, this.condition)
    this.condition.listQueryWhere.push(query)
    return query
  }

  generateQuerySet(key) {
    const query = this.strategy.generateQuery(key, this.condition)
    this.condition.listQuerySet.push(query)
    return query
  }

  generateQueryOrder(key) {
    const query = this.strategy.generateQuery(key, this.condition)
    this.condition.listQuerySet.push(query)
    return query
  }

  genAllQueryWhere() {
    if (this.condition.listQueryWhere.length) {
      return 'where ' + this.condition.listQueryWhere.join(' and ')
    }
    return ''
  }

  genAllQuerySet() {
    if (this.condition.listQuerySet.length) {
      return 'set ' + this.condition.listQuerySet.join(', ')
    }
    return ''
  }

  genAllQueryOrder(queryOrderObj) {
    const keyValueArray = Object.entries(queryOrderObj)
    if (!keyValueArray.length) {
      return ''
    }
    const listQuery = []
    for (const [key, value] of keyValueArray) {
      listQuery.push(`${key} ${value}`)
    }
    return 'order by ' + listQuery.join(', ')
  }

  genQueryOffset(offset) {
    if (offset) {
      return `offset ${offset}`
    }
    return ''
  }

  genQueryLimit(limit) {
    if (limit) {
      return `limit ${limit}`
    }
    return ''
  }
}

class BaseOperatorStrategy {
  constructor(value, operatorType) {
    this.value = value
    this.operatorType = operatorType
  }

  generateQuery(key, condition) {
    return ''
  }
}
class EqualOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(value, '=')
  }

  generateQuery(key, condition) {
    condition.index += 1
    condition.parameters.push(this.value)
    return `${key} = $${condition.index}`
  }
}

class NormalOperatorStrategy extends BaseOperatorStrategy {
  generateQuery(key, condition) {
    condition.index += 1
    condition.parameters.push(this.value)
    return `${key} ${this.operatorType} $${condition.index}`
  }
}

class InOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(value, 'IN')
  }

  generateQuery(key, condition) {
    const queryString = this.value
      .map((item) => {
        condition.index += 1
        condition.parameters.push(item)
        return `$${condition.index}`
      })
      .join(',')
    return `${key} IN (${queryString})`
  }
}

class BetweenOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(value, 'BETWEEN')
  }

  generateQuery(key, condition) {
    const index = condition.index
    condition.index += 2
    condition.parameters = [...condition.parameters, this.value[0], this.value[1]]
    return `${key} BETWEEN $${index + 1} and $${index + 2}`
  }
}

class IsNullOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(value, 'IS_NULL')
  }

  generateQuery(key, condition) {
    return `${key} is null`
  }
}

class IsNotNullOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(value, 'NOT_NULL')
  }

  generateQuery(key, condition) {
    return `${key} is not null`
  }
}

class LikeOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(`%${value}%`, 'LIKE')
  }

  generateQuery(key, condition) {
    condition.index += 1
    condition.parameters.push(this.value)
    return `${key} LIKE $${condition.index}`
  }
}

class ILikeOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(`%${value}%`, 'ILIKE')
  }

  generateQuery(key, condition) {
    condition.index += 1
    condition.parameters.push(this.value)
    return `${key} ILIKE $${condition.index}`
  }
}

class AddOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(value, '+')
  }

  generateQuery(key, condition) {
    condition.index += 1
    condition.parameters.push(this.value)
    return `${key} = ${key} + $${condition.index}`
  }
}

class SubtractOperatorStrategy extends BaseOperatorStrategy {
  constructor(value) {
    super(value, '-')
  }

  generateQuery(key, condition) {
    condition.index += 1
    condition.parameters.push(this.value)
    return `${key} = ${key} - $${condition.index}`
  }
}

module.exports = {
  SQLOperatorGenerator,
  InOperatorStrategy,
  EqualOperatorStrategy,
  BetweenOperatorStrategy,
  NormalOperatorStrategy,
  IsNullOperatorStrategy,
  IsNotNullOperatorStrategy,
  LikeOperatorStrategy,
  ILikeOperatorStrategy,
  AddOperatorStrategy,
  SubtractOperatorStrategy
}
