const tenorConfigRepo = require('../repositories/tenor-config-repo')
const holidayRepo = require('../repositories/holiday-repo')
const earlyTerminationFeeConfigRepo = require('../repositories/early-termination-fee-config-repo')
const configurationService = require('../other-services/configuration-service')
const common = require('./common')
require('dotenv').config({ path: `./.env.${process.env.NODE_ENV}` })
const Pool = require('pg').Pool

async function loadConfigData() {
  global.tenorConfig = await tenorConfigRepo.getListTenorConfig()
  global.listHoliday = await holidayRepo.getListHoliday()
  global.listETFeeConfig = await earlyTerminationFeeConfigRepo.getListETFeeConfig()
}

async function loadConfigService() {
  const serviceName = 'mclms'
  const env = process.env.HOST_ENV
  common.setServiceName(serviceName)
  const configurationServiceLink = `${process.env.HOST_CONFIGURATION}/services?service=` + serviceName

  try {
    const data = await common.getAPI(configurationServiceLink, { 'Content-Type': 'application/json' })
    if (!data) {
      common.log('Khong lay duoc cau hinh:')
    } else {
      // Lay duoc cau hinh
      const config = data
      global.config = config
      global.env = env
      // Ket noi database
      const poolWrite = new Pool({
        database: process.env.DB_DATABASE,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        port: process.env.DB_PORT,
        host: process.env.DB_HOST_WRITE
      })
      const poolRead = new Pool({
        database: process.env.DB_DATABASE,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        port: process.env.DB_PORT,
        host: process.env.DB_HOST_READ
      })
      global.poolWrite = poolWrite
      global.poolRead = poolRead
      global.calcuCfg = {
        totalDayOfYear: 365,
        scale: -3,
        scaleOfDay: 2,
        penalitiesRateAnnex: 0,
        defaultFee: 12000,
        minAnnex: 100000,
        futureDateRePayment: 5
      }
      global.ownerId = 1
      global.createdBy = 'system'
      global.isTesting = 0
      global.createdUser = 1
      global.aaaServiceLink = config.basic.aaa[env] + config.basic.aaa.service
      global.crmServiceLink = config.basic.crmService[env] + config.basic.crm.service
      global.bssEmailUrl = config.basic.bssEmailService[env] + config.basic.bssEmailService.service
      global.losServiceUrl = config.basic.losMcCredit[env] + config.basic.losMcCredit.service
      global.bssEsigningUrl = config.basic['bss-esigning-service'][env]
      global.losUnitedServiceUrl = config.basic.losUnited[env] + '/los-united/v1'
      global.voucherServiceUrl = config.basic.voucherService[env] + '/voucher-service/v1'
      global.voucherMobileBeUrl = config.basic.voucherMobileBe[env] + '/voucher-mobile-be/v1'
      global.disbursementUrl = config.basic.disbursement[env]
      global.crmCfg = config.data.crm
      global.disbursementCfg = config.data.disbursement
      global.losCfg = config.data.los
      global.listCashLoanPartnerCode = config.data.cashloan.listPartnerCode.split(',')
      global.ruleAllocate = []
      global.repaymentMemCache = {}
      for (const typeAllocate in config.data.ruleAllocate) {
        const configDateAllocate = config.data.ruleAllocate[typeAllocate].split(',')
        global.ruleAllocate.push({
          typeAllocate,
          startDate: configDateAllocate[0],
          endDate: configDateAllocate[1]
        })
      }
      const connectOptions = {
        host: config.data.queue.host,
        port: config.data.queue.port,
        resetDisconnect: false,
        connectHeaders: {
          login: config.data.queue.login,
          passcode: config.data.queue.passcode,
          'heart-beat': config.data.queue.heart_beat
        },
        ssl: false
      }
      global.connectMQueue = connectOptions
      common
        .getAPI(config.basic.masterData[env] + '/masterdata/v1/case-status' + '?codeType=' + 'STATUS_CASE_ECL', {})
        .then((dataStatusECL) => {
          console.log('Load config audit success')
          global.configActionAudit = dataStatusECL.data
        })
      common.log('Load cau hinh thanh cong')
    }
  } catch (error) {
    common.log('Khong lay duoc cau hinh:')
  }
}

module.exports = {
  loadConfigData,
  loadConfigService
}
