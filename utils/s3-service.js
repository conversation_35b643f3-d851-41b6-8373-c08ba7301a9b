const AWS = require('aws-sdk')
const moment = require('moment-timezone')
moment().tz('Asia/Ho_Chi_Minh').format()

const upload = (fileName, buffer, prefixDocIdPath) => {
  return new Promise((resolve, reject) => {
    try {
      const config = global.config?.data

      AWS.config.update(config.aws)
      const bucketName = config.awsS3.bucketName
      const s3 = new AWS.S3({ region: config.awsS3.region })
      const acl = 'public-read'

      console.log(`Start upload: ${fileName}`)

      const param = { Bucket: bucketName, ACL: acl }
      param.Key = prefixDocIdPath.replace('/', '') + '/' + moment().format('YYYYMMDD') + '/' + fileName

      console.log('param.Key', param.Key)

      param.Body = buffer
      s3.upload(param, {}, async (err, data) => {
        if (err) {
          reject(err)
        }
        console.log('URL S3: ', data.Location)
        resolve(data)
      })
    } catch (error) {
      console.log(error)
    }
  })
}

const download = (key) => {
  return new Promise((resolve, reject) => {
    // console.log(config)
    const config = global.config?.data
    AWS.config.update(config.aws)
    const bucketName = config.awsS3.bucketName
    const s3 = new AWS.S3({ region: config.awsS3.region })
    // const acl = 'public-read'

    console.log('Downloading...', key)
    const param = { Bucket: bucketName, Key: key }

    // console.log(param)
    try {
      s3.getObject(param, (err, data) => {
        if (err) {
          console.log(err)
          reject(err)
        }
        resolve(data)
      })
    } catch (error) {
      console.log('Cannot get file from: ', bucketName, error)
      reject(error)
    }
  })
}

module.exports = {
  upload,
  download
}
