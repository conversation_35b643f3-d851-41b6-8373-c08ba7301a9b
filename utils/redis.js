const { createClient } = require('redis')
const { retry } = require('./common')

const redisClient = createClient({
  socket: {
    host: '***********',
    port: '6379'
  }
})
async function initialize() {
  try {
    await redisClient.connect()
    console.log('Connect redis success!')
  } catch (e) {
    console.log('Connect redis fail!')
  }
}
async function getCache(key) {
  return redisClient.get(key)
}
async function checkCacheField(field) {
  const res = await retry({
    func: () => redisClient.sIsMember('repayment_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
  return res
}
async function setCacheField(field) {
  await retry({
    func: () => redisClient.sAdd('repayment_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
}
async function clearCacheField(field) {
  await retry({
    func: () => redisClient.sRem('repayment_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
}

async function checkCacheFieldSuspend(field) {
  const res = await retry({
    func: () => redisClient.sIsMember('repayment_suspend_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
  return res
}
async function setCacheFieldSuspend(field) {
  await retry({
    func: () => redisClient.sAdd('repayment_suspend_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
}
async function clearCacheFieldSuspend(field) {
  await retry({
    func: () => redisClient.sRem('repayment_suspend_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
}
async function checkCacheFieldRefund(field) {
  const res = await retry({
    func: () => redisClient.sIsMember('repayment_refund_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
  return res
}
async function setCacheFieldRefund(field) {
  await retry({
    func: () => redisClient.sAdd('repayment_refund_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
}
async function clearCacheFieldRefund(field) {
  await retry({
    func: () => redisClient.sRem('repayment_refund_cache_mclms', field),
    funcBeforeRetry: () => redisClient.connect(),
    retryCount: 5,
    retryInterval: 1000,
    retryBackoffFactor: 2
  })
}
module.exports = {
  initialize,
  getCache,
  checkCacheField,
  setCacheField,
  clearCacheField,
  checkCacheFieldSuspend,
  setCacheFieldSuspend,
  clearCacheFieldSuspend,
  checkCacheFieldRefund,
  setCacheFieldRefund,
  clearCacheFieldRefund,
}
