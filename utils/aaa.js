/**
 * <PERSON><PERSON><PERSON> tích hợp AAA service.
 *
 */
const common = require('./common')

async function authenticate(request, response, next) {
  const url = global.aaaServiceLink + global.config.data.aaa.authorService

  const token = request.headers.token
  const uiid = request.headers.uiid
  console.log('service ', request.baseUrl + request.route.path)
  console.log('token ', token)
  console.log('uiid ', uiid)

  if (isEmty(token) || isEmty(uiid)) {
    common.log('Invalid token')
    response.status(401).json((response.body = { message: 'Unauthorize request' }))
    return
  }

  try {
    common
      .getAPI(url, {
        uiid,
        token,
        // 'service': request.baseUrl
        service: request.baseUrl + request.route.path
      })
      .then((data) => {
        console.log('Data when query author service ', data)
        if (data.status !== 1) {
          response.status(403).json((response.body = { message: 'Forbidden request' }))
          return
        }
        // lay user tu token truyen
        request.createdBy = data.user.username
        request.createdUser = data.user.username

        return next()
      })
  } catch (err) {
    common.log('Authorization failed ' + err)
    response.status(401).json((response.body = { message: 'Unauthorize request' }))
  }
}

function isEmty(val) {
  // console.log('Val ', val);
  if (val === undefined) {
    return true
  }
  return false
}
async function authenticateOauth2(req, res, next) {
  const url = global.aaaServiceLink + '/oauth2/authenticate'

  const authorization = req.headers.authorization

  // check for bearer auth header
  if (!authorization || authorization.indexOf('Bearer ') === -1) {
    return res.status(401).json({ message: 'Missing Authorization Header' })
  }

  try {
    const headers = {
      authorization
    }
    return common
      .callAxios(url, {}, 'GET', headers)
      .then((data) => {
        // console.log(data);
        if (data.status === 401 || data.code === 401) {
          res.status(403).json({ message: 'Forbidden request' })
          return
        }
        return next()
      })
      .catch((err) => {
        common.log('Authorization failed ' + err.message)
        res.status(401).json({ code: 401, message: 'Request unauthorized.' })
      })
  } catch (err) {
    common.log('Authorization failed ' + err.message)
    res.status(401).json({ message: 'Unauthorize request' })
  }
}

async function authenInternal(request, response, next) {
  const url = global.config.basic.aaa[global.env] + '/aaa/v1/sale/authenticate'
  console.log('url', url)
  try {
    common
      .getAPI(url, {
        service: 'REPAYMENT',
        check_token: 0
      })
      .then((data) => {
        if (data.statusCode == '401' && data.isConfig == '1') {
          return response.status(403).json({ message: 'Forbidden request' })
        } else if (data.statusCode == '401') {
          return response.status(403).json({ message: 'Forbidden request' })
        } else if (data.statusCode == '200') {
          return next()
        } else {
          return response.status(401).json({ message: 'Unauthorize request' })
        }
      })
  } catch (err) {
    common.log('Authorization failed ' + err)
    response.status(401).json({ message: 'Unauthorize request' })
  }
}
module.exports = {
  authenticate,
  authenticateOauth2,
  authenInternal
}
