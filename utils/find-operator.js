const {
  InOperatorStrategy,
  BetweenOperatorStrategy,
  NormalOperatorStrategy,
  IsNotNullOperatorStrategy,
  IsNullOperatorStrategy,
  LikeOperatorStrategy,
  ILikeOperatorStrategy
} = require('./sql-operator-generate')

function In(value = []) {
  return new InOperatorStrategy(value)
}
function Between(value = []) {
  return new BetweenOperatorStrategy(value)
}
function Equal(value) {
  return new NormalOperatorStrategy(value, '=')
}
function MoreThan(value) {
  return new NormalOperatorStrategy(value, '>')
}
function MoreThanOrEqual(value) {
  return new NormalOperatorStrategy(value, '>=')
}
function LessThan(value) {
  return new NormalOperatorStrategy(value, '<')
}
function LessThanOrEqual(value) {
  return new NormalOperatorStrategy(value, '<=')
}

function DifferentFrom(value) {
  return new NormalOperatorStrategy(value, '<>')
}
function IsNull() {
  return new IsNullOperatorStrategy()
}
function IsNotNull() {
  return new IsNotNullOperatorStrategy()
}
function Like(value) {
  return new LikeOperatorStrategy(value)
}
function ILike(value) {
  return new ILikeOperatorStrategy(value)
}

module.exports = {
  In,
  Between,
  MoreThan,
  MoreThanOrEqual,
  LessThan,
  LessThanOrEqual,
  Equal,
  DifferentFrom,
  IsNull,
  IsNotNull,
  Like,
  ILike
}
