ALTER TABLE payment_allocate ADD start_time timestamp NULL;

ALTER TABLE payment_allocate ADD end_time timestamp NULL;

INSERT INTO payment_allocate (product_code,mc_limit_id,code,"name","type",value,idx,status,owner_id,is_testing,created_date,updated_date,created_by,start_time,end_time) VALUES
	 ('',NULL,'PAY_ALLO_NOT_EXPIRE','Thu nợ khi KUNN chưa quá hạn',2,'LPT',1,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',1,'Nợ gốc',1,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_NOT_EXPIRE','Thu nợ khi KUNN chưa quá hạn',1,'Nợ gốc',2,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',2,'LPT',2,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',5,'Các khoản phí/phạt',3,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_NOT_EXPIRE','Thu nợ khi KUNN chưa quá hạn',5,'Các khoản phí/phạt',3,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_NOT_EXPIRE','Thu nợ khi KUNN chưa quá hạn',6,'Phí phạt tất toán',4,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',3,'LPT chậm trả lãi của gốc',4,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',4,'LPT chậm trả lãi của lãi',5,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2018-01-01 00:00:00','2023-09-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',1,'Nợ gốc',1,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00');
INSERT INTO payment_allocate (product_code,mc_limit_id,code,"name","type",value,idx,status,owner_id,is_testing,created_date,updated_date,created_by,start_time,end_time) VALUES
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',2,'LPT',2,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_NOT_EXPIRE','Thu nợ khi KUNN chưa quá hạn',2,'LPT',11,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',5,'Các khoản phí/phạt',5,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',3,'LPT chậm trả lãi của gốc',3,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_EXPIRE','Thu nợ khi KUNN quá hạn',4,'LPT chậm trả lãi của lãi',4,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_NOT_EXPIRE','Thu nợ khi KUNN chưa quá hạn',1,'Nợ gốc',12,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_NOT_EXPIRE','Thu nợ khi KUNN chưa quá hạn',5,'Các khoản phí/phạt',15,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00'),
	 ('',NULL,'PAY_ALLO_NOT_EXPIRE','Thu nợ khi KUNN chưa quá hạn',6,'Phí phạt tất toán',16,1,1,0,'2021-04-22 14:52:55.683623','2021-04-22 14:52:55.683623','system','2023-09-01 00:00:00','2099-01-01 00:00:00');
