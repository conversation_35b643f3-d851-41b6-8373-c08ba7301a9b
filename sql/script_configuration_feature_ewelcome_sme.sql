-- Auto-generated SQL script #202207201705
INSERT INTO public."configuration" (service,param_group,param_name,value,data_type,is_deleted)
	VALUES ('mclms','aws','accessKeyId','********************','',0);
INSERT INTO public."configuration" (service,param_group,param_name,value,data_type,is_deleted)
	VALUES ('mclms','aws','secretAccessKey','RrmKxXkr1Y38N/aIqSlAw+gFF1Uez4EnWfeZ5m2t','',0);
INSERT INTO public."configuration" (service,param_group,param_name,value,data_type,is_deleted)
	VALUES ('mclms','awsS3','region','ap-southeast-1','',0);
INSERT INTO public."configuration" (service,param_group,param_name,value,data_type,is_deleted)
	VALUES ('mclms','awsS3','bucketName','ms-los-ap-southeast-1-446567516155-document','',0);
INSERT INTO public."configuration" (service,param_group,param_name,value,data_type,is_deleted)
	VALUES ('mclms','awsS3','url','https://ms-los-ap-southeast-1-446567516155-document.s3.ap-southeast-1.amazonaws.com','',0);
