CREATE TABLE insurance (
	insur_id serial4 NOT NULL,
	loan_id varchar NULL,
	contract_number varchar(100) NULL,
	debt_ack_contract_number varchar(100) NULL,
	amort_id int4 NULL,
	insur_id_ctlg int4 NULL,
	insur_name varchar(100) NULL,
	insur_type bpchar(1) NULL,
	insur_amt numeric(20, 3) NULL,
	is_invoiced bpchar(1) NULL,
	start_date timestamp NULL,
	end_date timestamp NULL,
	flag_active bpchar(1) NULL,
	created_date timestamp NULL DEFAULT now(),
	updated_date timestamp NULL,
	created_user varchar(100) NULL,
	owner_id int4 NULL DEFAULT 1,
	is_delete bpchar(1) NULL,
	is_testing int4 NULL DEFAULT 0,
	CONSTRAINT "PK_INSURANCE" PRIMARY KEY (insur_id)
);
CREATE TABLE insurance_detail (
	id serial4 NOT NULL,
	contract_number varchar(100) NULL,
	debt_ack_contract_number varchar(100) NULL,
	cust_id varchar(100) NULL,
	insur_id_ctlg int4 NULL,
	id_tras varchar(100) NULL,
	gcn varchar(100) NULL,
	so_id varchar(100) NULL,
	phi varchar(100) NULL,
	file varchar(200) NULL,
	insur_partner varchar(100) NULL,
	xml_input varchar NULL,
	created_date timestamp NULL DEFAULT now(),
	updated_date timestamp NULL,
	created_user varchar(100) NULL,
	owner_id int4 NULL DEFAULT 1,
	is_delete bpchar(1) NULL DEFAULT 0,
	xml_output varchar NULL,
	CONSTRAINT "PK_INSURANCE_DETAIL" PRIMARY KEY (id)
);