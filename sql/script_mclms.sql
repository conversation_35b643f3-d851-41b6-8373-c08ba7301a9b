ALTER TABLE payment ADD pay_type varchar(100) NULL;

ALTER TABLE ir_charge  ADD tenor_from int4 NULL;

ALTER TABLE ir_charge ADD tenor_to int4 NULL;

ALTER TABLE ir_charge ADD instal_from int4 NULL;

ALTER TABLE ir_charge ADD instal_to int4 NULL;

ALTER TABLE installment ADD outstanding_prin numeric(20, 3) NULL;

ALTER TABLE installment ADD ir_rate numeric(10, 3) NULL;

ALTER TABLE installment ADD description varchar(100) NULL;

ALTER TABLE loan_annex ADD penalty_rate numeric(10, 3) NULL;

ALTER TABLE loan_annex ADD total_amt numeric(20, 3) NULL;


UPDATE ir_charge
SET status = 1
where status = 0;