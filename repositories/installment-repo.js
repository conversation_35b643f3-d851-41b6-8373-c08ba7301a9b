const format = require('pg-format')
const { INSTALLMENT, CALCUCFG, DEBT_ACK_STATUS } = require('../utils/constant')
const baseRepo = require('./base.repo')
const constant = require("../utils/constant");
const moment = require("moment/moment");

const tableName = 'installment'

const insBatchInstallment = async function (records) {
  const sql = format(
    `INSERT INTO installment
    (debt_ack_contract_id, num_cycle, amount, remain_amount, cycle_date, "type", payment_status, owner_id, is_testing, created_by, contract_number
    ,debt_ack_contract_number,start_date,end_date,due_date,of_num_cycle,lpi_at_cycle,ir_num_cycle,outstanding_prin,description,ir_rate,amort_id,payment_priority)
    VALUES %L returning *`,
    records
  )
  return await global.poolWrite.query(sql)
}
const insBatchInstallmentLpi = async function (records) {
  const sql = format(
    `INSERT INTO installment
    (debt_ack_contract_id, num_cycle, amount, remain_amount, cycle_date, "type", payment_status, owner_id, is_testing, created_by, contract_number
    ,debt_ack_contract_number,start_date,end_date,due_date,of_num_cycle,lpi_at_cycle,ir_num_cycle,closed,origin_amt,ir_from_date,ir_to_date,ir_on_prin,payment_priority)
    VALUES %L returning *`,
    records
  )
  return await global.poolWrite.query(sql)
}

const insertInsmAnnexV2 = async function (payload) {
  try {
    if (payload.amount == 0) {
      return undefined
    }
    const result = await baseRepo.baseInsert({
      tableName,
      fieldsValueObj: {
        debt_ack_contract_id: payload.debtAckContractId || payload.debt_ack_contract_id,
        num_cycle: payload.numCycle || payload.num_cycle,
        amount: payload.amount || 0,
        remain_amount: payload.remainAmount || payload.remain_amount || 0,
        cycle_date: payload.cycleDate || payload.cycle_date,
        type: payload.type || payload.type,
        payment_status: payload.paymentStatus || payload.payment_status,
        owner_id: payload.ownerId || payload.owner_id,
        is_testing: payload.isTesting || payload.is_testing,
        created_by: payload.createdBy || payload.created_by,
        contract_number: payload.contractNumber || payload.contract_number,
        debt_ack_contract_number: payload.debtAckContractNumber || payload.debt_ack_contract_number,
        start_date: payload.startDate || payload.start_date,
        end_date: payload.endDate || payload.end_date,
        due_date: payload.dueDate || payload.due_date,
        ir_num_cycle: payload.irNumCycle || payload.ir_num_cycle,
        is_annex: payload.isAnnex || payload.is_annex,
        ir_from_date: payload.irFromDate || payload.ir_from_date,
        ir_to_date: payload.irToDate || payload.ir_to_date,
        ir_on_prin: payload.irOnPrin || payload.ir_on_prin,
        status: payload.status || payload.status,
        closed: payload.closed || payload.closed,
        of_num_cycle: payload.ofNumCycle || payload.of_num_cycle,
        outstanding_prin: payload.outstandingPrin || payload.outstanding_prin,
        description: payload.description || payload.description,
        ir_rate: payload.irRate || payload.ir_rate,
        amort_id: payload.amortId || payload.amort_id,
        payment_priority: payload.paymentPriority || payload.payment_priority
      },
      isReturn: true
    })
    return result.rows[0]
  } catch (error) {
    console.log('error insert insm', error)
  }
}

const insertBatchInsmAnnexV2 = async function (arrayFieldsValueObj) {
  try {
    if (!arrayFieldsValueObj.length) {
      console.log('insertBatchInsmAnnexV2 data empty')
      return
    }
    return await baseRepo.insertMany({
      tableName,
      arrayFieldsValueObj
    })
  } catch (error) {
    console.log('ERROR insertBatchInsmAnnexV2', error)
    throw error
  }
}
const findByContractNumberAndPaymentStatus = async function (poolRead, payload) {
  const sql = 'SELECT *  FROM installment where contract_number = $1 and payment_status = $2 order by num_cycle'
  return await poolRead.query(sql, [payload.contractNumber, payload.paymentStatus])
}
const findByContractNumberAndPaymentStatusNotCompleted = async function (poolRead, payload) {
  let sql = 'SELECT * FROM installment where payment_status <> 0 and status = 1'
  const params = []
  let idx = 1
  if (payload.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(payload.contractNumber)
    idx++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  sql += 'order by num_cycle,end_date'
  return await poolRead.query(sql, params)
}
const updatePaymentStatusAndRemainAmt = async function (poolWrite, payload) {
  const sql =
    'UPDATE installment SET remain_amount = $1, payment_status = $2, origin_amt = $4, updated_date = now() WHERE id = $3'
  return await poolWrite.query(sql, [
    payload.remainPrinAmount,
    payload.paymentStatus,
    payload.installmentId,
    payload.originAmt
  ])
}
const updateInsmRemainAmt = async function (poolWrite, payload) {
  const sql =
    'UPDATE installment SET remain_amount = remain_amount - $1, payment_status = $2 ,updated_date = now() WHERE id = $3'
  return await poolWrite.query(sql, [payload.remainPrinAmount, payload.paymentStatus, payload.installmentId])
}
const updateRemainAmtByDebtAckContractNumberAndNumCycleAndType = async function (poolWrite, payload) {
  let sql = `UPDATE installment SET remain_amount = remain_amount + $1, amount = amount + $1, origin_amt = origin_amt + $6, ir_to_date = $7, payment_status = 1, updated_date = now() 
    WHERE debt_ack_contract_number = $2 and type = $3 and lpi_at_cycle = $4`
  const params = [
    payload.remainAmount,
    payload.debtAckContractNumber,
    payload.type,
    payload.lpiAtCycle,
    payload.numCycleOfType,
    payload.originAmt,
    payload.irToDate
  ]
  const idx = 8
  if (payload.type == 3) {
    sql += ' and num_cycle = $5 '
  }
  if (payload.type == 4) {
    sql += ' and ir_num_cycle = $5 '
  }
  if (payload.installmentId) {
    sql += ` and id = $${idx}`
    params.push(payload.installmentId)
  }
  sql += ' returning *'
  return await poolWrite.query(sql, params)
}
const findByDebtAckContractNumberAndPaymentStatusNotCompleted = async function (payload) {
  const sql =
    'SELECT * FROM installment where debt_ack_contract_number = $1 and payment_status <> 0 and status = 1 order by num_cycle,end_date'
  return await global.poolWrite.query(sql, [payload.debtAckContractNumber])
}
const findByDebtAckContractNumber = async function (poolRead, payload) {
  const sql = 'SELECT *  FROM installment where debt_ack_contract_number = $1 order by num_cycle'
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}
const findByDebtAckContractNumberMQueue = async function (poolRead, payload, isType = false) {
  let sql = 'SELECT *  FROM installment where debt_ack_contract_number = $1 '
  if (isType) {
    sql += ' and "type" = 3 '
  }
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}
const findByDebtAckContractNumberForResendQueue = async function (poolRead, payload, isType = false) {
  let sql = 'SELECT *  FROM installment where debt_ack_contract_number = $1 '
  if (isType) {
    sql += ' and "type" = 3'
  }
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}
const findById = async function (poolRead, payload) {
  const sql = 'SELECT *  FROM installment where id = $1'
  return await poolRead.query(sql, [payload.installmentId])
}

const getMaxIrNumCycle = async function (poolRead, payload) {
  const sql =
    'SELECT ir_num_cycle  FROM installment where status = 1 and ir_num_cycle is not null  and debt_ack_contract_number = $1 order by ir_num_cycle desc'
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}

const getCurrentIrNumCycle = async function (poolRead, payload, currentDate = new Date()) {
  const sql =
    'SELECT ir_num_cycle  FROM installment where status = 1 and "type" = 2 and start_date < $2::date and end_date >= $2::date and debt_ack_contract_number = $1;'
  return await poolRead.query(sql, [payload.debtAckContractNumber, currentDate])
}

const getSumAmount = async function (poolRead, payload, type, currentDate = new Date()) {
  const sql = `SELECT sum(remain_amount) FROM installment where status = 1 and "type" in (${type}) and start_date < $2::date and end_date >= $2::date and debt_ack_contract_number = $1;`
  return await poolRead.query(sql, [payload.debtAckContractNumber, currentDate])
}


const getSumAmountV2 = async function (poolRead, payload, type, currentDate = new Date()) {
  const sql = `SELECT sum(remain_amount) FROM installment where closed = '0' and status = 1 and "type" in (${type}) and start_date <= $2::date and end_date >= $2::date and debt_ack_contract_number = $1;`
  return await poolRead.query(sql, [payload.debtAckContractNumber, currentDate])
}

const getByDebtAckContractAndTypeAndNumCycleAndOfNumCycle = async function (poolRead, payload) {
  let sql = `select * from installment i where debt_ack_contract_number = $1 and status = 1 and type = $2 
    and lpi_at_cycle  = $3`

  if (payload.type == 3) {
    sql += ' and num_cycle = $4 '
  }
  if (payload.type == 4) {
    sql += ' and ir_num_cycle = $4 '
  }
  return await poolRead.query(sql, [
    payload.debtAckContractNumber,
    payload.type,
    payload.lpiAtCycle,
    payload.numCycleOfType
  ])
}
const getInsIsNotClosed = async function (poolRead, payload) {
  let sql = 'SELECT *  FROM installment where closed = 0 and status = 1 and type in (1,2,5)'
  let idx = 1
  const params = []
  if (payload.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(payload.contractNumber)
    idx++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  if (payload.endDate != undefined) {
    sql += ' and end_date = $' + idx
    params.push(payload.endDate)
    idx++
  }
  sql += 'order by ir_num_cycle'
  return await poolRead.query(sql, params)
}

const getInsIsNotClosedFactoring = async function (poolRead, payload) {
  let yesterday = moment(payload.endDate).subtract(1, 'day').format(constant.DATE_FORMAT.YYYYMMDD2)
  let sql = 'SELECT *  FROM installment where closed = 0 and status = 1 ' +
      'and contract_number = $1 and debt_ack_contract_number = $2 and (' +
      '(type in (1) and start_date = $3) or (type in (2,5) and end_date <= $4)' +
      ')'
  
  const params = [
    payload.contractNumber,
    payload.debtAckContractNumber,
    yesterday,
    payload.endDate,
  ]
  sql += 'order by ir_num_cycle'
  return await poolRead.query(sql, params)
}

const getInstallmentNotClosed = async function (payload) {
  let sql = 'SELECT *  FROM installment where closed = 0 and status = 1 and type in (1,2,5)'
  let idx = 1
  const params = []
  if (payload.debtAckContractNumber) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  if (payload.endDate) {
    sql += ' and end_date <= $' + idx
    params.push(payload.endDate)
    idx++
  }
  sql += 'order by ir_num_cycle'
  return await global.poolRead.query(sql, params)
}
const getInstallmentNotClosedFactoring = async function (payload) {
  let yesterday = moment(payload.endDate).subtract(1, 'day').format(constant.DATE_FORMAT.YYYYMMDD2)
  let sql = 'SELECT *  FROM installment where closed = 0 and status = 1 ' +
      'and debt_ack_contract_number = $1 and ( ' +
      '(type in (1) and start_date = $2) or (type in (2,5,7,8) and end_date <= $3)' +
      ')'
  const params = [
      payload.debtAckContractNumber, yesterday, payload.endDate
  ]
 
  sql += 'order by ir_num_cycle'
  return await global.poolRead.query(sql, params)
}
const updateInsClosed = async function (poolWrite, ids) {
  let sql = 'UPDATE installment SET closed = 1, updated_date = now() WHERE id in ( '
  let idx = 1
  const params = []
  for (const i in ids) {
    sql += '$' + idx + ' ,'
    idx++
    params.push(ids[i])
  }
  sql = sql.substring(0, sql.length - 1) + ')'
  return await poolWrite.query(sql, params)
}
const findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompleted = async function (poolRead, payload) {
  const sql = `SELECT * FROM installment where debt_ack_contract_number = $1 and payment_status <> 0
     and status = 1 and closed = 0 order by start_date`
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}

const findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2 = async function ({
  debtAckContractNumber,
  closed,
  isNotCompleted = true
}) {
  const queryWhereObj = {
    debt_ack_contract_number: debtAckContractNumber,
    status: INSTALLMENT.STATUS.ACTIVE
  }
  if (closed != null) {
    queryWhereObj.closed = closed
  }
  if (isNotCompleted) {
    queryWhereObj['not payment_status'] = INSTALLMENT.PAYMENT_STATUS.DONE
  }
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj,
    queryOrderArr: ['start_date', 'asc', 'type', 'asc']
  })
}
const findInstallmentIsAnnex = async function (debtAckContractNumber) {
  const result = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      'not payment_status': INSTALLMENT.PAYMENT_STATUS.DONE,
      status: INSTALLMENT.STATUS.DEACTIVE,
      closed: INSTALLMENT.CLOSE.FALSE,
      is_annex: INSTALLMENT.IS_ANNEX.TRUE
    },
    queryOrderArr: ['num_cycle', 'desc'],
    limit: 1
  })
  if (result.length) {
    return result[0]
  }
  return null
}

const findListActiveInstallment = async function (payload) {
  const sql =
    'SELECT * FROM installment where debt_ack_contract_number = $1 and status = 1 and due_date <= $2 order by start_date asc'
  const result = await global.poolRead.query(sql, [payload.debtAckContractNumber, payload.simulationDate])

  return result.rows
}

const getInsmInfo = async function (poolRead, payload) {
  const sql = `select i.*, la.tenor, la.prin_amt from installment i 
    join loan_account la on la.debt_ack_contract_number = i.debt_ack_contract_number 
    where i.debt_ack_contract_number = $1 and i.status = 1 
    order by i.start_date`
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}
const updateStatusByDebtAckContract = async function (poolWrite, pl) {
  const sql =
    'UPDATE installment SET status = $1, annex_number = $4, updated_date = now() WHERE debt_ack_contract_number = $2 and status =$3 returning *'
  const res = await poolWrite.query(sql, [pl.newStatus, pl.debtAckContractNumber, pl.status, pl.annexNumber])
  return res.rows
}
const updateStatusByDebtAckContractAndPaymentNotClt = async function (pl) {
  let sql =
    'UPDATE installment SET status = $1, annex_number = $5, updated_date = now() WHERE debt_ack_contract_number = $2 and status = $3 and closed = $4 '
  if (pl.isPaymentStatus) {
    sql += 'and payment_status <> 0 '
  }
  const params = [pl.newStatus, pl.debtAckContractNumber, pl.status, pl.closed, pl.annexNumber]
  if (pl.maxNumcycle) {
    sql += 'and num_cycle <= $6 and type = 2 '
    params.push(pl.maxNumcycle)
  }
  sql += ' returning *'
  console.log('sql', sql)
  const res = await global.poolWrite.query(sql, params)
  return res.rows
}
const updateStatusByListId = async function (newStatus, listIds) {
  try {
    if (listIds.length) {
      const idJoinStr = listIds.join(',')
      const sql = `UPDATE installment SET status = $1, updated_date = now() WHERE id in (${idJoinStr})`
      const params = [newStatus]
      // console.log(sql)
      return await global.poolWrite.query(sql, params)
    }
  } catch (error) {
    console.log('ERROR updateStatusByListId', error)
    throw error
  }
}

const getInsMonthy = async function (poolRead, payload) {
  let sql = 'SELECT sum(amount) as amt FROM installment where status = 1 and type in (1,2,5) and ir_num_cycle =$1'
  let idx = 2
  const params = [payload.irNumberCycle || 2]

  if (payload.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(payload.contractNumber)
    idx++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  return await poolRead.query(sql, params)
}

const getEmi = async function (poolRead, payload) {
  const sql =
    'SELECT sum(amount) as amt FROM installment where status = 1 and type in (2,5) and ir_num_cycle = 1 and debt_ack_contract_number = $1;'
  const params = [payload.debtAckContractNumber]
  return await poolRead.query(sql, params)
}

const getNextDueDateIns = async function (poolRead, payload, currentDate = new Date()) {
  const sql =
    "select * from installment as i where debt_ack_contract_number = $1 and closed = '0' and status = '1' and \"type\" != 1 and start_date::date <= $2::date and end_date::date >= $2::date;"
  return await poolRead.query(sql, [payload.debtAckContractNumber, currentDate])
}

const getInstallment = async function (poolRead, payload, currentDate = new Date()) {
  const sql =
    'select * from installment i where start_date::date <= $2::date and end_date::date >= $2::date and debt_ack_contract_number = $1 order by end_date desc;'
  return await poolRead.query(sql, [payload.debtAckContractNumber, currentDate])
}
const findAllNotClosed = async function (poolRead, payload) {
  try {
    const sql =
      'SELECT debt_ack_contract_number FROM installment WHERE closed = 0 and status = 1 GROUP BY debt_ack_contract_number'
    return await poolRead.query(sql, [])
  } catch (e) {
    console.log('Error at findAllNotClosed:', e.message)
    console.log(e)
  }
}

const findAllNotClosedNotFactoring = async function (poolRead, payload) {
  try {
    const sql =
        'SELECT debt_ack_contract_number FROM installment WHERE closed = 0 and status = 1 ' +
        'and partner_code != ALL($1) GROUP BY debt_ack_contract_number'
    return await poolRead.query(sql, [
        [constant.PARTNER_CODE.BIZZ]
    ])
  } catch (e) {
    console.log('Error at findAllNotClosedNotFactoring:', e.message)
    console.log(e)
  }
}

const findAllNotClosedFactoring = async function (poolRead, payload) {
  try {
    const sql =
        'SELECT debt_ack_contract_number FROM installment WHERE closed = 0 and status = 1 ' +
        'and partner_code = ANY($1) GROUP BY debt_ack_contract_number'
    return await poolRead.query(sql, [
      [constant.PARTNER_CODE.BIZZ]
    ])
  } catch (e) {
    console.log('Error at findAllNotClosedFactoring:', e.message)
    console.log(e)
  }
}

const updateInterestAmount = async function (poolWrite, payload) {
  const sql = `update installment
        set amount = $1, updated_date = now()
        where debt_ack_contract_number = $2 and num_cycle = $3 and type = 2`

  return await poolWrite.query(sql, [payload.amount, payload.debtAckContractNumber, payload.numCycle])
}
const getTotalNumCycleFromInstallments = async function (poolRead, payload) {
  const sql = 'select count(*) from installment where debt_ack_contract_number = $1 and type = $2'

  return await poolRead.query(sql, [payload.debtAckContractNumber, payload.type])
}
const getInstallmentByDebtAckContractNumberAndNumCycle = async function (poolRead, payload) {
  let sql = 'select * from installment where debt_ack_contract_number = $1 and num_cycle = $2 '

  let idx = 2
  const params = [payload.debtAckContractNumber, payload.numCycle]
  if (payload.type) {
    idx++
    sql += 'and type = $' + idx
    params.push(payload.type)
  }

  return await poolRead.query(sql, params)
}
const updateInstallmentRemainAmount = async function (installmentId, remainAmount, paymentStatus) {
  try {
    return await baseRepo.baseUpdate({
      tableName,
      queryWhereObj: {
        id: installmentId
      },
      fieldsValueAddObj: {
        remain_amount: remainAmount
      },
      fieldsValueObj: {
        payment_status: paymentStatus
      },
      isUpdatedTime: true
    })
  } catch (e) {
    console.error('Error at updateBillRemainAmountAndPaymentStatus: ', e.message)
    console.log(e)
  }
}
const findInstallmentByDebtAck = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
}
const findInstallmentIdNotOnDueByDebtAck = async function (debtAckContractNumber, cancelDate) {
  try {
    const sql = `select * from installment i
      where i.debt_ack_contract_number = $1 and i.status = 1 and i.end_date <= $2  
      and i.closed = 0`
    const result = await global.poolRead.query(sql, [debtAckContractNumber, cancelDate])
    return result.rows
  } catch (e) {
    console.log('Error at findInstallmentIdNotOnDueByDebtAck:', e.message)
    console.log(e)
  }
}

const getTotalTenorPassed = async function (debtAckContractNumber) {
  try {
    const sql = `select count(ir_num_cycle) as total 
      from (
        select ir_num_cycle from installment where debt_ack_contract_number = $1 and closed = 1 group by ir_num_cycle
      ) as pass_num_cycle`

    return await global.poolRead.query(sql, [debtAckContractNumber])
  } catch (e) {
    console.log('Error at getTotalTenorPassed:', e.message)
    console.log(e)
  }
}
const findDebtContractsByDpd = async function (dpd) {
  try {
    // init sql for dpd < 0
    let sql = `select debt_ack_contract_number from installment i where status = 1
      group by debt_ack_contract_number, due_date 
      having CURRENT_DATE - due_date = $1
      order by debt_ack_contract_number;`

    if (dpd > 0) {
      sql = 'select distinct debt_ack_contract_number from loan_account la where dpd = $1 and payment_status in (1,2)'
    }
    const result = await global.poolRead.query(sql, [dpd])
    return result.rows
  } catch (e) {
    console.log('Error at findDebtContractsByDpd:', e.message)
    console.log(e)
  }
}

const findInstallmentToCalDpdByDebtAckContractNumber = async function (payload) {
  const sql = `SELECT * FROM installment 
  where debt_ack_contract_number = $1 and payment_status <> 0 and status = 1 and ( amount <> $2 or type != $3) order by num_cycle,end_date`
  return await global.poolWrite.query(sql, [payload.debtAckContractNumber, CALCUCFG.defaultFee, INSTALLMENT.TYPE.FEE])
}

const findInstallmentToCalDpdByDebtAckContractNumberFactoring = async function (payload) {
  const sql = `SELECT * FROM installment 
  where debt_ack_contract_number = $1 and payment_status <> 0 and status = 1 and end_date >= $2 and type != $3 order by end_date`
  return await global.poolWrite.query(sql, [payload.debtAckContractNumber, payload.endDate, INSTALLMENT.TYPE.FEE])
}

async function deActiveInsmLpiByDebtAckContractNumberMoreDate(debtAckContractNumber, irDate) {
  const params = [debtAckContractNumber, irDate, INSTALLMENT.STATUS.ARCHIVE]
  const sqlQuery = `update ${tableName} set status = $3, updated_date=now() where debt_ack_contract_number= $1 and status = 1 and type in (3,4) and due_date >= $2`

  return await global.poolWrite.query(sqlQuery, params)
}
const findInstalByDebtAckAndAmortId = async function (debtAckContractNumber, amortId) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      amort_id: amortId
    },
    rawResponse: true
  })
}

const sumRemainAmount = async (custId) => {
  const sql = 
`SELECT SUM(remain_amount) AS remain_amount FROM installment 
WHERE status = 1 AND "type" = 1
AND contract_number IN (
  SELECT contract_number FROM loan_account
  WHERE cust_id = $1
)`
  return await global.poolWrite.query(sql, [custId])
}

const updateStatusActiveToCancel = async (debtAckContractNumber) => {
  const sqlQuery = `update ${tableName} set status = ${DEBT_ACK_STATUS.CANCEL} where debt_ack_contract_number= $1 and status = ${DEBT_ACK_STATUS.ACTIVE} `;
  return await global.poolWrite.query(sqlQuery, [debtAckContractNumber]);
};

const deleteByDebtAckContractNumber = async function (debt_ack_contract_number) {
  console.log('deleteByDebtAckContractNumber:', debt_ack_contract_number)
  const sql = 'delete from installment where debt_ack_contract_number = $1;'
  try {
    return Promise.all([
      global.poolWrite.query(sql, [debt_ack_contract_number]),
    ])
  } catch (error) {
    console.log('Error deleteByDebtAckContractNumber', error)
  }
}
const insBatchInstallmentFactoring = async function (records) {
  const sql = format(
    `INSERT INTO installment
    (debt_ack_contract_id, num_cycle, amount, remain_amount, cycle_date, "type", payment_status, owner_id, is_testing, created_by, contract_number
    ,debt_ack_contract_number,start_date,end_date,due_date,of_num_cycle,lpi_at_cycle,ir_num_cycle,outstanding_prin,description,ir_rate,amort_id,payment_priority,accept_payment_date,order_number,invoiced_date)
    VALUES %L returning *`,
    records
  )
  return await global.poolWrite.query(sql)
}

module.exports = {
  deleteByDebtAckContractNumber,
  insBatchInstallment,
  findByContractNumberAndPaymentStatus,
  updatePaymentStatusAndRemainAmt,
  findByContractNumberAndPaymentStatusNotCompleted,
  updateRemainAmtByDebtAckContractNumberAndNumCycleAndType,
  findByDebtAckContractNumberAndPaymentStatusNotCompleted,
  findByDebtAckContractNumber,
  getByDebtAckContractAndTypeAndNumCycleAndOfNumCycle,
  getInsIsNotClosed,
  getInsIsNotClosedFactoring,
  updateInsClosed,
  findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompleted,
  getInsmInfo,
  updateStatusByDebtAckContract,
  updateInsmRemainAmt,
  updateStatusByDebtAckContractAndPaymentNotClt,
  findById,
  insBatchInstallmentLpi,
  getInsMonthy,
  getNextDueDateIns,
  findByDebtAckContractNumberMQueue,
  getEmi,
  getMaxIrNumCycle,
  getCurrentIrNumCycle,
  getSumAmount,
  getSumAmountV2,
  getInstallment,
  findByDebtAckContractNumberAndIsNotAnnexAndPaymentStatusNotCompletedV2,
  insertInsmAnnexV2,
  insertBatchInsmAnnexV2,
  findAllNotClosed,
  findAllNotClosedNotFactoring,
  findAllNotClosedFactoring,
  updateInterestAmount,
  getTotalNumCycleFromInstallments,
  getInstallmentByDebtAckContractNumberAndNumCycle,
  findInstallmentIsAnnex,
  updateStatusByListId,
  findListActiveInstallment,
  updateInstallmentRemainAmount,
  findInstallmentByDebtAck,
  findInstallmentIdNotOnDueByDebtAck,
  getTotalTenorPassed,
  findByDebtAckContractNumberForResendQueue,
  findDebtContractsByDpd,
  findInstallmentToCalDpdByDebtAckContractNumber,
  findInstallmentToCalDpdByDebtAckContractNumberFactoring,
  getInstallmentNotClosed,
  getInstallmentNotClosedFactoring,
  deActiveInsmLpiByDebtAckContractNumberMoreDate,
  findInstalByDebtAckAndAmortId,
  sumRemainAmount,
  updateStatusActiveToCancel,
  insBatchInstallmentFactoring
}
