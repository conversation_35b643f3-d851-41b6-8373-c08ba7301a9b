const { FLAG_ACTIVE, config } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'loan_status_hst'

const insertLoanStatusHst = async function (payload, flagActive = FLAG_ACTIVE) {
  const result = await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      debt_ack_contract_number: payload.debtAckContractNumber,
      loan_status: payload.status,
      status_date: payload.statusDate,
      created_user: payload.createdBy || config.createdBy,
      flag_active: flagActive
    },
    isReturn: true
  })
  if (result.rows.length) {
    return result.rows[0].hst_id
  }
  return null
}
const updateLoanStatusHstFlagActive = async function (debtAckContractNumber, oldFlagActive, newflagActive) {
  const result = await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: oldFlagActive
    },
    fieldsValueObj: {
      flag_active: newflagActive
    },
    isUpdatedTime: true
  })
  if (result.length) {
    return result[0]
  }
  return {}
}
module.exports = {
  insertLoanStatusHst,
  updateLoanStatusHstFlagActive
}
