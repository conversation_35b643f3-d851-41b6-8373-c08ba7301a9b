const baseRepo = require('./base.repo')

const tableName = 'batch_process_log'

const insertBatchProcessLog = async function (payload) {
  const result = await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      product_type: payload.productType,
      batch_date: payload.batchDate,
      job_name: payload.jobName,
      status: payload.status,
      start_date: payload.startDate,
      end_date: payload.endDate,
      run_at: payload.runAt,
      job_id: payload.jobId
    },
    isReturn: true
  })
  if (result.rows.length) {
    return result.rows[0].amort_id
  }
  return null
}

module.exports = {
  insertBatchProcessLog
}
