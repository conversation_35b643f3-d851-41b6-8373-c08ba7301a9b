const clearKunn = async function (kunnNumber, custId) {
  console.log('Clear kunn:', kunnNumber)
  const sql1 = 'delete from loan_account where debt_ack_contract_number = $1;'
  const sql2 = 'delete from bill_on_due where debt_ack_contract_number = $1;'
  const sql3 = 'delete from loan_status_hst where debt_ack_contract_number = $1;'
  const sql4 = 'delete from payment where debt_ack_contract_number = $1;'
  const sql5 = 'delete from payment_detail where debt_ack_contract_number = $1;'
  const sql6 = 'delete from installment where debt_ack_contract_number = $1;'
  const sql7 = 'delete from coll_dpd_hist where debt_ack_contract_number = $1;'
  const sql8 = 'delete from contract_risk_grp where debt_ack_contract_number = $1;'
  const sql9 = 'delete from loan_risk_grp where debt_ack_contract_number = $1; '
  const sql10 = 'delete from ir_charge where debt_ack_contract_number = $1; '
  const sql11 = 'delete from loan_annex where debt_ack_contract_number = $1;'
  const sql12 = 'delete from cust_risk_grp where cust_id = $1;'
  try {
    return Promise.all([
      global.poolWrite.query(sql1, [kunnNumber]),
      global.poolWrite.query(sql2, [kunnNumber]),
      global.poolWrite.query(sql3, [kunnNumber]),
      global.poolWrite.query(sql4, [kunnNumber]),
      global.poolWrite.query(sql5, [kunnNumber]),
      global.poolWrite.query(sql6, [kunnNumber]),
      global.poolWrite.query(sql7, [kunnNumber]),
      global.poolWrite.query(sql8, [kunnNumber]),
      global.poolWrite.query(sql9, [kunnNumber]),
      global.poolWrite.query(sql10, [kunnNumber]),
      global.poolWrite.query(sql11, [kunnNumber]),
      global.poolWrite.query(sql12, [custId])
    ])
  } catch (error) {
    console.log('Error clear kunn', error)
  }
}
const clearHM = async function (contractNumber) {
  console.log('Clear HM:', contractNumber)
  const sql = 'delete  from loan_contract_limit where contract_number = $1'
  return await global.poolWrite.query(sql, [contractNumber])
}

module.exports = {
  clearKunn,
  clearHM
}
