
const common = require('../utils/common')

const baseRepo = require('./base.repo')

const tableName = 'loan_contract_limit'

const insMerchantLimit = async function (poolWrite, payload) {
  const sql = `INSERT INTO loan_contract_limit
    (product_code, apr_limit_amt, cust_id, remain_limit_amt, status, contract_number, periodicity, hold_money,
    active_date, owner_id, is_testing, created_date, updated_date, created_by, tenor,grace_day_number,bill_day,
    start_date,end_date, partner_code, contract_type, channel, phone_number, co_cust_id)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, now(), $9, $10, now(), now(), $11,$12,$13,$14,$15,$16, $17, $18, $19, $20, $21) returning *`
  return await poolWrite.query(sql, [
    payload.productCode,
    payload.amount,
    payload.custId,
    payload.remainAmount,
    payload.status,
    payload.contractNumber,
    payload.periodicity,
    payload.holdMoney,
    payload.ownerId,
    payload.isTesting,
    payload.createdBy,
    payload.tenor,
    payload.graceDayNumber,
    payload.billDay,
    payload.startDate,
    payload.endDate,
    payload.partnerCode,
    payload.contractType,
    payload.channel,
    payload.phoneNumber,
    payload.coCustId,
  ])
}

const findByContractNumberAndCustId = async function (poolRead, payload) {
  const arrParams = []
  let index = 1
  let sql = 'select * from loan_contract_limit ml where 1 = 1'
  if (payload.contractNumber != undefined) {
    sql = sql + 'and contract_number = $' + index
    arrParams.push(payload.contractNumber)
    index++
  }
  if (payload.custId != undefined) {
    sql = sql + 'and cust_id  = $' + index
    arrParams.push(payload.custId)
    index++
  }
  if (payload.mcLimitId != undefined) {
    sql = sql + 'and contract_limit_id = $' + index
    arrParams.push(payload.mcLimitId)
    index++
  }
  return await poolRead.query(sql, arrParams)
}
const findActiveByContractNumberAndCustId = async function (poolRead, payload) {
  const arrParams = []
  let index = 1
  let sql = 'select * from loan_contract_limit ml where status = 1 '
  if (payload.contractNumber != undefined) {
    sql = sql + 'and contract_number = $' + index
    arrParams.push(payload.contractNumber)
    index++
  }
  if (payload.custId != undefined) {
    sql = sql + 'and cust_id  = $' + index
    arrParams.push(payload.custId)
    index++
  }
  if (payload.mcLimitId != undefined) {
    sql = sql + 'and contract_limit_id = $' + index
    arrParams.push(payload.mcLimitId)
    index++
  }
  return await poolRead.query(sql, arrParams)
}
const updateRemainAmount = async function (poolWrite, payload) {
  const sql = `UPDATE loan_contract_limit
    SET remain_limit_amt = remain_limit_amt - $1, updated_date=now()
    WHERE contract_limit_id=$2 and remain_limit_amt - $1 >= 0 returning remain_limit_amt,updated_date`
  return await poolWrite.query(sql, [payload.amount, payload.mcLimitId])
}
const updateStatusByMcLimitId = async function (poolWrite, payload) {
  const sql = `UPDATE loan_contract_limit 
    SET  status= $1,  updated_date=now() 
    WHERE contract_limit_id= $2 returning status,updated_date,contract_limit_id`
  return await poolWrite.query(sql, [payload.status, payload.mcLimitId])
}
const updateStatusByContractNumber = async function (poolWrite, payload) {
  const sql = `UPDATE loan_contract_limit 
    SET  status= $1,  updated_date=now() 
    WHERE contract_number= $2 returning status,updated_date,contract_limit_id`
  return await poolWrite.query(sql, [payload.status, payload.contractNumber])
}
const updateRemainLimit = async function (poolWrite, payload) {
  try {
    const sql = `UPDATE loan_contract_limit
        SET remain_limit_amt = remain_limit_amt + $1, updated_date=now()
        WHERE contract_number=$2 `
    return await poolWrite.query(sql, [payload.amount, payload.contractNumber])
  } catch (e) {
    common.log('Error at updateRemainLimit: ' + e.message, 'error')
    console.log(e)
  }
}
const updateMcLimitByContractNumber = async function (payload) {
  try {
    return await baseRepo.baseUpdate({
      tableName,
      queryWhereObj: {
        contract_number: payload.contractNumber
      },
      fieldsValueObj: {
        apr_limit_amt: payload.amount,
        remain_limit_amt: payload.amount,
        tenor: payload.tenor
      }
    })
  } catch (e) {
    common.log('Error at updateMcLimitByContractNumber: ' + e.message, 'error')
    throw e
  }
}
module.exports = {
  insMerchantLimit,
  findByContractNumberAndCustId,
  findActiveByContractNumberAndCustId,
  updateRemainAmount,
  updateStatusByMcLimitId,
  updateRemainLimit,
  updateMcLimitByContractNumber,
  updateStatusByContractNumber
}
