const baseRepo = require('./base.repo')

const tableName = 'import_cic_risk_grp'

const insCicRiskGrp = async function (payload) {
  return await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      file_name: payload.fileName,
      upload_date: payload.importDate,
      owner_id: payload.ownerId,
      is_testing: payload.isTesting,
      created_by: payload.createdBy
    },
    isReturn: true
  })
}

const findCicRiskGrp = async function () {
  return await baseRepo.baseReadAll({
    tableName,
    queryOrderArr: ['id', 'DESC'],
    limit: 1
  })
}

const getHistoryImportByDebtAck = async function (poolRead, pl) {
  const sql = `select distinct icrg.* from import_cic_risk_grp_detail icrgd 
    join import_cic_risk_grp icrg on icrgd.import_cic_rp_id  = icrg.id
    where debt_ack_contract_number  = $1`
  return await poolRead.query(sql, [pl.debtAckContractNumber])
}

module.exports = {
  insCicRiskGrp,
  getHistoryImportByDebtAck,
  findCicRiskGrp
}
