/**
 * Disbursement repository.
 *
 */

const baseRepo = require('./base.repo')

const tableName = 'disbursement'

async function insert(poolWrite, payload) {
  try {
    const sqlQuery = `insert into disbursement(contract_number, loan_id, amt, tran_id, partner_code, receiver_name, bank_code, bank_name, 
      bank_account, tran_status, created_date, update_date,created_user,session_id,debt_ack_contract_number,partner_tran_no,branch_code,email,reference,comments,tran_date,value_date
      ,group_acc_type,type) 
      values ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, now(), now(),$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22) returning *`

    return await poolWrite.query(sqlQuery, [
      payload.contractNumber,
      payload.loanId,
      payload.amt,
      payload.tranId,
      payload.partnerCode,
      payload.receiverName,
      payload.bankCode,
      payload.bankName,
      payload.bankAccount,
      payload.tranStatus,
      payload.createdUser,
      payload.sessionId,
      payload.debtAckContractNumber,
      payload.partnerTranNo,
      payload.branchCode,
      payload.email,
      payload.reference,
      payload.comments,
      payload.tranDate,
      payload.valueDate,
      payload.groupAccType,
      payload.type,
    ])
  } catch (error) {
    console.log(error)
  }
}

async function insertManyDisbursement(arrayFieldsValueObj) {
  return await baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj,
    isReturn: true
  })
}
async function updateStatusByLoanId(poolWrite, pl) {
  const sqlQuery =
    'update disbursement set tran_status = $1, partner_code = $3, tran_id = $4 ,partner_tran_no = $5,tran_date =$6,value_date =$6, update_date = now() where loan_id = $2 returning *'
  return await poolWrite.query(sqlQuery, [
    pl.status,
    pl.loanId,
    pl.partnerCode,
    pl.tranId,
    pl.partnerTranNo,
    pl.tranDate
  ])
}
async function updateStatusByDisburId(poolWrite, pl) {
  const sqlQuery =
    `update disbursement set tran_status = $1,
    partner_code = $3,
    tran_id = $4 ,
    partner_tran_no = $5,
    tran_date =$6,
    value_date =$6,
    update_date = now() ,
    re_disburse_date = $7
    where disb_id = $2 returning *`
  return await poolWrite.query(sqlQuery, [
    pl.status,
    pl.disburId,
    pl.partnerCode,
    pl.tranId,
    pl.partnerTranNo,
    pl.tranDate,
    pl.reDisburseDate
  ])
}
async function updateStatusByDebtAck(pl) {
  const sqlQuery =
    'update disbursement set tran_status = $1, partner_code = $3, tran_id = $4 ,partner_tran_no = $5,tran_date =$6,value_date =$6, update_date = now() where debt_ack_contract_number = $2 returning *'
  return await global.poolWrite.query(sqlQuery, [
    pl.status,
    pl.debtAckContractNumber,
    pl.partnerCode,
    pl.tranId,
    pl.partnerTranNo,
    pl.tranDate
  ])
}

async function findByContractNumber(poolRead, contractNumber) {
  const sqlQuery = 'select * from disbursement d  where contract_number = $1'
  const rs = await poolRead.query(sqlQuery, [contractNumber])
  return rs.rows
}

async function findByDebtAckContractNumber(debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
}
async function findByTranId(poolRead, tranId) {
  const sql = 'select * from disbursement where tran_id = $1'
  return await poolRead.query(sql, [tranId])
}
async function findAllReconcili(poolRead, pl) {
  try {
    let sql = "select * from disbursement where tran_status = '1'"
    const params = []
    let idx = 1
    if (pl.mode && pl.mode == 1) {
      sql += ' and payment_reconciled = 0'
    }
    if (pl.contractNumber) {
      sql += ' and debt_ack_contract_number = $' + idx
      params.push(pl.contractNumber)
      idx++
    }
    if (pl.payId) {
      sql += ' and reference = $' + idx
      params.push(pl.payId)
      idx++
    }
    if (pl.fromDate) {
      sql += ' and tran_date >= $' + idx
      params.push(pl.fromDate)
      idx++
    }
    if (pl.toDate) {
      sql += ' and tran_date < $' + idx
      params.push(pl.toDate)
      idx++
    }
    if (pl.partnerCode) {
      sql += ' and partner_code = $' + idx
      params.push(pl.partnerCode)
      idx++
    }
    if (pl.transactionId) {
      sql += ' and partner_tran_no = $' + idx
      params.push(pl.transactionId)
      idx++
    }
    // console.log({sql})
    // console.log({params})
    return await poolRead.query(sql, params)
  } catch (e) {
    console.log('Error at disbur findAllReconcili:', e.message)
    console.log(e)
  }
}
async function updatePaymentReconciled(poolWrite, payIds) {
  try {
    const dateNow = new Date()
    let sql = 'UPDATE disbursement set update_date=$1, payment_reconciled = 1 WHERE partner_tran_no in ('
    let idx = 2
    const params = [dateNow]
    for (const i in payIds) {
      sql += '$' + idx + ' ,'
      idx++
      params.push(payIds[i])
    }
    sql = sql.substring(0, sql.length - 1) + ')'
    sql += ' returning *'
    return await poolWrite.query(sql, params)
  } catch (e) {
    console.log('Error at disbursement updatePaymentReconciled:', e.message)
    console.log(e)
  }
}

module.exports = {
  updateStatusByDebtAck,
  insert,
  updateStatusByLoanId,
  findByContractNumber,
  findByTranId,
  findAllReconcili,
  updatePaymentReconciled,
  findByDebtAckContractNumber,
  insertManyDisbursement,
  updateStatusByDisburId
}
