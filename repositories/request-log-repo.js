const format = require('pg-format')

async function insert(poolWrite, payload) {
  try {
    const records = [
      [
        payload.api,
        payload.eventType,
        payload.contractNumber,
        payload.tranTime,
        payload.method,
        payload.requestBody?.slice(0, 10000),
        payload.response?.slice(0, 10000),
        payload.requestTime,
        payload.responseTime,
        payload.responseCode,
        payload.createdDate,
        payload.updatedDate,
        payload.createdUser,
        payload.ownerId,
        payload.statusCode,
        payload.serviceType,
        payload.custId,
        payload.requestParams,
        payload.requestQuery,
        payload.requestHeaders
      ]
    ]
    const sqlQuery = format(
      `insert into request_log(api, event_type, contract_number, tran_time, method, request_body, response,
                    request_time, response_time, response_code, created_date, updated_date, created_user, 
                    owner_id, status_code, service_type, cust_id, request_params, request_query, request_headers) values %L returning id`,
      records
    )
    return await poolWrite.query(sqlQuery)
  } catch (error) {
    console.log(error)
    console.log('Error at insert request log : ' + error.message)
  }
}

async function findRequestByContractNumberRouteAndTime(contractNumber, endpoint, startTime, endTime) {
  const sql =
    'SELECT * from request_log where contract_number = $1 and api = $2 and tran_time >= $3 and tran_time <= $4'

  return await global.poolRead.query(sql, [contractNumber, endpoint, startTime, endTime])
}
module.exports = {
  insert,
  findRequestByContractNumberRouteAndTime
}
