const insertFeeDetail = async function (payload) {
  const sql = `INSERT INTO fee_detail
  (fee_id, debt_ack_contract_number, fee_amt, status, owner_id, is_testing, created_date, updated_date, created_by)
    VALUES($1, $2, $3, 1, $4, 0, now(), now(), $5) returning *`
  return await global.poolWrite.query(sql, [
    payload.feeId,
    payload.debtAckContractNumber,
    payload.feeAmt,
    payload.ownerId,
    payload.createdBy
  ])
}

module.exports = {
  insertFeeDetail
}
