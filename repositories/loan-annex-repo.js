const { ANNEX } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'loan_annex'

const insLoanAnnex = async function (poolWrite, pl) {
  const sql = `INSERT INTO loan_annex
    (annex_number, annex_type, annex_status, status_date, termination_motive, amendment_type, effect_start_date,
         termination_date, prin_amt, penalities_amt, discount_amt, frees_amt, insur_adjust_amt, instal_adjust_amt,
          already_due_amt, lpi_amt, created_by, owner_id, debt_ack_contract_number)
    VALUES($1,$2,$3,$4,$5,$6,$7,$8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)`
  return await poolWrite.query(sql, [
    pl.annexNumber,
    pl.annexType,
    pl.annexStatus,
    pl.statusDate,
    pl.terminationMotive,
    pl.amendmentType,
    pl.effectStartDate,
    pl.terminationDate,
    pl.prinAmt,
    pl.penalitiesAmt,
    pl.discountAmt,
    pl.freesAmt,
    pl.insurAdjustAmt,
    pl.instalAdjustAmt,
    pl.alreadyDue_amt,
    pl.lpiAmt,
    pl.createdBy,
    pl.ownerId,
    pl.debtAckContractNumber
  ])
}

const insertLoanAnnex = async function (payload) {
  return await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      annex_number: payload.annexNumber,
      debt_ack_contract_number: payload.debtAckContractNumber,
      annex_status: payload.annexStatus,
      effect_start_date: payload.effectStartDate,
      termination_date: payload.terminationDate,
      prin_amt: payload.prinAmt,
      penalities_amt: payload.penalitiesAmt,
      penalty_rate: payload.penaltyRate,
      lpi_amt: payload.lpiAmt,
      total_amt: payload.totalAmount,
      already_due_amt: payload.alreadyDue,
      created_by: payload.createdBy,
      owner_id: payload.ownerId,
      annex_type: payload.annexType,
      account_request_status: payload.accountRequestStatus,
      instal_adjust_amt: payload.installmentAdjustment,
      due_capital_amt: payload.dueCapital,
      due_interest_amt: payload.dueInterest,
      due_fee_amt: payload.dueFee,
      non_allocate_amt: payload.totalNonAllAmt,
      cod_fee_amt: payload.codFeeAmt,
      total_bill_amt: payload.totalBillAmount,
      lpi_capital_amt: payload.lpiPrin,
      lpi_interest_amt: payload.lpiInterest,
      penalty_fee_amt: payload.penaltyFeeAmt,
      on_due_capital: payload.onDueCapital,
      on_due_interest: payload.onDueInterest,
      on_due_fee: payload.onDueFee,
      over_due_capital: payload.overDueCapital,
      over_due_interest: payload.overDueInterest,
      over_due_fee: payload.overDueFee,
    },
    isReturn: true
  })
}

const updateLoanAnnexByDraftAnnexNumber = async function (draftAnnexNumber, payload) {
  return await baseRepo.baseUpdate({
    tableName,
    fieldsValueObj: {
      debt_ack_contract_number: payload.debtAckContractNumber,
      annex_status: payload.annexStatus,
      effect_start_date: payload.effectStartDate,
      termination_date: payload.terminationDate,
      prin_amt: payload.prinAmt,
      penalities_amt: payload.penalitiesAmt,
      penalty_rate: payload.penaltyRate,
      lpi_amt: payload.lpiAmt,
      total_amt: payload.totalAmount,
      already_due_amt: payload.alreadyDue,
      created_by: payload.createdBy,
      owner_id: payload.ownerId,
      annex_type: payload.annexType,
      account_request_status: payload.accountRequestStatus,
      instal_adjust_amt: payload.installmentAdjustment,
      due_capital_amt: payload.dueCapital,
      due_interest_amt: payload.dueInterest,
      due_fee_amt: payload.dueFee,
      non_allocate_amt: payload.totalNonAllAmt,
      cod_fee_amt: payload.codFeeAmt,
      total_bill_amt: payload.totalBillAmount,
      lpi_capital_amt: payload.lpiPrin,
      lpi_interest_amt: payload.lpiInterest,
      penalty_fee_amt: payload.penaltyFeeAmt,
      on_due_capital: payload.onDueCapital,
      on_due_interest: payload.onDueInterest,
      on_due_fee: payload.onDueFee,
      over_due_capital: payload.overDueCapital,
      over_due_interest: payload.overDueInterest,
      over_due_fee: payload.overDueFee,
    },
    queryWhereObj: {
      annex_number: draftAnnexNumber
    },
    isReturn: true,
    isUpdatedTime: true
  })
}
const findLoanAnnexByAnnexNumber = async function (annexNumber) {
  const result = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      annex_number: annexNumber
    }
  })
  if (result.length) {
    return result[0]
  }
  return null
}
const updateStatusLoanAnnex = async function (poolWrite, pl) {
  const sql =
    'UPDATE loan_annex SET annex_status = $1, updated_date = now() WHERE annex_number = $2 and annex_status =$3 returning *'
  return await poolWrite.query(sql, [pl.annexStatusNew, pl.annexNumber, pl.annexStatusOld])
}

const getAnnexHistory = async (poolRead, debtAckContractNumber) => {
  const sql =
    'select * from loan_annex la where debt_ack_contract_number = $1 order by termination_date desc, annex_id desc'
  return await poolRead.query(sql, [debtAckContractNumber])
}

const getAnnextAmt = async (poolRead, pl) => {
  const sql = `select sum(bod.remain_amount) as annextamt, la.annex_id from loan_annex la, bill_on_due as bod where la.annex_number = bod.annex_number
        and la.debt_ack_contract_number = bod.debt_ack_contract_number and la.debt_ack_contract_number = $1 and annex_status ='0' group by la.annex_id;`
  return await poolRead.query(sql, [pl.debtAckContractNumber])
}
const findAnnexByDebtAck = async (poolRead, pl) => {
  try {
    const sql = "select * from loan_annex where debt_ack_contract_number = $1 and annex_status = '1'"
    return await poolRead.query(sql, [pl.debtAckContractNumber])
  } catch (e) {
    console.log('Error at findAnnexByDebtAck: ', e.message)
    throw e
  }
}
const findAnnexNotCancelByDebtAck = async (poolRead, pl) => {
  try {
    const sql = "select * from loan_annex where debt_ack_contract_number = $1 and annex_status <> '2'"
    return await poolRead.query(sql, [pl.debtAckContractNumber])
  } catch (e) {
    console.log('Error at findAnnexByDebtAck: ', e.message)
    throw e
  }
}

async function findAnnexNotCancelByDebtAckContractNumber(debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      'not annex_status': ANNEX.STATUS.CANCEL
    }
  })
}
async function findAnnexInitByDebtAckContractNumber(debtAckContractNumber, type) {
  const queryWhereObj = {
    debt_ack_contract_number: debtAckContractNumber,
    annex_status: ANNEX.STATUS.INIT
  }
  if (type) {
    queryWhereObj.annex_type = type
  }
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj
  })
}

async function findAnnexByDebtAckContractNumberAndAnnexNumberAndAnnexStatus(debtAckContractNumber, annexNumber, annexStatus, type) {
  const queryWhereObj = {
    debt_ack_contract_number: debtAckContractNumber,
    annex_number: annexNumber,
    annex_status: annexStatus
  }
  if (type) {
    queryWhereObj.annex_type = type
  }
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj
  })
}

const findMinTerminationMotiveByCustId = async (custId, startDate, endDate) => {
  const params = [ANNEX.TYPE.FULL_EARLY_TERMINATION]
  let subQuery = `select
                    la.cust_id,
                    case
                      when abs(extract(day from la.active_date - la2.termination_date)) < 5 then '2.CO'
                      when la2.termination_date < la.end_date then '3.CR'
                      when abs(extract(day from la.end_date - la.termination_date)) = 0 then '4.NE'
                      else '5.NULL'
                    end termination_motive
                  from
                    loan_account la
                  left join loan_annex la2 on
                    la.debt_ack_contract_number = la2.debt_ack_contract_number
                    and la2.annex_type = $1
                    and la2.annex_status = '1'
                  `
  if (startDate) {
    params.push(startDate)
    subQuery += ` and la2.termination_date::date >= $${params.length}`
  }
  if (endDate) {
    params.push(endDate)
    subQuery += ` and la2.termination_date::date <= $${params.length}`
  }
  if (custId) {
    params.push(custId)
    subQuery += ` where la.cust_id = $${params.length}`
  }
  const sql = `select min (termination_motive) from (${subQuery}) as a`
  return await global.poolRead.query(sql, params)
}
module.exports = {
  insertLoanAnnex,
  updateLoanAnnexByDraftAnnexNumber,
  insLoanAnnex,
  updateStatusLoanAnnex,
  getAnnexHistory,
  getAnnextAmt,
  findLoanAnnexByAnnexNumber,
  findAnnexByDebtAck,
  findAnnexNotCancelByDebtAck,
  findAnnexNotCancelByDebtAckContractNumber,
  findAnnexInitByDebtAckContractNumber,
  findAnnexByDebtAckContractNumberAndAnnexNumberAndAnnexStatus,
  findMinTerminationMotiveByCustId
}
