const { FLAG_ACTIVE, FLAG_NOT_ACTIVE } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'loan_emi_history'

const insertEmiHistory = async function (loanAccount, emi, eventType) {
  return await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      debt_ack_contract_number: loanAccount.debt_ack_contract_number,
      contract_number: loanAccount.contract_number,
      cust_id: loanAccount.cust_id,
      emi,
      event_type: eventType
    }
  })
}
const findCurrentEmi = async function (debtAckContractNumber) {
  const result = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: FLAG_ACTIVE
    }
  })
  return result.length ? result[0].emi : 0
}

const deActiveCurrentEmi = async function (debtAckContractNumber) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: FLAG_ACTIVE
    },
    fieldsValueObj: {
      flag_active: FLAG_NOT_ACTIVE
    }
  })
}
module.exports = {
  insertEmiHistory,
  findCurrentEmi,
  deActiveCurrentEmi
}
