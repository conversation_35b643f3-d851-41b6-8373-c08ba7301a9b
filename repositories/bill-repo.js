const insBill = async function (poolWrite, payload) {
  const sql = `INSERT INTO bill\
    (contract_number, prin_amount, ir_amount, fee_amount, remain_prin_amount, remain_ir_amount, remain_fee_amount,
         bill_print_date, owner_id, created_date, updated_date, payment_status, start_date, end_date, due_date)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, now(), now(), $10, $11, $12, $13)`
  return await poolWrite.query(sql, [
    payload.contractNumber,
    payload.prinAmount,
    payload.irAmount,
    payload.feeAmount,
    payload.remainPrinAmount,
    payload.remainIrAmount,
    payload.remainFeeAmount,
    payload.billPrintDate,
    payload.ownerId,
    payload.paymentStatus,
    payload.startDate,
    payload.endDate,
    payload.dueDate
  ])
}

module.exports = {
  insBill
}
