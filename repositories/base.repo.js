const _ = require('lodash')
const { SQLOperatorGenerator } = require('../utils/sql-operator-generate')

async function baseReadAll({
  tableName = 'tableName',
  selectQuery = '*',
  queryWhereObj = {},
  queryOrderArr = [],
  limit = null,
  rawResponse = false,
  poolConnect = global.poolRead
}) {
  let queryWhere = ''
  const valuesArray = Object.values(queryWhereObj)
  const valuesArrayFilterNull = valuesArray.filter((item) => item != null)

  if (valuesArray.length) {
    const listQueryWhere = []
    let index = 1
    for (const [key, value] of Object.entries(queryWhereObj)) {
      if (value == null) {
        listQueryWhere.push(`${key} is null`)
      } else {
        listQueryWhere.push(`${key} = $${index}`)
        index += 1
      }
    }
    queryWhere = 'where ' + listQueryWhere.join(' and ')
  }
  let queryOrder = ''

  if (queryOrderArr.length > 1 && queryOrderArr.length % 2 == 0) {
    const chunkQueryOrder = _.chunk(queryOrderArr, 2)
    queryOrder = 'order by ' + chunkQueryOrder.map((item) => item.join(' ')).join(', ')
  }

  let queryLimit = ''
  if (limit) {
    queryLimit = `limit ${limit}`
  }
  const sqlQuery = `select ${selectQuery} from ${tableName} ${queryWhere} ${queryOrder} ${queryLimit}`

  const result = await poolConnect.query(sqlQuery, valuesArrayFilterNull)

  if (rawResponse) {
    return result
  }
  return result.rows
}

async function baseInsert({ tableName = 'tableName', fieldsValueObj = {}, isReturn = false }) {
  const fields = Object.keys(fieldsValueObj).join(',')
  const valuesArray = Object.values(fieldsValueObj)
  const queryValues = valuesArray.map((item, index) => `$${index + 1}`).join(',')
  const returnQuery = isReturn ? 'returning *' : ''
  const sqlQuery = `insert into ${tableName} (${fields}) values (${queryValues}) ${returnQuery}`

  return await global.poolWrite.query(sqlQuery, valuesArray)
}

async function baseInsertV2({ tableName = 'tableName', fieldsValueObj = {}, isReturn = false }) {
  const fields = Object.keys(fieldsValueObj).join(',')
  const valuesArray = Object.values(fieldsValueObj)
  const queryValues = valuesArray.map((item, index) => `$${index + 1}`).join(',')
  const returnQuery = isReturn ? 'returning *' : ''
  const sqlQuery = `insert into ${tableName} (${fields}) values (${queryValues}) ${returnQuery}`

  const result = await global.poolWrite.query(sqlQuery, valuesArray)
  if (isReturn) {
    return result.rows
  }
  return result
}

async function insertMany({ tableName = 'tableName', arrayFieldsValueObj = [], isReturn = false }) {
  if (!arrayFieldsValueObj.length) {
    console.log('[BASE REPO]Input data is invalid')
    return
  }
  const allValuesArray = []
  const queryValuesArray = []
  const maxLengthKeyObj = _.maxBy(arrayFieldsValueObj, function (e) {
    return Object.keys(e).length
  })
  const fields = Object.keys(sortObject(maxLengthKeyObj))
  for (const fieldsValueObj of arrayFieldsValueObj) {
    const queryValues = fields.map((item, index) => `$${allValuesArray.length + index + 1}`).join(',')
    queryValuesArray.push(`(${queryValues})`)

    for (const field of fields) {
      if (fieldsValueObj[field] != null) {
        allValuesArray.push(fieldsValueObj[field])
      } else {
        allValuesArray.push(null)
      }
    }
  }
  const allQueryValues = queryValuesArray.join(',')
  const returnQuery = isReturn ? 'returning *' : ''

  const sqlQuery = `insert into ${tableName} (${fields}) values ${allQueryValues} ${returnQuery}`
  const result = await global.poolWrite.query(sqlQuery, allValuesArray)

  return result
}

async function baseUpdate({
  tableName = 'tableName',
  fieldsValueObj = {},
  fieldsValueAddObj = {},
  queryWhereObj = {},
  isUpdatedTime = false,
  isReturn = false
}) {
  const valuesWhere = Object.values(queryWhereObj)
  const valuesData = Object.values(fieldsValueObj)
  const valuesAddData = Object.values(fieldsValueAddObj)

  if (!valuesWhere.length) {
    return
  }

  if (!valuesData.length && !valuesAddData.length) {
    return
  }
  let querySet = 'set '

  if (valuesData.length) {
    querySet += Object.keys(fieldsValueObj)
      .map((key, index) => `${key} = $${index + 1} `)
      .join(',')
  }

  if (valuesAddData.length) {
    valuesData.length && (querySet += ', ')
    querySet += Object.keys(fieldsValueAddObj)
      .map((key, index) => `${key} = ${key} + $${index + valuesData.length + 1} `)
      .join(',')
  }

  if (isUpdatedTime) {
    querySet += ', updated_date = now() '
  }

  valuesAddData.forEach((value) => {
    valuesData.push(value)
  })

  const valuesWhereFilterNull = valuesWhere.filter((item) => item != null)

  const listQueryWhere = []
  let index = valuesData.length + 1
  for (const [key, value] of Object.entries(queryWhereObj)) {
    if (value == null) {
      listQueryWhere.push(`${key} is null`)
    } else {
      listQueryWhere.push(`${key} = $${index}`)
      index += 1
    }
  }
  const queryWhere = 'where ' + listQueryWhere.join(' and ')

  valuesWhereFilterNull.forEach((value) => {
    valuesData.push(value)
  })
  const returnQuery = isReturn ? 'returning *' : ''

  const sqlQuery = `update ${tableName} ${querySet} ${queryWhere} ${returnQuery}`
  const result = await global.poolWrite.query(sqlQuery, valuesData)
  if (isReturn) {
    return result.rows
  }
  return result
}
function sortObject(obj) {
  const sorted = {}
  const str = []
  for (const key in obj) {
    str.push(key)
  }
  str.sort()
  for (let key = 0; key < str.length; key++) {
    sorted[str[key]] = obj[str[key]]
  }
  return sorted
}

async function baseSelectV2({
  tableName = 'tableName',
  selectQuery = '*',
  queryWhereObj = {},
  queryOrderObj = {},
  offset = null,
  limit = null,
  rawResponse = false,
  poolConnect = global.poolRead
}) {
  const keyValueArray = Object.entries(queryWhereObj)

  const condition = {
    parameters: [],
    index: 0,
    listQueryWhere: []
  }
  const sqlGenerator = new SQLOperatorGenerator(condition)
  for (const [key, value] of keyValueArray) {
    sqlGenerator.setStrategy(value)
    sqlGenerator.generateQueryWhere(key)
  }
  const queryWhere = sqlGenerator.genAllQueryWhere()
  const queryOrder = sqlGenerator.genAllQueryOrder(queryOrderObj)
  const queryOffset = sqlGenerator.genQueryOffset(offset)
  const queryLimit = sqlGenerator.genQueryLimit(limit)

  const sqlQuery = `select ${selectQuery} from ${tableName} ${queryWhere} ${queryOrder} ${queryOffset} ${queryLimit}`
  const result = await poolConnect.query(sqlQuery, condition.parameters)

  if (rawResponse) {
    return result
  }
  return result.rows
}

async function baseUpdateV2({ tableName = 'tableName', fieldsValueObj = {}, queryWhereObj = {}, isReturn = false }) {
  const keyValueArrayWhere = Object.entries(queryWhereObj)
  const keyValueArraySet = Object.entries(fieldsValueObj)

  const condition = {
    parameters: [],
    index: 0,
    listQueryWhere: [],
    listQuerySet: []
  }
  const sqlGenerator = new SQLOperatorGenerator(condition)

  for (const [key, value] of keyValueArraySet) {
    sqlGenerator.setStrategy(value)
    sqlGenerator.generateQuerySet(key)
  }

  for (const [key, value] of keyValueArrayWhere) {
    sqlGenerator.setStrategy(value)
    sqlGenerator.generateQueryWhere(key)
  }

  const querySet = sqlGenerator.genAllQuerySet()
  const queryWhere = sqlGenerator.genAllQueryWhere()

  const returnQuery = isReturn ? 'returning *' : ''

  const sqlQuery = `UPDATE ${tableName} ${querySet} ${queryWhere} ${returnQuery}`
  console.log(
    `UPDATE Table: ${tableName} - Where: ${JSON.stringify(queryWhereObj)} - Data: ${JSON.stringify(fieldsValueObj)}`
  )

  const result = await global.poolWrite.query(sqlQuery, condition.parameters)
  if (isReturn) {
    return result.rows
  }
  return result
}

async function saveOne({ entity = {}, metadata = {} }) {
  if (_.isEmpty(entity)) {
    throw new Error('Entity is empty')
  }
  const { tableName, primaryKey, createdDateColumn, updatedDateColumn } = metadata
  let result
  const fieldsValueObj = _.omit(entity, [primaryKey])
  console.log(`SAVE Table: ${tableName} - Entity: ${JSON.stringify(fieldsValueObj)}`)
  if (primaryKey && entity[primaryKey] != null) {
    updatedDateColumn && (fieldsValueObj[updatedDateColumn] = new Date())
    result = await baseUpdateV2({
      tableName,
      fieldsValueObj,
      queryWhereObj: {
        [primaryKey]: entity[primaryKey]
      },
      isReturn: true
    })
  } else {
    createdDateColumn && (fieldsValueObj[createdDateColumn] = new Date())
    updatedDateColumn && (fieldsValueObj[updatedDateColumn] = new Date())
    result = await baseInsertV2({
      tableName,
      fieldsValueObj,
      isReturn: true
    })
  }
  return result?.[0] || undefined
}

async function saveMany({ entities = [], metadata = {} }) {
  const result = await Promise.all(
    entities.map((entity) => {
      return saveOne({
        entity,
        metadata
      })
    })
  )
  return result
}

async function baseSave(data, metadata) {
  if (Array.isArray(data) && data.length) {
    return saveMany({
      entities: data,
      metadata
    })
  }
  return saveOne({
    entity: data,
    metadata
  })
}

module.exports = {
  baseInsert,
  baseReadAll,
  baseUpdate,
  insertMany,
  baseSelectV2,
  baseSave,
  saveOne,
  baseUpdateV2
}
