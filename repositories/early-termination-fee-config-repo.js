const { DATE_FORMAT } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'early_termination_fee_config'
const moment = require('moment')

const getListETFeeConfig = async function () {
  const result = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      flag_active: 1
    }
  })

  let config = {};

  for (const item of result) {
    let partner_code = item.partner_code

    if (!(partner_code in config)) {
      config[partner_code] = {}
    }

    config[partner_code][item.delta_day] = item.fee
  }

  return config;
}
module.exports = {
  getListETFeeConfig
}
