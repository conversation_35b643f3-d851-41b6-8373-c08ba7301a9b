const format = require('pg-format')
const { BILL_ON_DUE } = require('../utils/constant')
const baseRepo = require('./base.repo')
const constant = require("../utils/constant");

const tableName = 'bill_on_due'

const insBill = async function (poolWrite, payload) {
  const sql = `INSERT INTO bill
    (contract_number, prin_amount, ir_amount, fee_amount, remain_prin_amount, remain_ir_amount, remain_fee_amount,
         bill_print_date, owner_id, created_date, updated_date, payment_status, start_date, end_date, due_date)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, now(), now(), $10, $11, $12, $13)`
  return await poolWrite.query(sql, [
    payload.contractNumber,
    payload.prinAmount,
    payload.irAmount,
    payload.feeAmount,
    payload.remainPrinAmount,
    payload.remainIrAmount,
    payload.remainFeeAmount,
    payload.billPrintDate,
    payload.ownerId,
    payload.paymentStatus,
    payload.startDate,
    payload.endDate,
    payload.dueDate
  ])
}
const insBatchBillOnDue = async function (records) {
  const sql = format(
    `INSERT INTO bill_on_due
    (contract_number, debt_ack_contract_number, amount, remain_amount, "type", num_cycle, on_due_date, payment_status, start_date, end_date, due_date, owner_id, created_by,installment_id,payment_priority,accept_payment_date,invoiced_date)
    VALUES %L`,
    records
  )
  return await global.poolWrite.query(sql)
}
const insBatchBillAnnex = async function (poolWrite, records) {
  const sql = format(
    `INSERT INTO bill_on_due
    (contract_number, debt_ack_contract_number, amount, remain_amount, "type", num_cycle, on_due_date, payment_status, start_date, end_date, due_date, owner_id, created_by,installment_id,is_annex,annex_number)
    VALUES %L`,
    records
  )
  return await poolWrite.query(sql)
}
const insBatchBillAnnexV2 = async function (arrayFieldsValueObj) {
  if (!arrayFieldsValueObj.length) {
    console.log('insBatchBillAnnexV2 data empty')
  }
  return await baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj
  })
}
const getBillByContractAndOnDueDate = async function (poolRead, pl) {
  let sql = 'select * from bill_on_due bod where status = 1 and type in (1,2,7,8)'
  const params = []
  let idx = 1
  if (pl.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(pl.contractNumber)
    idx++
  }
  if (pl.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(pl.debtAckContractNumber)
    idx++
  }
  if (pl.onDueDate != undefined) {
    sql += ' and on_due_date = $' + idx
    params.push(pl.onDueDate)
    idx++
  }
  return await poolRead.query(sql, params)
}

const getSumRemainAmount = async function (poolRead, pl, type) {
  const sql = `select sum(remain_amount) as sum_remain_amount from bill_on_due bod where status = 1 and "type" = ${type} and debt_ack_contract_number = $1`
  const params = [pl.debtAckContractNumber]
  return await poolRead.query(sql, params)
}

const getLpi = async function (poolRead, pl) {
  const sql =
    'select sum(remain_amount) as lpi from bill_on_due bod where "type" in (3,4) and debt_ack_contract_number = $1'
  const params = [pl.debtAckContractNumber]
  return await poolRead.query(sql, params)
}

const getBillByContractAndOnDueDateMQueue = async function (poolRead, pl, currentDate = new Date()) {
  try {
    let sql = 'select * from bill_on_due bod where 1 = 1'
    const params = []
    let idx = 1
    if (pl.contractNumber != undefined) {
      sql += ' and contract_number = $' + idx
      params.push(pl.contractNumber)
      idx++
    }
    if (pl.debtAckContractNumber != undefined) {
      sql += ' and debt_ack_contract_number = $' + idx
      params.push(pl.debtAckContractNumber)
      idx++
    }
    sql += ` and updated_date::date >= $${idx}::date`
    params.push(currentDate)
    idx++
    return await poolRead.query(sql, params)
  } catch (error) {
    console.error('Error at getBillByContractAndOnDueDateMQueue: ', error.message)
    console.log(error)
  }
}

const getBillOnDuePaymentNotComplete = async function (poolRead, pl) {
  let sql = 'select * from bill_on_due bod where payment_status <> 0 and status = 1 '
  const params = []
  let idx = 1
  if (pl.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(pl.contractNumber)
    idx++
  }
  if (pl.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(pl.debtAckContractNumber)
    idx++
  }
  if (pl.isAnnex != undefined) {
    sql += ' and is_annex = $' + idx
    params.push(pl.isAnnex)
    idx++
  }
  sql += ' order by debt_ack_contract_number,num_cycle,on_due_date,payment_priority'

  return await poolRead.query(sql, params)
}
const getBillOnDuePaymentNotCompleteFactoring = async function (poolRead, pl) {
  let sql = 'select bod.*, i.num_cycle as num_cycle from bill_on_due bod' +
      ' join installment i on i.id = bod.installment_id' +
      ' where bod.payment_status <> 0 and bod.status = 1 '
  const params = []
  let idx = 1
  if (pl.contractNumber != undefined) {
    sql += ' and bod.contract_number = $' + idx
    params.push(pl.contractNumber)
    idx++
  }
  if (pl.debtAckContractNumber != undefined) {
    sql += ' and bod.debt_ack_contract_number = $' + idx
    params.push(pl.debtAckContractNumber)
    idx++
  }
  if (pl.isAnnex != undefined) {
    sql += ' and bod.is_annex = $' + idx
    params.push(pl.isAnnex)
    idx++
  }
  sql += ' order by bod.debt_ack_contract_number,bod.num_cycle,bod.on_due_date,bod.payment_priority'

  return await poolRead.query(sql, params)
}
const getBillOnDuePaymentNotCompleteAndAcceptPaymentFactoring = async function (poolRead, pl) {
  let sql = 'select bod.* from bill_on_due bod' +
      ' where bod.payment_status <> 0 and bod.status = 1' +
      ' and ((bod.invoiced_date is not null and bod.invoiced_date <= ($2::date - ($1 || \' days\')::INTERVAL)) or bod.due_date = $2::date)'
  const params = [pl.suspendAccountHoldingDay, pl.paymentDate]
  let idx = 3
  if (pl.contractNumber != undefined) {
    sql += ' and bod.contract_number = $' + idx
    params.push(pl.contractNumber)
    idx++
  }
  if (pl.debtAckContractNumber != undefined) {
    sql += ' and bod.debt_ack_contract_number = $' + idx
    params.push(pl.debtAckContractNumber)
    idx++
  }
  if (pl.isAnnex != undefined) {
    sql += ' and bod.is_annex = $' + idx
    params.push(pl.isAnnex)
    idx++
  }
  sql += ' order by bod.debt_ack_contract_number,bod.num_cycle,bod.on_due_date,bod.payment_priority'

  return await poolRead.query(sql, params)
}
const getNumCyclePaymentNotCompleteFactoring = async function (poolRead, pl) {
  let sql = 'select DISTINCT on_due_date, num_cycle from bill_on_due bod where payment_status <> 0 and status = 1' +
      ' and debt_ack_contract_number = $1'
  const params = [pl.debtAckContractNumber]
  let idx = 2

  if (pl.isAnnex != undefined) {
    sql += ' and is_annex = $' + idx
    params.push(pl.isAnnex)
    idx++
  }
  sql += ' order by on_due_date asc,num_cycle asc'

  return await poolRead.query(sql, params)
}

const getBillOnDuePaymentNotCompleteV2 = async function (poolRead, pl) {
  let sql = 'select * from bill_on_due bod where status = 1 and payment_status <> 0'
  const params = []
  let idx = 1
  if (pl.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(pl.contractNumber)
    idx++
  }
  if (pl.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(pl.debtAckContractNumber)
    idx++
  }
  if (pl.isAnnex != undefined) {
    sql += ' and is_annex = $' + idx
    params.push(pl.isAnnex)
    idx++
  }
  sql += ' order by debt_ack_contract_number,num_cycle,on_due_date'

  return await poolRead.query(sql, params)
}

const getBillOnDuePaymentNotCompleteV3 = async function (
  debtAckContractNumber,
  order = 'asc',
  status = BILL_ON_DUE.STATUS.ACTIVE
) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      'not payment_status': 0,
      status
    },
    queryOrderArr: ['on_due_date', order, 'payment_priority', order]
  })
}
const getBillOnDuePaymentNotCompleteV3Factoring = async function (
  debtAckContractNumber,
  order = 'asc',
  status = BILL_ON_DUE.STATUS.ACTIVE,
) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      'not payment_status': 0,
      status,
    },
    queryOrderArr: ['on_due_date', order, 'payment_priority', order]
  })
}

const getBillOnDuePaymentNotCompleteAndAcceptPaymentV3Factoring = async function (
    debtAckContractNumber,
    status = BILL_ON_DUE.STATUS.ACTIVE,
    date,
    suspendAccountHoldingDay
) {
  let sql = 'select bod.* from bill_on_due bod' +
      ' where bod.debt_ack_contract_number = $1 and bod.payment_status <> 0 and bod.status = $2' +
      ' and ((bod.invoiced_date is not null and bod.invoiced_date <= ($3::date - ($4 || \' days\')::INTERVAL)) or bod.due_date = $3::date)'
  const params = [
      debtAckContractNumber,
    status,
    date,
    suspendAccountHoldingDay,
  ]
  sql += ' order by bod.on_due_date asc,bod.payment_priority asc'

  return await poolRead.query(sql, params)
}

const getAllPrinBillOnDueCompletedPayment = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      status: BILL_ON_DUE.STATUS.ACTIVE,
      type: BILL_ON_DUE.TYPE.PRIN,
      payment_status: BILL_ON_DUE.PAYMENT_STATUS.DONE
    },
    queryOrderArr: ['on_due_date', 'asc', 'id', 'asc']
  })
}

const getBillOnDuePaymentComplete = async function (poolRead, pl) {
  let sql = 'select * from bill_on_due bod where payment_status = 0 and status = 1 '
  const params = []
  let idx = 1
  if (pl.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(pl.contractNumber)
    idx++
  }
  if (pl.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(pl.debtAckContractNumber)
    idx++
  }
  if (pl.isAnnex != undefined) {
    sql += ' and is_annex = $' + idx
    params.push(pl.isAnnex)
    idx++
  }
  sql += ' order by debt_ack_contract_number,num_cycle,on_due_date'

  return await poolRead.query(sql, params)
}
const updateBillWithPaymentStatusAndRemainAmt = async function (poolWrite, payload) {
  const sql =
    'UPDATE bill_on_due SET remain_amount = $1, payment_status = $2, status = $4,updated_date = now() WHERE id = $3'
  return await poolWrite.query(sql, [payload.remainPrinAmount, payload.paymentStatus, payload.billId, payload.status])
}
const updatePaymentExpire = async function (payload) {
  const sql =
    'UPDATE bill_on_due SET amount = amount + $1, remain_amount = remain_amount + $1, payment_status = 1 ,updated_date = now() WHERE installment_id = $2'
  return await global.poolWrite.query(sql, [payload.amount, payload.installmentId])
}
const updateStatusBill = async function (poolWrite, payload) {
  const sql = 'UPDATE bill_on_due SET status = $1, updated_date = now() WHERE id = $2'
  return await poolWrite.query(sql, [payload.status, payload.billId])
}
const updateBillStatusByAnnexNumber = async function (annexNumber, status) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      annex_number: annexNumber
    },
    fieldsValueObj: {
      status
    },
    isReturn: true
  })
}

const findAllBillAnnexPaymentNotCompleted = async function (poolWrite, pl) {
  try {
    const sql =
      'select debt_ack_contract_number from bill_on_due bod where status = 0 and payment_status <> 0 and is_annex = 1 group by debt_ack_contract_number'
    return await poolWrite.query(sql, [])
  } catch (e) {
    console.error('Error at findAllBillAnnexPaymentNotCompleted: ', e.message)
    console.log(e)
  }
}
const findAllBillPaymentNotCompleted = async function (poolWrite, pl) {
  try {
    const sql =
      'select debt_ack_contract_number from bill_on_due bod where status in (1,0) and payment_status <> 0 group by debt_ack_contract_number'
    return await poolWrite.query(sql, [])
  } catch (e) {
    console.error('Error at findAllBillPaymentNotCompleted: ', e.message)
    console.log(e)
  }
}

const findAllBillPaymentNotCompletedAndAcceptPayment = async function (poolWrite, date) {
  try {
    const sql =
        'select bod.debt_ack_contract_number from bill_on_due bod' +
        ' where bod.status in (1,0) and bod.payment_status <> 0' +
        ' and ((bod.invoiced_date is not null and bod.invoiced_date <= $1::date) or bod.due_date = $1::date)' +
        ' group by bod.debt_ack_contract_number'
    return await poolWrite.query(sql, [date])
  } catch (e) {
    console.error('Error at findAllBillPaymentNotCompletedAndAcceptPayment: ', e.message)
    console.log(e)
  }
}

const findAllBillActived = async function (debtAckContractNumber) {
  try {
    const sql = 'select * from bill_on_due bod where status = 1 and debt_ack_contract_number = $1'
    const result = await global.poolWrite.query(sql, [debtAckContractNumber])
    return result.rows
  } catch (e) {
    console.error('Error at findAllBillActived: ', e.message)
    console.log(e)
  }
}

const findAllBillActiveWithNumCycle = async function (debtAckContractNumber, numCycle) {
  try {
    const sql = 'select * from bill_on_due bod where status = 1 and debt_ack_contract_number = $1 and num_cycle = $2'
    const result = await global.poolWrite.query(sql, [debtAckContractNumber, numCycle])
    return result.rows
  } catch (e) {
    console.error('Error at findAllBillActiveWithNumCycle: ', e.message)
    console.log(e)
  }
}

const updateBillRemainAmountAndPaymentStatus = async function (billId, remainAmount, paymentStatus) {
  try {
    return await baseRepo.baseUpdate({
      tableName,
      queryWhereObj: {
        id: billId
      },
      fieldsValueAddObj: {
        remain_amount: remainAmount
      },
      fieldsValueObj: {
        payment_status: paymentStatus
      }
    })
  } catch (e) {
    console.error('Error at updateBillRemainAmountAndPaymentStatus: ', e.message)
    console.log(e)
  }
}
const checkCreateAnnex = async function (amount, debtAckContractNumber) {
  try {
    const sql =
      'select sum(bod.remain_amount) as totalremainamt from bill_on_due bod where status = 1 and debt_ack_contract_number = $1 group by debt_ack_contract_number'
    const totalRemainAmt = (await global.poolRead.query(sql, [debtAckContractNumber])).rows[0]?.totalremainamt
    // if(parseFloat(amount) > parseFloat(totalRemainAmt)){//sinh annex
    //   return true
    // } else {//k sinh annex
    //   return false
    // }
    // console.log({amount,totalRemainAmt,debtAckContractNumber})
    return parseFloat(amount) - parseFloat(totalRemainAmt)
  } catch (e) {
    console.error('Error at checkCreateAnnex: ', e?.message)
    console.log(e)
  }
}
const calculateInterestCurrentDate = async function (debtAckContractNumber) {
  try {
    const sql =
      'select remain_amount, start_date, end_date, CURRENT_DATE from installment i where debt_ack_contract_number = $1 and closed = 0 and "type" = 2 and start_date < CURRENT_DATE and CURRENT_DATE <= end_date'
    const result = await global.poolRead.query(sql, [debtAckContractNumber])
    const interestAmt = result.rows[0]?.remain_amount
    const startDate = result.rows[0]?.start_date
    const endDate = result.rows[0]?.end_date
    const currentDate = result.rows[0]?.current_date

    const _startDate = new Date(startDate)
    const _endDate = new Date(endDate)
    const _currentDate = new Date(currentDate)
    const diffTime = Math.abs(_endDate - _startDate)
    const diffTimeOverDue = Math.abs(_currentDate - _startDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    const diffDaysOverDue = Math.ceil(diffTimeOverDue / (1000 * 60 * 60 * 24))
    // console.log({interestAmt,diffDaysOverDue,diffDays})
    const interest = (interestAmt * diffDaysOverDue) / diffDays
    return interest
  } catch (e) {
    console.error('Error at calculateInterestCurrentDate: ', e?.message)
    console.log(e)
    return false
  }
}

const findBillIsOnDueByDate = async function (dueDate, debtAckContractNumber) {
  try {
    let sql =
      'select * from bill_on_due bod where status = $2 and payment_status <> $3 and due_date::date = $1 and is_annex = $4'
    const params = [dueDate, BILL_ON_DUE.STATUS.ACTIVE, BILL_ON_DUE.PAYMENT_STATUS.DONE, BILL_ON_DUE.IS_ANNEX.FALSE]
    if (debtAckContractNumber) {
      sql += ' and debt_ack_contract_number = $5'
      params.push(debtAckContractNumber)
    }
    const result = await global.poolWrite.query(sql, params)
    return result.rows
  } catch (e) {
    console.log(e)
    return []
  }
}

async function deActiveLpiDueByDebtAckContractNumberMoreDate(debtAckContractNumber, irDate) {
  const params = [debtAckContractNumber, irDate, BILL_ON_DUE.STATUS.CANCEL]
  const sqlQuery = `update ${tableName} set status = $3, updated_date=now() where debt_ack_contract_number= $1 and status = 1 and type in (3,4) and due_date >= $2`

  return await global.poolWrite.query(sqlQuery, params)
}

module.exports = {
  insBill,
  insBatchBillOnDue,
  getBillByContractAndOnDueDate,
  getBillOnDuePaymentNotComplete,
  getBillOnDuePaymentNotCompleteFactoring,
  getBillOnDuePaymentNotCompleteAndAcceptPaymentFactoring,
  getNumCyclePaymentNotCompleteFactoring,
  updateBillWithPaymentStatusAndRemainAmt,
  updatePaymentExpire,
  insBatchBillAnnex,
  insBatchBillAnnexV2,
  updateStatusBill,
  getBillOnDuePaymentComplete,
  getBillByContractAndOnDueDateMQueue,
  getSumRemainAmount,
  getBillOnDuePaymentNotCompleteV2,
  getBillOnDuePaymentNotCompleteV3,
  getBillOnDuePaymentNotCompleteV3Factoring,
  getBillOnDuePaymentNotCompleteAndAcceptPaymentV3Factoring,
  getLpi,
  findAllBillAnnexPaymentNotCompleted,
  findAllBillPaymentNotCompleted,
  findAllBillPaymentNotCompletedAndAcceptPayment,
  getAllPrinBillOnDueCompletedPayment,
  findAllBillActived,
  findAllBillActiveWithNumCycle,
  updateBillRemainAmountAndPaymentStatus,
  updateBillStatusByAnnexNumber,
  checkCreateAnnex,
  calculateInterestCurrentDate,
  findBillIsOnDueByDate,
  deActiveLpiDueByDebtAckContractNumberMoreDate
}
