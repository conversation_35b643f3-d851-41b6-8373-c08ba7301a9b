const baseRepo = require('./base.repo')
const constant = require('../utils/constant')

const tableName = 'loan_annex_pending'

const insertLoanAnnexPending = async function (payload) {
  return await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      debt_ack_contract_number: payload.debtAckContractNumber,
      annex_amt: payload.annexAmount,
      annex_date: payload.annexDate,
      status: payload.status,
      request_id: payload.requestId
    },
    isReturn: true
  })
}

const findListAnnexPendingByDate = async function ({ scanDate, status = constant.ANNEX_PENDING_STATUS.PENDING }) {
  const queryWhereObj = {
    status
  }
  if (scanDate) {
    queryWhereObj.annex_date = scanDate
  }
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj
  })
}

const updateStatusAnnexPending = async function (payload) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      id: payload.id
    },
    fieldsValueObj: {
      status: payload.status
    }
  })
}

const updateCancelNotActiveAnnexPending = async function (payload) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: payload.debtAckContractNumber,
      status: payload.oldStatus
    },
    fieldsValueObj: {
      status: payload.newStatus
    }
  })
}

const findAnnexPendingByKunnAndRequestId = async function (debtAckContractNumber, requestId) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      request_id: requestId
    }
  })
}

const findAnnexPendingByKunnNumber = async function (
  debtAckContractNumber,
  status = constant.ANNEX_PENDING_STATUS.PENDING
) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      status
    }
  })
}
module.exports = {
  insertLoanAnnexPending,
  findListAnnexPendingByDate,
  updateStatusAnnexPending,
  findAnnexPendingByKunnAndRequestId,
  findAnnexPendingByKunnNumber,
  updateCancelNotActiveAnnexPending
}
