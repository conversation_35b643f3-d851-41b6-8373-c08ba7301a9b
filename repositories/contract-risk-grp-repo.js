const baseRepo = require('./base.repo')

const tableName = 'contract_risk_grp'

const findContractRiskGrpByDebtAckContractNumber = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
}

const findContractRiskGrpByCustId = async function (custId) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      cust_id: custId
    }
  })
}

const updateContractRiskGrpByCustId = async function (custId, cicRiskGrp, importDate, cicImportId) {
  const sql = `update contract_risk_grp c
    set cic_risk_grp = $2, final_risk_grp = GREATEST (c.individual_risk_grp,$2),cic_import_date = $3, cic_risk_grp_import_id= $4
  from loan_account la where la.cust_id = $1 and la.debt_ack_contract_number = c.debt_ack_contract_number  and la.status = 1 returning *`
  const result = await global.poolWrite.query(sql, [custId, cicRiskGrp, importDate, cicImportId])
  return result.rows
}

const updateContractRgByDebtAckContractNumber = async function (payload) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: payload.debtAckContractNumber,
      contract_number: payload.contractNumber
    },
    fieldsValueObj: {
      user_risk_grp: payload.userRgValue,
      cont_risk_grp: payload.conRgValue,
      individual_risk_grp: payload.indRgValue,
      final_risk_grp: payload.finRgValue
    },
    isUpdatedTime: true
  })
}
const findContractRiskGrp = async function (poolRead, payload) {
  let sql = 'select * from contract_risk_grp crg where 1 = 1 '
  const params = []
  let i = 1
  if (payload.contractNumber != undefined) {
    sql = sql + 'and contract_number = $' + i
    params.push(payload.contractNumber)
    i++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql = sql + 'and debt_ack_contract_number = $' + i
    params.push(payload.debtAckContractNumber)
    i++
  }
  if (payload.custId != undefined) {
    sql = sql + 'and cust_id = $' + i
    params.push(payload.custId)
    i++
  }

  return await poolRead.query(sql, params)
}
const findContractRiskGrpMQueue = async function (poolRead, payload) {
  let sql = 'select * from contract_risk_grp crg where 1 = 1 '
  const params = []
  let i = 1
  if (payload.contractNumber != undefined) {
    sql = sql + 'and contract_number = $' + i
    params.push(payload.contractNumber)
    i++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql = sql + 'and debt_ack_contract_number = $' + i
    params.push(payload.debtAckContractNumber)
    i++
  }
  if (payload.custId != undefined) {
    sql = sql + 'and cust_id = $' + i
    params.push(payload.custId)
    i++
  }
  return await poolRead.query(sql, params)
}
const insContractRiskGrp = async function (poolWrite, pl) {
  const sql = `INSERT INTO contract_risk_grp 
    (contract_number, cust_id, dpd_risk_grp, start_date, day_over_due, obs_risk_grp, user_risk_grp, user_update_date, cont_risk_grp, individual_risk_grp, cic_risk_grp, cic_import_date, final_risk_grp, created_date, created_user, owner_id, job_detail_id, debt_ack_contract_number,dpd_num_day,cal_dpd_date,dpd_strategy,observation_time)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, now(), $14, $15, $16, $17,$18,$19,$20,$21) returning *`
  return await poolWrite.query(sql, [
    pl.contractNumber,
    pl.custId,
    pl.dpdRiskGrp,
    pl.startDate,
    pl.dayOverdue,
    pl.obsRiskGrp,
    pl.userRiskGrp,
    pl.userUpdateDate,
    pl.contRiskGrp,
    pl.individualRiskGrp,
    pl.cicRiskGrp,
    pl.cicImportDate,
    pl.finalRiskGrp,
    pl.createdUser,
    pl.ownerId,
    pl.jobDetailId,
    pl.debtAckContractNumber,
    pl.numDayDpd,
    pl.calDate,
    pl.dpdStrategy,
    pl.observationTime
  ])
}
const updateRiskGrpByContractNumberAndDebtAckContract = async function (poolWrite, pl) {
  const sql = `UPDATE contract_risk_grp
    SET dpd_risk_grp=$3, obs_risk_grp=$4, user_risk_grp=$5, user_update_date=$6, cont_risk_grp=$7, individual_risk_grp=$8, final_risk_grp=$9,dpd_num_day =$10,cal_dpd_date=$11,dpd_strategy = $12,observation_time = $13, cic_risk_grp= $14, updated_date= now()
    WHERE contract_number= $1 and debt_ack_contract_number= $2`
  return await poolWrite.query(sql, [
    pl.contractNumber,
    pl.debtAckContractNumber,
    pl.dpdRiskGrp,
    pl.obsRiskGrp,
    pl.userRiskGrp,
    pl.userUpdateDate,
    pl.contRiskGrp,
    pl.individualRiskGrp,
    pl.finalRiskGrp,
    pl.numDayDpd,
    pl.calDate,
    pl.dpdStrategy,
    pl.observationTime,
    pl.cicRiskGrp
  ])
}

const updateUserRiskGrpByDebtAckContract = async function (poolWrite, pl) {
  const sql = `UPDATE contract_risk_grp
    SET user_risk_grp=$2, cont_risk_grp =$3,individual_risk_grp = $4, final_risk_grp = $5, updated_date= now()
    WHERE debt_ack_contract_number= $1 returning *`
  return await poolWrite.query(sql, [
    pl.debtAckContractNumber,
    pl.userRiskGrp,
    pl.contRiskGrp,
    pl.individualRiskGrp,
    pl.finalRiskGrp
  ])
}
const updateCicRiskGrpByDebtAckContract = async function (poolWrite, pl) {
  const sql = `UPDATE contract_risk_grp
    SET cic_risk_grp =$1,cic_import_date=$2,cic_risk_grp_import_id=$5,final_risk_grp=$6, updated_date= now()
    WHERE debt_ack_contract_number= $3 and cust_id =$4 returning *`
  return await poolWrite.query(sql, [
    pl.cicRiskGrp,
    pl.cicImportDate,
    pl.debtAckContractNumber,
    pl.custId,
    pl.cicRiskGrpImportId,
    pl.finalRiskGrp
  ])
}
const findContRiskGrpInDebt = async function (poolRead, ids) {
  let sql = 'select * from contract_risk_grp crg WHERE debt_ack_contract_number in ( '
  let idx = 1
  const params = []
  for (const i in ids) {
    sql += '$' + idx + ' ,'
    idx++
    params.push(ids[i])
  }
  sql = sql.substring(0, sql.length - 1) + ')'
  // console.log('sql: ',sql)
  return await poolRead.query(sql, params)
}

const findCustIdHaveCic = async function () {
  const sql = 'select distinct (cust_id) from contract_risk_grp crg where cic_risk_grp > 1'

  const result = await global.poolRead.query(sql, [])
  return result.rows
}

const updateContractRgByDebtAckContractNumberV2 = async function (payload) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: payload.debtAckContractNumber
    },
    fieldsValueObj: {
      dpd_risk_grp: payload.dpdRiskGrp,
      obs_risk_grp: payload.obsRiskGrp,
      cont_risk_grp: payload.contRiskGrp,
      individual_risk_grp: payload.individualRiskGrp,
      final_risk_grp: payload.finalRiskGrp,
      dpd_num_day: payload.numDayDpd,
      dpd_strategy: payload.dpdStrategy
    },
    isUpdatedTime: true
  })
}

const deleteByDebtAckContractNumber = async function (debt_ack_contract_number) {
  console.log('deleteByDebtAckContractNumber:', debt_ack_contract_number)
  const sql = 'delete from contract_risk_grp where debt_ack_contract_number = $1;'
  try {
    return Promise.all([
      global.poolWrite.query(sql, [debt_ack_contract_number]),
    ])
  } catch (error) {
    console.log('Error deleteByDebtAckContractNumber', error)
  }
}

module.exports = {
  deleteByDebtAckContractNumber,
  findContractRiskGrp,
  insContractRiskGrp,
  updateRiskGrpByContractNumberAndDebtAckContract,
  updateUserRiskGrpByDebtAckContract,
  updateCicRiskGrpByDebtAckContract,
  findContRiskGrpInDebt,
  findContractRiskGrpMQueue,
  findContractRiskGrpByDebtAckContractNumber,
  findContractRiskGrpByCustId,
  updateContractRgByDebtAckContractNumber,
  updateContractRiskGrpByCustId,
  findCustIdHaveCic,
  updateContractRgByDebtAckContractNumberV2
}
