const { RISK_GROUP, FLAG_NOT_ACTIVE, FLAG_ACTIVE } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'loan_risk_grp'

async function insertManyContractRg(payload) {
  const fieldsValueObj = {
    contract_number: payload.contractNumber,
    debt_ack_contract_number: payload.debtAckContractNumber,
    cust_id: payload.custId,
    risk_type: RISK_GROUP.DPD_RG,
    risk_date: payload.riskDate || 'now()',
    risk_grp_val: payload.dpdRgValue,
    dpd: payload.dpd,
    created_user: payload.createdUser || 'system',
    created_date: payload.riskDate || 'now()',
    updated_date: payload.riskDate || 'now()',
    owner_id: payload.ownerId || 1
  }
  const arrayFieldsValueObj = []

  if (payload.dpdRgValue) {
    arrayFieldsValueObj.push(fieldsValueObj)
  }

  if (payload.obsRgValue) {
    arrayFieldsValueObj.push({
      ...fieldsValueObj,
      risk_type: RISK_GROUP.OBSER_RG,
      risk_grp_val: payload.obsRgValue
    })
  }

  if (payload.conRgValue) {
    arrayFieldsValueObj.push({
      ...fieldsValueObj,
      risk_type: RISK_GROUP.CONTRACT_RG,
      risk_grp_val: payload.conRgValue
    })
  }

  if (payload.userRgValue) {
    arrayFieldsValueObj.push({
      ...fieldsValueObj,
      risk_type: RISK_GROUP.USER_RG,
      risk_grp_val: payload.userRgValue
    })
  }

  return await baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj
  })
}

async function findLastestContractRgTypeByDebtAckContractNumber(debtAckContractNumber, riskType, limit = 1) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      risk_type: riskType,
      flag_active: FLAG_ACTIVE
    },
    queryOrderArr: ['risk_date', 'DESC', 'risk_id', 'DESC'],
    limit
  })
}

async function deActiveByDebtAckContractNumberMoreDate(debtAckContractNumber, riskDate) {
  const params = [debtAckContractNumber, riskDate, FLAG_NOT_ACTIVE]
  const sqlQuery = `update ${tableName} set flag_active = $3, updated_date=now() where debt_ack_contract_number= $1 and risk_date >= $2`

  return await global.poolWrite.query(sqlQuery, params)
}

const deleteByDebtAckContractNumber = async function (debt_ack_contract_number) {
  console.log('deleteByDebtAckContractNumber:', debt_ack_contract_number)
  const sql = 'delete from loan_risk_grp where debt_ack_contract_number = $1;'
  try {
    return Promise.all([
      global.poolWrite.query(sql, [debt_ack_contract_number]),
    ])
  } catch (error) {
    console.log('Error deleteByDebtAckContractNumber', error)
  }
}

module.exports = {
  deleteByDebtAckContractNumber,
  insertManyContractRg,
  findLastestContractRgTypeByDebtAckContractNumber,
  deActiveByDebtAckContractNumberMoreDate
}
