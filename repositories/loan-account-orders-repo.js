const baseRepo = require('./base.repo')

const tableName = 'loan_account_orders'

// CREATE TABLE public.loan_account_orders (
// 	loan_account_order_id serial4 NOT NULL,
// 	debt_ack_contract_number varchar(200) NULL,
// 	order_index int4 NULL,
// 	order_number varchar(100) NULL,
// 	prin_amt numeric(20, 3) NULL,
// 	order_amt numeric(20, 3) NULL,
// 	payment_date timestamp NULL,
// 	created_date timestamp DEFAULT now() NULL,
// 	updated_date timestamp NULL,
// 	CONSTRAINT "PK_LAOI" PRIMARY KEY (loan_account_order_id)
// );

const insertLoanAccountOrder = async function (payload) {
  return await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      order_index: payload.orderIndex,
      order_number: payload.orderNumber,
      prin_amt: payload.prinAmt,
      order_amt: payload.orderAmt,
      payment_date: payload.paymentDate,
      debt_ack_contract_number: payload.debtAckContractNumber
    }
  })
}
const findOrderByKunnNumber = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
}
module.exports = {
  insertLoanAccountOrder,
  findOrderByKunnNumber
}
