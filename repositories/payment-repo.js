const { PAYMENT } = require('../utils/constant')
const { MoreThanOrEqual } = require('../utils/find-operator')
const baseRepo = require('./base.repo')

const tableName = 'payment'

async function updateNonAllocatedAmtById(data) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      id: data.payId
    },
    fieldsValueObj: {
      non_allocated_amt: data.nonAllocatedAmt,
      comments: data.comments,
      tran_status: data.tranStatus
    },
    isUpdatedTime: true
  })
}
async function findPaymentById(payId) {
  const result = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      id: payId
    }
  })
  if (result.length) {
    return result[0]
  }
  return null
}
async function insertPayment(payload) {
  try {
    const result = await baseRepo.baseInsert({
      tableName,
      fieldsValueObj: {
        installment_amort: payload.installmentAmort,
        partner_code: payload.partnerCode,
        payment_date: payload.paymentDate,
        status: payload.status,
        owner_id: payload.ownerId,
        is_testing: payload.isTesting,
        created_by: payload.createdBy,
        contract_number: payload.contractNumber,
        debt_ack_contract_number: payload.debtAckContractNumber,
        non_allocated_amt: payload.installmentAmort,
        reference: payload.reference,
        ccycd: payload.ccycd,
        bank_code: payload.bankCode,
        bank_name: payload.bankName,
        bank_account: payload.bankAccount,
        partner_tran_no: payload.transactionId,
        comments: payload.comments,
        comment1: payload.comment1,
        comment2: payload.comment2,
        original_pay_id: payload.originalPayId,
        tran_id: payload.tranId,
        tran_status: payload.tranStatus,
        pay_type: payload.payType,
        customer_name: payload.customerName,
        value_date: payload.valueDate
      },
      isReturn: true
    })
    return result.rows?.[0]
  } catch (error) {
    console.log('error insert payment', JSON.stringify(error))
    return null
  }
}
const insPayment = async function (payload) {
  try {
    const sql = `INSERT INTO payment
    (installment_amort, partner_code, payment_date, status, owner_id, is_testing, created_by,contract_number,debt_ack_contract_number,non_allocated_amt,reference,ccycd,bank_code,bank_name,bank_account,partner_tran_no,comments,comment1,comment2,original_pay_id, tran_id, tran_status, pay_type,customer_name,value_date)
    VALUES($1, $2, $3, $4, $5, $6, $7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24,$25) returning *`
    return await global.poolWrite.query(sql, [
      payload.installmentAmort,
      payload.partnerCode,
      payload.paymentDate,
      payload.status,
      payload.ownerId,
      payload.isTesting,
      payload.createdBy,
      payload.contractNumber,
      payload.debtAckContractNumber,
      payload.installmentAmort,
      payload.reference,
      payload.ccycd,
      payload.bankCode,
      payload.bankName,
      payload.bankAccount,
      payload.transactionId,
      payload.comments,
      payload.comment1,
      payload.comment2,
      payload.originalPayId,
      payload.tranId,
      payload.tranStatus,
      payload.payType,
      payload.customerName,
      payload.valueDate
    ])
  } catch (error) {
    console.log('error', error.message)
    return {
      rowCount: 0
    }
  }
}
const getAllByDebtAckContractNumber = async function (poolRead, payload) {
  const sql = 'select * FROM payment where debt_ack_contract_number = $1 and status = 1 order by id desc'
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}

const getAllPaymentByDebtAckContractNumber = async function (poolRead, payload) {
  const sql = 'select * FROM payment where debt_ack_contract_number = $1 and installment_amort > 0 order by id desc'
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}

const getAllPaymentByCustId = async function (pl) {
  let sql =
    'select p.* from loan_account la join payment p on la.debt_ack_contract_number = p.debt_ack_contract_number \
  where la.cust_id = $1 and p.installment_amort > 0'
  const arrParams = [pl.custId]
  let idx = 1
  if (pl.debtAckContractNumber) {
    sql += ` and la.debt_ack_contract_number = $${++idx}`
    arrParams.push(pl.debtAckContractNumber)
  }
  if (pl.contractType) {
    sql += ` and la.contract_type = $${++idx}`
    arrParams.push(pl.contractType)
  }
  sql += ' order by p.id desc'
  return await global.poolRead.query(sql, arrParams)
}

const getSumInstallmentAmort = async function (poolRead, payload) {
  const sql =
    'select sum(installment_amort) as "sumInstallmentAmort" FROM payment where debt_ack_contract_number = $1 and status = 1;'
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}

const updateRemainAmort = async function (poolWrite, pl) {
  const sql = `UPDATE payment SET non_allocated_amt = non_allocated_amt - $2, updated_date=now() 
    WHERE id = $1 and non_allocated_amt - $2 >= 0`
  return await poolWrite.query(sql, [pl.paymentId, pl.deltaAmort])
}
const updatetranLogInfo = async function (poolWrite, pl) {
  const sql = `UPDATE payment SET tran_id = $2,tran_date = $3, value_date = $4, updated_date=now() 
    WHERE id = $1`
  return await poolWrite.query(sql, [pl.paymentId, pl.tranId, pl.tranDate, pl.valueDate])
}
const getRemainAmortByContractNumber = async function (pl) {
  let sql = 'select * FROM payment where non_allocated_amt > 0 and status = 1'
  const params = []
  let idx = 1
  if (pl.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(pl.contractNumber)
    idx++
  }
  if (pl.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(pl.debtAckContractNumber)
    idx++
  }
  const result = await global.poolRead.query(sql, params)
  return result.rows
}
const getRemainAmortByContractNumberV2 = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      'not non_allocated_amt': 0,
      debt_ack_contract_number: debtAckContractNumber,
      status: PAYMENT.STATUS.ACTIVE
    },
    queryOrderArr: ['id', 'asc'],
    poolConnect: global.poolWrite
  })
}

const getListPaymentDetailByDebtAckContractNumber = async function ({
  debtAckContractNumber,
  fromDate,
  createdDate,
  toDate,
  payId
}) {
  let customQuery = ''
  const params = [debtAckContractNumber]
  if (fromDate != null && createdDate != null) {
    params.push(fromDate)
    params.push(createdDate)
    customQuery += ` and p.value_date >= $${params.length - 1} and pd.created_date >= $${params.length}`
  }
  if (toDate != null) {
    params.push(toDate)
    customQuery += ` and p.payment_date <= $${params.length}`
  }
  if (payId != null) {
    params.push(payId)
    customQuery += ` and p.id = $${params.length}`
  }
  const sql = `SELECT pd.id,pd.bill_id , pd.amount , payment_date , payment_id ,bod.installment_id, i.type, i.description , bod.is_annex, bod.annex_number, pd.created_date, installment_amort from payment p 
              left join payment_detail pd on p.id = pd.payment_id 
              join bill_on_due bod on bod.id = pd.bill_id 
              join installment i on i.id  = bod.installment_id 
              where p.debt_ack_contract_number = $1 and p.cancel_date is null ${customQuery} and pd.flag_active = 1 order by payment_date asc`
  const result = await global.poolRead.query(sql, params)
  return result.rows
}
const findById = async function (poolRead, payload) {
  const sql = 'select * FROM payment where id = $1 '
  return await poolRead.query(sql, [payload.paymentId])
}

const getSystemDate = async function (poolRead) {
  const sql = 'select now()'
  return await poolRead.query(sql, [])
}
async function findAllReconcili(poolRead, pl) {
  try {
    let sql = 'select * from payment where status = 1'
    const params = []
    let idx = 1
    if (pl.mode && pl.mode == 1) {
      sql += ' and payment_reconciled = 0'
    }
    if (pl.contractNumber) {
      sql += ' and debt_ack_contract_number = $' + idx
      params.push(pl.contractNumber)
      idx++
    }
    if (pl.payId) {
      sql += ' and reference = $' + idx
      params.push(pl.payId)
      idx++
    }
    if (pl.fromDate) {
      sql += ' and payment_date >= $' + idx
      params.push(pl.fromDate)
      idx++
    }
    if (pl.toDate) {
      sql += ' and payment_date < $' + idx
      params.push(pl.toDate)
      idx++
    }
    if (pl.partnerCode) {
      sql += ' and partner_code = $' + idx
      params.push(pl.partnerCode)
      idx++
    }
    if (pl.transactionId) {
      sql += ' and partner_tran_no = $' + idx
      params.push(pl.transactionId)
      idx++
    }
    return await poolRead.query(sql, params)
  } catch (e) {
    console.log('Error at payment findAllReconcili:', e.message)
    console.log(e)
  }
}
async function updatePaymentReconciled(poolWrite, payIds) {
  try {
    const dateNow = new Date()
    let sql = 'UPDATE payment set updated_date=$1, payment_reconciled = 1 WHERE partner_tran_no in ('
    let idx = 2
    const params = [dateNow]
    for (const i in payIds) {
      sql += '$' + idx + ' ,'
      idx++
      params.push(payIds[i])
    }
    sql = sql.substring(0, sql.length - 1) + ')'
    sql += ' returning *'
    return await poolWrite.query(sql, params)
  } catch (e) {
    console.log('Error at payment findAllReconcili:', e.message)
    console.log(e)
  }
}

async function findByCondition(pl) {
  try {
    let sql =
      'select p.id as pay_id, pd.amount as pd_amount,* from payment p left join payment_detail pd on p.id = pd.payment_id and pd.flag_active = 1 left join bill_on_due bod on pd.bill_id = bod.id where p.status = 1'
    const params = []
    let idx = 1
    if (pl.partnerCode) {
      sql += ' and partner_code = $' + idx
      params.push(pl.partnerCode)
      idx++
    }
    if (pl.contractNumber) {
      sql += ' and p.debt_ack_contract_number = $' + idx
      params.push(pl.contractNumber)
      idx++
    }
    if (pl.transactionId) {
      sql += ' and partner_tran_no = $' + idx
      params.push(pl.transactionId)
      idx++
    }
    if (pl.fromDate) {
      sql += ' and tran_date >= $' + idx
      params.push(pl.fromDate)
      idx++
    }
    if (pl.toDate) {
      sql += ' and tran_date < $' + idx
      params.push(pl.toDate)
      idx++
    }
    sql += ' order by tran_date desc'
    return await global.poolRead.query(sql, params)
  } catch (e) {
    console.log('Error at payment findByCondition:', e.message)
    console.log(e)
  }
}

const getAllByDebtAckContractNumberMqueue = async function (poolRead, payload, currentDate = new Date()) {
  const sql =
    'select * FROM payment where debt_ack_contract_number = $1 and status = 1 and updated_date::date >= $2::date;'
  return await poolRead.query(sql, [payload.debtAckContractNumber, currentDate])
}

const getLastPaymentAmt = async function (poolRead, payload) {
  const sql =
    'select * FROM payment where debt_ack_contract_number = $1 and status = 1 and installment_amort > 0 order by id desc;'
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}

const getListPaymentFromDateToDate = async function (payload) {
  let customQuery = ''
  const params = [payload.debtAckContractNumber]
  if (payload.fromDate != null) {
    params.push(payload.fromDate)
    customQuery += `and payment_date >= $${params.length}`
  }
  if (payload.toDate != null) {
    params.push(payload.toDate)
    customQuery += `and payment_date <= $${params.length}`
  }
  const sql = `select * FROM payment where debt_ack_contract_number = $1 and status = 1 ${customQuery} order by payment_date asc`
  const result = await global.poolRead.query(sql, params)
  return result.rows
}

async function updatePaymentCanceled(payload) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      id: payload.payId
    },
    fieldsValueObj: {
      cancel_date: payload.cancelDate,
      status: payload.status,
      comments: `${payload.createdUser} CANCELED PAYMENT`,
      non_allocated_amt: Number(payload.nonAllocatedAmt)
    }
  })
}
async function updatePaymentNonAllocationAmount(payload) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      id: payload.payId
    },
    fieldsValueAddObj: {
      non_allocated_amt: payload.nonAllocatedAmt
    },
    isReturn: true
  })
}

const updateLastAllocationDate = async function (listPaymentIds = []) {
  try {
    if (listPaymentIds.length) {
      const idJoinStr = listPaymentIds.join(',')
      const sql = `UPDATE payment SET last_allocation_date = $1, updated_date = now() WHERE id in (${idJoinStr})`
      const params = [new Date()]
      return await global.poolWrite.query(sql, params)
    }
  } catch (error) {
    console.log('ERROR updateLastAllocationDate', error)
    throw error
  }
}

const getListPaymentMoreDate = async function (debtAckContractNumber, valueDate) {
  return await baseRepo.baseSelectV2({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      status: PAYMENT.STATUS.ACTIVE,
      value_date: MoreThanOrEqual(valueDate)
    },
    queryOrderObj: {
      value_date: 'ASC'
    },
    poolConnect: global.poolWrite
  })
}

module.exports = {
  insPayment,
  getAllByDebtAckContractNumber,
  updateRemainAmort,
  getRemainAmortByContractNumber,
  findById,
  findByCondition,
  updatetranLogInfo,
  getSystemDate,
  findAllReconcili,
  updatePaymentReconciled,
  getAllByDebtAckContractNumberMqueue,
  getLastPaymentAmt,
  getSumInstallmentAmort,
  findPaymentById,
  insertPayment,
  updateNonAllocatedAmtById,
  getRemainAmortByContractNumberV2,
  getListPaymentDetailByDebtAckContractNumber,
  getListPaymentFromDateToDate,
  updatePaymentCanceled,
  updatePaymentNonAllocationAmount,
  getAllPaymentByDebtAckContractNumber,
  getAllPaymentByCustId,
  updateLastAllocationDate,
  getListPaymentMoreDate
}
