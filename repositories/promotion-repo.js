const baseRepo = require('./base.repo')

const tableName = 'promotion'

const insertPromotion = async function (payload) {
  return await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      promotion_code: payload.promotionCode,
      promotion_type: payload.promotionType,
      promotion_name: payload.promotionName,
      promotion_value: payload.promotionValue,
      promotion_max_value: payload.promotionMaxValue,
      total_promotion_amount: payload.totalPromotionAmount,
      deduction_amount: payload.deductionAmount,
      revenue_amount: payload.revenueAmount,
      debt_ack_contract_number: payload.debtAckContractNumber
    }
  })
}
const updatePromotion = async function (payload) {
  return await baseRepo.baseUpdate({
    tableName,
    fieldsValueAddObj: {
      deduction_amount: -payload.revenueAmount,
      revenue_amount: payload.revenueAmount
    },
    queryWhereObj: {
      debt_ack_contract_number: payload.debtAckContractNumber
    }
  })
}

const findPromotionByDebtAckContractNumber = async function (debtAckContractNumber) {
  const res = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
  if (res.length) {
    return res[0]
  }
  return {}
}
module.exports = {
  insertPromotion,
  updatePromotion,
  findPromotionByDebtAckContractNumber
}
