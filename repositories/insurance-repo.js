const { FLAG_ACTIVE_INIT, FLAG_ACTIVE } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'insurance'

const insertInsurance = async function (payload) {
  const result = await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: payload,
    isReturn: true
  })
  if (result.rows.length) {
    return result.rows[0]
  }
  return null
}

const getInitInsurance = async function (debtAckContractNumber) {
  return baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: FLAG_ACTIVE_INIT
    }
  })
}

const activeInsurance = async function (insur_id, payload) {
  return await baseRepo.baseUpdate({
    tableName,
    fieldsValueObj: payload,
    queryWhereObj: {
      insur_id
    },
    isReturn: true,
    isUpdatedTime: true
  })
}
const getInsuranceActive = async function (debtAckContractNumber) {
  return baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: FLAG_ACTIVE
    }
  })
}
module.exports = {
  insertInsurance,
  activeInsurance,
  getInitInsurance,
  getInsuranceActive
}
