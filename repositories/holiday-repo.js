const { DATE_FORMAT } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'holiday'
const moment = require('moment')

const insertListHoliday = async function (listHoliday, createdBy) {
  const arrayFieldsValueObj = listHoliday.map((holiday) => {
    return {
      created_user: createdBy,
      holiday_date: holiday
    }
  })
  return await baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj
  })
}

const getListHolidayAfterDayCheck = async function (dateCheck) {
  const sqlQuery =
    'select start_day,end_date,delta_month from holiday where holiday_date::date >= $1::date and flag_active = 1'
  const res = await global.poolRead.query(sqlQuery, [dateCheck])
  return res.rows
}

const getListHoliday = async function () {
  const result = await baseRepo.baseReadAll({
    tableName,
    selectQuery: 'holiday_date'
  })
  return result.map((item) => moment(item.holiday_date).format(DATE_FORMAT.YYYYMMDD2))
}
module.exports = {
  insertListHoliday,
  getListHolidayAfterDayCheck,
  getListHoliday
}
