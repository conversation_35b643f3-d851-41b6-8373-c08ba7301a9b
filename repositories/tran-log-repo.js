const insTranLog = async function (poolWrite, pl) {
  const sql = `INSERT INTO tran_log 
    (contract_number, loan_id, tran_type, ref_id, amt_number, tran_date, value_date, tran_desc, tran_status,
          created_user, session_id, owner_id)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12) returning *`
  return await poolWrite.query(sql, [
    pl.contractNumber,
    pl.loanId,
    pl.tranType,
    pl.refId,
    pl.amtNumber,
    pl.tranDate,
    pl.valueDate,
    pl.tranDesc,
    pl.tranStatus,
    pl.createdUser,
    pl.sessionId,
    pl.ownerId
  ])
}

module.exports = {
  insTranLog
}
