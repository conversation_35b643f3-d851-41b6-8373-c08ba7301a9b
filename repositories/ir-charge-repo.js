const format = require('pg-format')
const { IR_CHARGE_STATUS } = require('../utils/constant')

const baseRepo = require('./base.repo')

const tableName = 'ir_charge'

const insIrCharge = async function (poolWrite, payload) {
  const sql = `INSERT INTO ir_charge 
    (mc_limit_id, product_code, ir_code, ir_name, ir_type, ir_value, status, owner_id, is_testing, created_by) 
    VALUES($1, $2, $2, $3, $4, $5, $6, $7, $8, $9) returing *`
  return await poolWrite.query(sql, [
    payload.mcLimitId,
    payload.productCode,
    payload.irCode,
    payload.irName,
    payload.irType,
    payload.irValue,
    payload.status,
    payload.ownerId,
    payload.isTesting,
    payload.createdBy
  ])
}
const insBatchIrCharge = async function (records) {
  try {
    const sql = format(
      `INSERT INTO ir_charge 
    (debt_ack_contract_number,mc_limit_id, product_code, ir_code, ir_name, ir_type, ir_value, status, owner_id, is_testing, created_by , tenor_from, tenor_to, instal_from ,instal_to ) 
    VALUES %L`,
      records
    )
    return await global.poolWrite.query(sql)
  } catch (error) {
    console.log('ERROR insBatchIrCharge')
    throw error
  }
}

const findByMcLimitIdAndProductCode = async function (poolRead, payload) {
  const sql = `SELECT id, mc_limit_id, product_code, ir_code, ir_name, ir_type, ir_value, status, owner_id,
     is_testing, created_date, updated_date, created_by
    FROM ir_charge where mc_limit_id = $1 and product_code = $2`
  return await poolRead.query(sql, [payload.mcLimitId, payload.productCode])
}
const findByDebtAckContractNumberAndProductCode = async function (poolRead, payload) {
  let sql = `SELECT *
    FROM ir_charge where status = 1`
  const params = []
  let idx = 1
  if (payload.productCode != undefined) {
    sql += ' and product_code = $' + idx
    params.push(payload.productCode)
    idx++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  if (payload.irType) {
    sql += ' and ir_type = $' + idx
    params.push(payload.irType)
    idx++
  }
  return await poolRead.query(sql, params)
}

const findByDebtAckContractNumberAndProductCodeV2 = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      status: IR_CHARGE_STATUS.ACTIVE
    }
  })
}
async function updateStatusIrCharge(debtAckContractNumber) {
  const sql = `update ir_charge
        set status = 0, updated_date = now()
        where debt_ack_contract_number = $1 and ir_type <= 2`

  return await global.poolWrite.query(sql, [debtAckContractNumber])
}
const updateIrValue = async function (poolWrite, payload) {
  const sql = `update ir_charge
        set ir_value = $1, updated_date = now()
        where debt_ack_contract_number = $2 and product_code = $3 and ir_type = 1`

  return await poolWrite.query(sql, [payload.irValue, payload.debtAckContractNumber, payload.productCode])
}

module.exports = {
  insIrCharge,
  findByMcLimitIdAndProductCode,
  insBatchIrCharge,
  findByDebtAckContractNumberAndProductCode,
  findByDebtAckContractNumberAndProductCodeV2,
  updateIrValue,
  updateStatusIrCharge
}
