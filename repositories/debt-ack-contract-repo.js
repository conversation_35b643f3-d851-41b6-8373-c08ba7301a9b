const { PAYMENT_STATUS, DEBT_ACK_STATUS, DEFAULT_DPD } = require('../utils/constant')

const insDebtAckContract = async function (poolWrite, payload) {
  const sql = `INSERT INTO loan_account
    (product_code, apr_limit_amt, tenor, start_date, end_date, noti_date_num, product_child_code, status, 
        contract_number, hold_money, active_date, owner_id, is_testing, created_date, updated_date, 
        created_by, mc_limit_id,contract_limit_id,prin_amt,debt_ack_contract_number,cust_id,periodicity,
        grace_day_number,bill_day,ccycd,contract_type,partner_code,phone_number,approval_date,signing_in_progress_date,
        signature_date, channel, disbursement_type,dpd,dpd_strategy, order_amt)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, now(), now(), $14, $15,$15,$2,$16,$17,$18,$19,$20,$21,
    $22,$23,$24, now(), now(), now(), $25, $26, $27, $27, $28) returning *`
  // let sql ='INSERT INTO debt_ack_contract\
  // (product_code, amount, tenor, start_date, end_date, noti_date_num, product_child_code, status, \
  //     contract_number, hold_money, active_date, owner_id, is_testing, created_date, updated_date, \
  //     created_by, mc_limit_id)\
  // VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, now(), now(), $14, $15) returning *';
  return await poolWrite.query(sql, [
    payload.productCode,
    payload.amount,
    payload.tenor,
    payload.startDate,
    payload.endDate,
    payload.notiDateNum,
    payload.productChildCode,
    payload.status,
    payload.contractNumber,
    payload.holdMoney,
    payload.activeDate,
    payload.ownerId,
    payload.isTesting,
    payload.createdBy,
    payload.contractLimitId,
    payload.debtAckContractNumber,
    payload.custId,
    payload.periodicity,
    payload.graceDayNumber,
    payload.billDay,
    payload.ccycd,
    payload.contractType,
    payload.partnerCode,
    payload.phoneNumber,
    payload.channel,
    payload.disbursementType,
    DEFAULT_DPD,
    payload.orderAmt,
  ])
}
const activeDebtAckContract = async function (poolWrite, payload) {
  const sql = `UPDATE loan_account
    SET start_date= $2,status='1', active_date= $2, end_date = $3, updated_date=now()
    WHERE loan_id = $1 and status = '0' returning *`
  return await poolWrite.query(sql, [payload.debtAckContractId, payload.startDate, payload.endDate])
}
const findByContractNumberAndStatus = async function (poolRead, payload) {
  let sql = 'SELECT * FROM loan_account where 1 = 1'
  let idx = 1
  const params = []
  if (payload.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(payload.contractNumber)
    idx++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  if (payload.status != undefined) {
    sql += ' and status = $' + idx
    params.push(payload.status)
    idx++
  }
  if (payload.custId) {
    sql += ' and cust_id = $' + idx
    params.push(payload.custId)
    idx++
  }
  return await poolRead.query(sql, params)
}

const findByContractNumberAndStatusMQueue = async function (poolRead, payload) {
  let sql = 'SELECT * FROM loan_account where 1 = 1 '
  let idx = 1
  const params = []
  if (payload.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(payload.contractNumber)
    idx++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  return await poolRead.query(sql, params)
}

const updateMoneyHold = async function (poolWrite, payload) {
  const sql = 'UPDATE loan_account SET hold_money = hold_money + $1 WHERE loan_id= $2'
  // let sql='UPDATE public.debt_ack_contract SET hold_money = hold_money + $1 WHERE id= $2'
  return await poolWrite.query(sql, [payload.holdMoney, payload.debtAckContractId])
}
const findByContractNumberAndPaymentStatus = async function (poolRead, payload) {
  const sql = 'SELECT * FROM loan_account where contract_number = $1 and payment_status = $2'
  return await poolRead.query(sql, [payload.contractNumber, payload.paymentStatus])
}
const findByContractNumberAndPaymentStatusNotCompleted = async function (poolRead, payload) {
  const sql = 'SELECT * FROM loan_account where contract_number = $1 and payment_status <> $2 and status <> $3'
  return await poolRead.query(sql, [payload.contractNumber, PAYMENT_STATUS.DONE, DEBT_ACK_STATUS.CANCEL])
}
const resetHoldMoneyWithContractNumberAndStatus = async function (poolRead, payload) {
  const sql = 'UPDATE loan_account SET hold_money = 0 where contract_number = $1 and status = $2'
  return await poolRead.query(sql, [payload.contractNumber, payload.status])
}
const findByLoanIdAndStatus = async function (poolRead, payload) {
  const sql = 'SELECT * FROM loan_account where loan_id = $1'
  return await poolRead.query(sql, [payload.loanId])
}
const getLoanAccByDebtAckContract = async function (poolRead, payload) {
  const sql = 'SELECT * FROM loan_account where debt_ack_contract_number = $1'
  return await poolRead.query(sql, [payload.debtAckContractNumber])
}
const getLoanAccByDebtAckContractMQueue = async function (poolRead, payload, currentDate = new Date()) {
  const sql = 'SELECT * FROM loan_account where debt_ack_contract_number = $1 and updated_date >= $2::date;'
  return await poolRead.query(sql, [payload.debtAckContractNumber, currentDate])
}
const updateToCollect = async function (pl) {
  const sql = 'UPDATE loan_account SET to_collect = to_collect + $1, updated_date = now() WHERE loan_id= $2'
  return await global.poolWrite.query(sql, [pl.toCollect, pl.loanId])
}
const updateNonAllAmt = async function (pl) {
  const sql =
    'UPDATE loan_account SET non_allocation_amt = non_allocation_amt + $1, updated_date = now() WHERE loan_id= $2'
  return await global.poolWrite.query(sql, [pl.nonAllocationAmt, pl.loanId])
}
const updateAmtPaid = async function (pl) {
  const sql =
    'UPDATE loan_account SET prin_paid = prin_paid + $1 , int_paid = int_paid + $2, fee_paid_amt = fee_paid_amt + $3, lpi_paid = lpi_paid + $5, prin_amt = prin_amt - $1, to_collect = to_collect + $6,' +
      ' normal_int_paid_amt = normal_int_paid_amt + $7, pref_int_paid_amt = pref_int_paid_amt + $8, factoring_fee_paid_amt = factoring_fee_paid_amt + $9, ' +
      ' updated_date = now()  WHERE loan_id= $4'
  return await global.poolWrite.query(sql, [
    pl.prinPaid,
    pl.intPaid,
    pl.feePaidAmt,
    pl.loanId,
    pl.lpiPaid,
    pl.toCollect,
    pl.normalIntPaid || 0,
    pl.preferentialIntPaid || 0,
    pl.factoringFeePaidAmt || 0,
  ])
}

const updateSuspendAmtPaid = async function (pl) {
  const sql =
      'UPDATE loan_account SET suspend_amt = suspend_amt - $1, updated_date = now()  WHERE loan_id= $2'
  return await global.poolWrite.query(sql, [
    pl.suspendAmtPaid,
    pl.loanId,
  ])
}
const updateDataIrJobAmt = async function (pl) {
  const sql =
    'UPDATE loan_account SET lpi_amt = lpi_amt + $1, to_collect = to_collect + $2, updated_date = now() WHERE loan_id= $3'
  return await global.poolWrite.query(sql, [pl.lpiAmt, pl.toCollect, pl.loanId])
}
const updateDataIrJobAmtFactoring = async function (pl) {
  const sql =
    'UPDATE loan_account SET lpi_amt = lpi_amt + $1, to_collect = to_collect + $2,int_amt = int_amt + $3,fee_amt = fee_amt + $4,factoring_fee_amt = factoring_fee_amt + $5, normal_int_amt = normal_int_amt + $6,pref_int_amt = pref_int_amt + $7, updated_date = now() WHERE loan_id= $8'
  return await global.poolWrite.query(sql, [pl.lpiAmt, pl.toCollect, pl.intAmt, pl.feeAmt, pl.factoringFee, pl.normalAmt, pl.prefIntAmt, pl.loanId])
}
const updateLoanAcc = async function (poolWrite, pl) {
  let sql = 'UPDATE loan_account SET updated_date = now()'
  let idx = 1
  const params = []
  if (pl.intAmt != undefined) {
    sql += ', int_amt = $' + idx
    params.push(pl.intAmt)
    idx++
  }
  if (pl.feeAmt != undefined) {
    sql += ', fee_amt = $' + idx
    params.push(pl.feeAmt)
    idx++
  }
  if (pl.rlsAmt != undefined) {
    sql += ', rls_amt = $' + idx
    params.push(pl.rlsAmt)
    idx++
  }
  if (pl.nonAllocationAmt != undefined) {
    sql += ', non_allocation_amt = $' + idx
    params.push(pl.nonAllocationAmt)
    idx++
  }
  sql += ' WHERE loan_id= $' + idx
  params.push(pl.loanId)
  return await poolWrite.query(sql, params)
}
const findLoanAccByPaymentStatusNotCompleted = async function (poolRead, payload) {
  let sql = 'SELECT * FROM loan_account where payment_status <> 0'
  let idx = 1
  const params = []
  if (payload.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(payload.contractNumber)
    idx++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  if (payload.status != undefined) {
    sql += ' and status = $' + idx
    params.push(payload.status)
    idx++
  }
  if (payload.custId) {
    sql += ' and cust_id = $' + idx
    params.push(payload.custId)
    idx++
  }
  return await poolRead.query(sql, params)
}
const findCollectionList = async function (poolRead, pl) {
  let sqlQuery =
    "select la.*,CASE WHEN crg.dpd_num_day = 0 THEN 'B0'\
            WHEN  crg.dpd_num_day >= 1 and crg.dpd_num_day <= 30 THEN 'B1'\
            WHEN  crg.dpd_num_day >= 31 and crg.dpd_num_day <= 60 THEN 'B2'\
            WHEN  crg.dpd_num_day >= 61 and crg.dpd_num_day <= 90 THEN 'B3'\
            WHEN  crg.dpd_num_day >= 91 and crg.dpd_num_day <= 120 THEN 'B4'\
            WHEN  crg.dpd_num_day >= 121 and crg.dpd_num_day <= 150 THEN 'B5'\
            WHEN  crg.dpd_num_day >= 151 and crg.dpd_num_day <= 180 THEN 'B6'\
            WHEN  crg.dpd_num_day >= 181 THEN 'B7'\
            ELSE '' END AS current_bucket\
        from loan_account la \
        join contract_risk_grp crg on crg.debt_ack_contract_number  = la.debt_ack_contract_number\
        where is_testing = 0 and la.status = 1 and payment_status = 0"
  let idx = 1
  const params = []
  if (pl.type == 'DEBT_SALE') {
    return {}
  }
  if (pl.bucket_type) {
    if (pl.bucket_type == 'B0') {
      sqlQuery += ' and crg.dpd_num_day = 0'
    } else if (pl.bucket_type == 'B1') {
      sqlQuery += ' and crg.dpd_num_day >= 1 and crg.dpd_num_day <= 30'
    } else if (pl.bucket_type == 'B2') {
      sqlQuery += ' and crg.dpd_num_day >= 31 and crg.dpd_num_day <= 60'
    } else if (pl.bucket_type == 'B3') {
      sqlQuery += ' and crg.dpd_num_day >= 61 and crg.dpd_num_day <= 90'
    } else if (pl.bucket_type == 'B4') {
      sqlQuery += ' and crg.dpd_num_day >= 91 and crg.dpd_num_day <= 120'
    } else if (pl.bucket_type == 'B5') {
      sqlQuery += ' and crg.dpd_num_day >= 121 and crg.dpd_num_day <= 150'
    } else if (pl.bucket_type == 'B6') {
      sqlQuery += ' and crg.dpd_num_day >= 151 and crg.dpd_num_day <= 180'
    } else if (pl.bucket_type == 'B7') {
      sqlQuery += ' and crg.dpd_num_day >= 181'
    }
  }
  if (pl.search) {
    sqlQuery += ` and (la.debt_ack_contract_number like $${idx} or la.cust_id like $${idx})`
    params.push('%' + pl.search + '%')
    idx++
  }
  if (pl.cust_id_list) {
    sqlQuery += ` and la.cust_id = ANY ($${idx})`
    params.push(pl.cust_id_list)
    idx++
  }
  // sqlQuery += ' order by dpd'
  if (pl.size) {
    sqlQuery += ' LIMIT $' + idx
    params.push(pl.size)
    idx++
  }
  if (pl.page) {
    sqlQuery += ' OFFSET $' + idx
    params.push((pl.page - 1) * pl.size)
    idx++
  }
  return await poolRead.query(sqlQuery, params)
}
const findTotalCollectionList = async function (poolRead, pl) {
  let sqlQuery = `select count(la.debt_ack_contract_number) as total_contract,\
    sum(bod.total_remain) as total_all_debt_amt,
    sum(rls_amt) - sum(prin_paid) as total_all_pri_amt \
        from loan_account la \
        join contract_risk_grp crg on crg.debt_ack_contract_number  = la.debt_ack_contract_number\
        join (select sum(remain_amount) as total_remain,debt_ack_contract_number from bill_on_due group by debt_ack_contract_number) as bod on bod.debt_ack_contract_number = la.debt_ack_contract_number
        where is_testing = 0 and la.status = 1 and la.payment_status = 0`
  let idx = 1
  const params = []
  if (pl.type == 'DEBT_SALE') {
    return {}
  }
  if (pl.bucket_type) {
    if (pl.bucket_type == 'B0') {
      sqlQuery += ' and crg.dpd_num_day = 0'
    } else if (pl.bucket_type == 'B1') {
      sqlQuery += ' and crg.dpd_num_day >= 1 and crg.dpd_num_day <= 30'
    } else if (pl.bucket_type == 'B2') {
      sqlQuery += ' and crg.dpd_num_day >= 31 and crg.dpd_num_day <= 60'
    } else if (pl.bucket_type == 'B3') {
      sqlQuery += ' and crg.dpd_num_day >= 61 and crg.dpd_num_day <= 90'
    } else if (pl.bucket_type == 'B4') {
      sqlQuery += ' and crg.dpd_num_day >= 91 and crg.dpd_num_day <= 120'
    } else if (pl.bucket_type == 'B5') {
      sqlQuery += ' and crg.dpd_num_day >= 121 and crg.dpd_num_day <= 150'
    } else if (pl.bucket_type == 'B6') {
      sqlQuery += ' and crg.dpd_num_day >= 151 and crg.dpd_num_day <= 180'
    } else if (pl.bucket_type == 'B7') {
      sqlQuery += ' and crg.dpd_num_day >= 181'
    }
  }
  if (pl.search) {
    sqlQuery += ` and (la.debt_ack_contract_number like $${idx} or la.cust_id like $${idx})`
    params.push('%' + pl.search + '%')
    idx++
  }
  if (pl.cust_id_list) {
    sqlQuery += ` and la.cust_id = ANY ($${idx})`
    params.push(pl.cust_id_list)
    idx++
  }
  // sqlQuery += ' order by dpd'
  if (pl.size) {
    sqlQuery += ' LIMIT $' + idx
    params.push(pl.size)
    idx++
  }
  if (pl.page) {
    sqlQuery += ' OFFSET $' + idx
    params.push((pl.page - 1) * pl.size)
    idx++
  }
  return await poolRead.query(sqlQuery, params)
}

module.exports = {
  insDebtAckContract,
  activeDebtAckContract,
  findByContractNumberAndStatus,
  updateMoneyHold,
  findByContractNumberAndPaymentStatus,
  findByContractNumberAndPaymentStatusNotCompleted,
  resetHoldMoneyWithContractNumberAndStatus,
  findByLoanIdAndStatus,
  getLoanAccByDebtAckContract,
  updateToCollect,
  updateAmtPaid,
  findLoanAccByPaymentStatusNotCompleted,
  updateLoanAcc,
  updateDataIrJobAmt,
  updateNonAllAmt,
  findCollectionList,
  findTotalCollectionList,
  findByContractNumberAndStatusMQueue,
  getLoanAccByDebtAckContractMQueue,
  updateSuspendAmtPaid,
  updateDataIrJobAmtFactoring
}
