const format = require('pg-format')
const { FLAG_ACTIVE, FLAG_NOT_ACTIVE } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'ir'

const insBatchIr = async function (poolWrite, records) {
  const sql = format(
    `INSERT INTO ir
    (ir_charge_id, installment_id, ir_date, ir_rate, ir_type, ir_amount, owner_id, is_testing, created_by,contract_number,debt_ack_contract_number,prin_amount)
    VALUES %L returning *`,
    records
  )
  return await poolWrite.query(sql)
}

const insIr = async function (poolWrite, payload) {
  const sql = `INSERT INTO ir 
    (ir_charge_id, installment_id, ir_date, ir_rate, ir_type, ir_amount, owner_id, is_testing, created_date, updated_date, created_by)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, now(), now(), $9) returning *`
  return await poolWrite.query(sql, [
    payload.irChargeId,
    payload.installmentId,
    payload.irDate,
    payload.irRate,
    payload.irType,
    payload.irAmount,
    payload.ownerId,
    payload.isTesting,
    payload.createdBy
  ])
}
const findByContractNumberAndIrDate = async function (payload) {
  let sql = 'SELECT * FROM ir where flag_active = 1'
  const params = []
  let idx = 1
  if (payload.contractNumber != undefined) {
    sql += ' and contract_number = $' + idx
    params.push(payload.contractNumber)
    idx++
  }
  if (payload.debtAckContractNumber != undefined) {
    sql += ' and debt_ack_contract_number = $' + idx
    params.push(payload.debtAckContractNumber)
    idx++
  }
  if (payload.irDate != undefined) {
    sql += ' and ir_date = $' + idx
    params.push(payload.irDate)
    idx++
  }
  return await global.poolRead.query(sql, params)
}
const findIrByDebtAckContractNumberAndFromToIrDate = async function (poolRead, pl) {
  const sql =
    'SELECT * FROM ir where debt_ack_contract_number = $1 and ir_type = $2 and ir_date >= $3 and ir_date <= $4 and flag_active = 1'
  return await poolRead.query(sql, [pl.debtAckContractNumber, pl.irType, pl.fromDate, pl.toDate])
}

const findLpiIrByDebtAckContractNumberAndFromIrDate = async function (payload) {
  const sql =
    'SELECT * FROM ir where debt_ack_contract_number = $1 and ir_type in (2,3) and ir_date <= $2 and flag_active = 1 order by ir_date asc'
  const result = await global.poolRead.query(sql, [payload.debtAckContractNumber, payload.toDate])

  return result.rows
}

const findListIrByDebtAckContractNumber = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: FLAG_ACTIVE
    },
    queryOrderArr: ['ir_date', 'desc']
  })
}

async function deActiveByDebtAckContractNumberMoreDate(debtAckContractNumber, irDate) {
  const params = [debtAckContractNumber, irDate, FLAG_NOT_ACTIVE]
  const sqlQuery = `update ${tableName} set flag_active = $3, updated_date=now() where debt_ack_contract_number= $1 and ir_date > $2`

  return await global.poolWrite.query(sqlQuery, params)
}

module.exports = {
  insIr,
  insBatchIr,
  findByContractNumberAndIrDate,
  findIrByDebtAckContractNumberAndFromToIrDate,
  findListIrByDebtAckContractNumber,
  findLpiIrByDebtAckContractNumberAndFromIrDate,
  deActiveByDebtAckContractNumberMoreDate
}
