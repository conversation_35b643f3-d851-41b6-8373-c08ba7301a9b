const baseRepo = require('./base.repo')

const tableName = 'voucher'

const insertVoucher = async function (payload) {
  return await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      voucher_code: payload.voucherCode,
      voucher_type: payload.voucherType,
      voucher_month: payload.voucherMonth,
      voucher_amount: payload.voucherAmount,
      voucher_partner: payload.voucherPartner,
      debt_ack_contract_number: payload.debtAckContractNumber
    }
  })
}
const findVoucherByKunnNumber = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
}
module.exports = {
  insertVoucher,
  findVoucherByKunnNumber
}
