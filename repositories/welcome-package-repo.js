async function findByContractNumber(poolRead, contractNumber, authCode, isDelete = 0) {
  try {
    const sqlQuery = 'select * from welcome_package where contract_number = $1 and is_delete=$2 and auth_code=$3'
    return await poolRead.query(sqlQuery, [contractNumber, isDelete, authCode])
  } catch (err) {
    console.log('Error at findByContractNumber WCL: ', err)
  }
}
async function findByContractNumberV2(contractNumber, isDelete = 0) {
  try {
    const sqlQuery = 'select * from welcome_package where contract_number = $1 and is_delete=$2'
    return await global.poolRead.query(sqlQuery, [contractNumber, isDelete])
  } catch (err) {
    console.log('Error at findByContractNumberV2 WCL: ', err)
  }
}

async function insert(poolWrite, payload) {
  try {
    const sqlQuery = `INSERT INTO welcome_package
    (contract_number, body_data, url, created_user, owner_id, phone_number, auth_code)
    VALUES($1, $2, $3, $4, $5, $6, $7)`
    return await poolWrite.query(sqlQuery, [
      payload.contractNumber,
      payload.bodyData,
      payload.url,
      payload.createdUser,
      payload.ownerId,
      payload.phoneNumber,
      payload.authCode
    ])
  } catch (error) {
    console.log('Error at insert WCL: ', error)
  }
}

module.exports = {
  findByContractNumberV2,
  findByContractNumber,
  insert
}
