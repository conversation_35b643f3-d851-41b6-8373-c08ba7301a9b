const tableName = 'loan_account_orders'
const getLoanAccountOrderByDebtAckContractNumberAndNumCycle = async function (poolRead, pl) {
  let sql = 'select * from loan_account_orders lao where debt_ack_contract_number = $1 and order_index = $2'
  const params = [pl.debtAckContractNumber, pl.numCycle]
  return await poolRead.query(sql, params)
}

const getNotFullSuspendLoanAccountOrderByDebtAckContractNumber = async function (poolRead, pl) {
  let sql = 'select * from loan_account_orders lao where debt_ack_contract_number = $1 and is_full_suspend = $2 order by order_index asc'
  const params = [pl.debtAckContractNumber, false]
  return await poolRead.query(sql, params)
}

const addSuspendAmtByDebtAckContractNumberAndNumCycle = async function (poolRead, pl) {
  let sql = 'update loan_account_orders lao set suspend_amt = suspend_amt + $3, is_full_suspend = $4, suspend_date = $5 where debt_ack_contract_number = $1 and order_index = $2'
  const params = [pl.debtAckContractNumber, pl.numCycle, pl.suspendAmt, pl.isFullSuspend, pl.suspendDate]
  return await poolRead.query(sql, params)
}

const fillSuspendAmtByDebtAckContractNumber = async function (poolRead, pl) {
  let sql = 'update loan_account_orders lao set suspend_amt = order_amt - prin_amt, is_full_suspend = $2 where debt_ack_contract_number = $1 and suspend_amt = 0'
  const params = [pl.debtAckContractNumber, pl.isFullSuspend]
  return await poolRead.query(sql, params)
}

module.exports = {
  getLoanAccountOrderByDebtAckContractNumberAndNumCycle,
  getNotFullSuspendLoanAccountOrderByDebtAckContractNumber,
  addSuspendAmtByDebtAckContractNumberAndNumCycle,
  fillSuspendAmtByDebtAckContractNumber,
}
