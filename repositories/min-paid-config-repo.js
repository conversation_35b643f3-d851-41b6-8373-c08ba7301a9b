const baseRepo = require('./base.repo')

const tableName = 'min_paid_config'

const insertManyMinPaidConfig = async function (records) {
  return await baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj: records
  })
}

const findMinPaidConfig = async function (debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
}
module.exports = {
  insertManyMinPaidConfig,
  findMinPaidConfig
}
