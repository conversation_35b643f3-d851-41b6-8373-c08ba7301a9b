const baseRepo = require('./base.repo')

const tableName = 'tenor_config'

const getListTenorConfig = async function () {
  const result = await baseRepo.baseReadAll({
    tableName
  })
  const tenorConfig = {}
  result.forEach((item) => {
    tenorConfig[item.start_day] = {
      endDay: item.end_day,
      deltaMonth: item.delta_month
    }
  })
  return tenorConfig
}
module.exports = {
  getListTenorConfig
}
