/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2022-05-30 10:51:15
 * @modify date 2022-05-30 10:51:15
 * @desc [save risk group infomation of customer]
 */

const { RISK_GROUP, FLAG_NOT_ACTIVE, FLAG_ACTIVE } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'cust_risk_grp'

async function insertIndAndCicAndFinalRg(payload) {
  const fieldsValueObj = {
    cust_id: payload.custId,
    risk_type: RISK_GROUP.INDIVIDUAL_RG,
    risk_date: payload.riskDate || 'now()',
    risk_grp_val: payload.indRgValue,
    dpd: payload.dpd,
    created_user: payload.createdUser || 'system',
    created_date: payload.riskDate || 'now()',
    updated_date: payload.riskDate || 'now()',
    owner_id: payload.ownerId || 1
  }
  const arrayFieldsValueObj = []

  if (payload.indRgValue) {
    arrayFieldsValueObj.push(fieldsValueObj)
  }

  if (payload.finRgValue) {
    arrayFieldsValueObj.push({
      ...fieldsValueObj,
      risk_type: RISK_GROUP.FINAL_RG,
      risk_grp_val: payload.finRgValue
    })
  }

  if (payload.cicRgValue) {
    arrayFieldsValueObj.push({
      ...fieldsValueObj,
      risk_type: RISK_GROUP.CIC_RG,
      risk_grp_val: payload.cicRgValue
    })
  }

  return await baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj
  })
}

async function insertManyCustRg(arrayFieldsValueObj) {
  try {
    return await baseRepo.insertMany({
      tableName,
      arrayFieldsValueObj
    })
  } catch (error) {
    console.log('ERROR insertManyCustRg', error)
    throw error
  }
}

async function findLastestCustRgTypeByCustId(custId, riskType, limit = 1) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      cust_id: custId,
      risk_type: riskType,
      flag_active: FLAG_ACTIVE
    },
    queryOrderArr: ['risk_date', 'DESC', 'cust_risk_id', 'DESC'],
    limit
  })
}

async function deActiveByCustIdMoreDate(custId, riskDate) {
  const params = [custId, riskDate, FLAG_NOT_ACTIVE]
  const sqlQuery = `update ${tableName} set flag_active = $3, updated_date=now() where cust_id= $1 and risk_date >= $2`

  return await global.poolWrite.query(sqlQuery, params)
}

module.exports = {
  insertIndAndCicAndFinalRg,
  insertManyCustRg,
  findLastestCustRgTypeByCustId,
  deActiveByCustIdMoreDate
}
