const { formatDate } = require('../utils/common')
const { FLAG_NOT_ACTIVE, FLAG_ACTIVE } = require('../utils/constant')
const { Between, MoreThanOrEqual, LessThanOrEqual } = require('../utils/find-operator')
const baseRepo = require('./base.repo')

const tableName = 'coll_dpd_hist'

async function insertCollDpdHis(payload) {
  return await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      contract_number: payload.contractNumber,
      debt_ack_contract_number: payload.debtAckContractNumber,
      dpd: payload.dpd,
      dpd_strategy: payload.dpdStrategy,
      owner_id: payload.ownerId || 1,
      created_date: payload.valueDate || 'now()',
      updated_date: payload.valueDate || 'now()',
      created_user: payload.createdUser || 'system',
      value_date: payload.valueDate || 'now()'
    }
  })
}

async function findMaxDpdByDebtAckContractNumber(debtAckContractNumber, startDate, endDate) {
  const queryWhereObj = {
    debt_ack_contract_number: debtAckContractNumber,
    flag_active: FLAG_ACTIVE
  }
  startDate = startDate ? formatDate({ date: startDate }) : null
  endDate = endDate ? formatDate({ date: endDate }) : null
  if (startDate && endDate) {
    queryWhereObj.value_date = Between([startDate, endDate])
  } else if (startDate) {
    queryWhereObj.value_date = MoreThanOrEqual(startDate)
  } else if (endDate) {
    queryWhereObj.value_date = LessThanOrEqual(endDate)
  }
  return await baseRepo.baseSelectV2({
    tableName,
    queryWhereObj,
    selectQuery: 'max(dpd)'
  })
}

async function findLastDpdByDebtAckContractNumber(debtAckContractNumber, endDate) {
  const queryWhereObj = {
    debt_ack_contract_number: debtAckContractNumber,
    flag_active: FLAG_ACTIVE,
    value_date: LessThanOrEqual(formatDate({ date: endDate }))
  }
  return await baseRepo.baseSelectV2({
    tableName,
    queryWhereObj,
    limit: 1,
    queryOrderObj: {
      value_date: 'DESC'
    }
  })
}

async function findDpdByDebtAckContractNumberAndValueDate(debtAckContractNumber, valueDate) {
  const queryWhereObj = {
    debt_ack_contract_number: debtAckContractNumber,
    flag_active: FLAG_ACTIVE,
    value_date: formatDate({ date: valueDate })
  }
  return await baseRepo.baseSelectV2({
    tableName,
    queryWhereObj,
    limit: 1
  })
}

async function deActiveByDebtAckContractNumberMoreDate(debtAckContractNumber, valueDate) {
  const params = [debtAckContractNumber, valueDate, FLAG_NOT_ACTIVE]
  const sqlQuery = `update ${tableName} set flag_active = $3, updated_date=now() where debt_ack_contract_number= $1 and value_date >= $2`

  return await global.poolWrite.query(sqlQuery, params)
}

module.exports = {
  insertCollDpdHis,
  findMaxDpdByDebtAckContractNumber,
  findLastDpdByDebtAckContractNumber,
  deActiveByDebtAckContractNumberMoreDate,
  findDpdByDebtAckContractNumberAndValueDate
}
