const format = require('pg-format')
const baseRepo = require('./base.repo')
const constant = require("../utils/constant");

const tableName = 'payment_detail'

const insPaymentDetail = async function (poolWrite, payload) {
  const sql = `INSERT INTO payment_detail 
    (bill_id, payment_id, amount, owner_id, is_testing, created_date, updated_date, created_by)
    VALUES($1, $2, $3, $4, $5, now(), now(), $6)`
  return await poolWrite.query(sql, [
    payload.installmentId,
    payload.paymentId,
    payload.amount,
    payload.ownerId,
    payload.isTesting,
    payload.createdBy
  ])
}
const insBatchPaymentDetail = async function (poolWrite, records) {
  const sql = format(
    `INSERT INTO payment_detail 
    (bill_id, payment_id, amount, owner_id, is_testing, created_by, post_affect, debt_ack_contract_number)
    VALUES %L `,
    records
  )
  return await poolWrite.query(sql)
}

const insBatchPaymentDetailV2 = async function (records) {
  return baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj: records
  })
}

const getAllByPaymentId = async function (poolRead, payload) {
  const sql =
    'select pd.*,bod.type,bod.on_due_date FROM payment_detail pd left join bill_on_due bod on bod.id = pd.bill_id where pd.payment_id = $1 and pd.flag_active <> $2'
  return await poolRead.query(sql, [payload.paymentId, constant.FLAG_NOT_ACTIVE])
}

const getAllByPaymentIdV2 = async function (poolRead, payload) {
  const sql =
      'select pd.*,bod.type,bod.on_due_date,i.description FROM payment_detail pd ' +
      'left join bill_on_due bod on bod.id = pd.bill_id ' +
      'left join installment i on bod.installment_id = i.id ' +
      'where payment_id = $1 and pd.flag_active <> $2'
  return await poolRead.query(sql, [payload.paymentId, constant.FLAG_NOT_ACTIVE])
}

const getPaymentDetailByBillId = async function (listBillId) {
  if (listBillId.length) {
    const listBillIdString = listBillId.join(',')
    const sql = `SELECT pd.id,pd.bill_id , pd.amount , pd.payment_id ,bod.installment_id, i.type, i.description , bod.is_annex, bod.annex_number, pd.created_date 
    from payment_detail pd
    join bill_on_due bod on bod.id = pd.bill_id 
    join installment i on i.id  = bod.installment_id 
    where pd.flag_active = 1 and pd.bill_id in (${listBillIdString})`
    const result = await global.poolRead.query(sql, [])
    return result.rows
  }
  return []
}

const updateFlagActiveByListId = async function (newFlagActive, listIds, cancelDate) {
  try {
    if (listIds.length) {
      const idJoinStr = listIds.join(',')
      const sql = `UPDATE payment_detail SET flag_active = $1, updated_date = now(), cancel_date= $2 WHERE id in (${idJoinStr})`
      const params = [newFlagActive, cancelDate]
      return await global.poolWrite.query(sql, params)
    }
  } catch (error) {
    console.log('ERROR updateFlagActiveByListId', error)
    throw error
  }
}

module.exports = {
  insPaymentDetail,
  insBatchPaymentDetail,
  getAllByPaymentId,
  getAllByPaymentIdV2,
  updateFlagActiveByListId,
  getPaymentDetailByBillId,
  insBatchPaymentDetailV2
}
