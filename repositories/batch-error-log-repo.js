const baseRepo = require('./base.repo')

const tableName = 'batch_err_log'

const insertBatchErrorLog = async function (payload) {
  const result = await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      product_type: payload.productType,
      job_name: payload.jobName,
      job_id: payload.jobId,
      status: payload.status,
      start_date: payload.startDate,
      end_date: payload.endDate,
      error_code: payload.errorCode,
      error_message: payload.errorMessage,
      contract_number: payload.debtAckContractNumber
    },
    isReturn: true
  })
  if (result.rows.length) {
    return result.rows[0].amort_id
  }
  return null
}

module.exports = {
  insertBatchErrorLog
}
