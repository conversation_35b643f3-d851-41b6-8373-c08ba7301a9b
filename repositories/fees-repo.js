const insertFees = async function (poolWrite, payload) {
  const sql = `INSERT INTO fees
    (mc_limit_id, product_code, code, "name", fee_amt, owner_id, is_testing, created_by, debt_ack_contract_number,fee_type, calcula_type, instal_from, instal_to, priority)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) returning *`
  return await poolWrite.query(sql, [
    payload.mcLimitId,
    payload.productCode,
    payload.code,
    payload.name,
    payload.feeAmt,
    payload.ownerId,
    payload.isTesting,
    payload.createdBy,
    payload.debtAckContractNumber,
    payload.feeType,
    payload.calculaType,
    payload.instalFrom,
    payload.instalTo,
    payload.priority
  ])
}
const findByProductCode = async function (poolRead, pl) {
  const sql = 'SELECT * FROM fees where product_code = $1'
  return await poolRead.query(sql, [pl.productCode])
}
const findByDebtAckAndCode = async function (pl) {
  const sql = 'SELECT * FROM fees where code = $1 and debt_ack_contract_number = $2 and status = 1'
  return await global.poolRead.query(sql, [pl.feeCode, pl.debtAckContractNumber])
}
const findByDebtAckAndProductCode = async function (pl) {
  const sql = 'SELECT * FROM fees where product_code = $1 and debt_ack_contract_number = $2 and status = 1'
  return await global.poolRead.query(sql, [pl.productCode, pl.debtAckContractNumber])
}

module.exports = {
  findByDebtAckAndProductCode,
  insertFees,
  findByProductCode,
  findByDebtAckAndCode
}
