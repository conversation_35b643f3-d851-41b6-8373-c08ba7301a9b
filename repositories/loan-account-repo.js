const { DEBT_ACK_STATUS, CONTRACT_TYPE, IS_WRITE_OFF, PAYMENT_STATUS, KUNN_TYPE, PARTNER_CODE } = require('../utils/constant')
const { In } = require('../utils/find-operator')
const baseRepo = require('./base.repo')

const tableName = 'loan_account'

async function findLoanAccByDebtAckContractNumber(debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
}

async function findOneLoanAcc(debtAckContractNumber) {
  const res = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    }
  })
  if (res.length) {
    return res?.[0]
  }
  return null
}
async function findListContractByCustId(custId) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      cust_id: custId,
      status: DEBT_ACK_STATUS.ACTIVE
    },
    queryOrderArr: ['dpd', 'desc']
  })
}

const findListContractByCustIdToRunDpd = async function (date, custId) {
  try {
    const sql = `select * from loan_account
        where (status = 1 or 
               (status = 3 and date_part('day', $1::date::timestamp - termination_date::date::timestamp) <= 90)) 
          and cust_id = $2 order by dpd desc`
    const result = await global.poolRead.query(sql, [date, custId])
    return result.rows
  } catch (e) {
    console.log(e)
    return []
  }
}

async function findListContractByContractNumber(contractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      contract_number: contractNumber
    }
  })
}

async function getListActiveLoanAccByPartnerCodeAndContractType(partnerCode, contractType, debtAckContractNumber) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      partner_code: partnerCode,
      contract_type: contractType,
      debt_ack_contract_number: debtAckContractNumber,
      status: DEBT_ACK_STATUS.ACTIVE,
      'not payment_status': PAYMENT_STATUS.DONE
    }
  })
}

async function updateLoanAccountAmount({
  debtAckContractNumber,
  toCollect = 0,
  nonAllocationAmt = 0,
  prinPaid = 0,
  intPaid = 0,
  feePaid = 0,
  lpiPaid = 0,
  toCollectUpdated
}) {
  const fieldsValueAddObj = {
    non_allocation_amt: nonAllocationAmt,
    prin_amt: prinPaid,
    prin_paid: -prinPaid,
    int_paid: -intPaid,
    fee_paid_amt: -feePaid,
    lpi_paid: -lpiPaid
  }
  if (toCollect) {
    fieldsValueAddObj.to_collect = toCollect
  }
  const fieldsValueObj = {}
  if (toCollectUpdated != null) {
    fieldsValueObj.to_collect = toCollectUpdated
  }
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    },
    fieldsValueAddObj,
    fieldsValueObj,
    isUpdatedTime: true
  })
}
async function updateLoanAccountSuspendAmtAndNonAllocationAmt({debtAckContractNumber, suspendAmt = 0, nonAllocateAmt = 0, toCollect = 0}) {
  const fieldsValueAddObj = {
    to_collect: toCollect,
    suspend_amt: suspendAmt,
    non_allocation_amt: nonAllocateAmt,
  }

  const fieldsValueObj = {}
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    },
    fieldsValueAddObj,
    fieldsValueObj,
    isUpdatedTime: true
  })
}
async function findFirstActiveKunnByContractNumberAndContractType(
  contractNumber,
  contractType = CONTRACT_TYPE.CASHLOAN
) {
  const result = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      contract_number: contractNumber,
      contract_type: contractType,
      status: DEBT_ACK_STATUS.ACTIVE
    },
    queryOrderArr: ['active_date', 'asc'],
    limit: 1
  })
  if (result.length) {
    return result[0]
  }
  return null
}
const updateLoanAccount = async function (poolWrite, pl) {
  let sql = 'UPDATE loan_account SET updated_date = now()'
  let idx = 1
  const params = []
  if (pl?.dpd != null) {
    sql += ', dpd = $' + idx
    params.push(pl.dpd)
    idx++
  }
  if (pl?.dpd_strategy != null) {
    sql += ', dpd_strategy = $' + idx
    params.push(pl.dpd_strategy)
    idx++
  }
  if (pl?.is_writing_off != null) {
    sql += ', is_writing_off = $' + idx
    params.push(pl.is_writing_off)
    idx++
  }
  if (pl?.is_contract_breach != null) {
    sql += ', is_contract_breach = $' + idx
    params.push(pl.is_contract_breach)
    idx++
  }
  if (pl?.channel != null) {
    sql += ', channel = $' + idx
    params.push(pl.channel)
    idx++
  }
  sql += ' WHERE debt_ack_contract_number= $' + idx
  params.push(pl.debt_ack_contract_number)
  return await poolWrite.query(sql, params)
}
const findAllActiveAndPaymentNotComplete = async function (offset, limit = 1000) {
  try {
    const sql = `select * from loan_account where status = 1 and payment_status <> 0 and cust_id is not null offset ${offset} limit ${limit}`
    const result = await global.poolRead.query(sql, [])
    return result.rows
  } catch (e) {
    console.log(e)
  }
}
const findAllFactoringActiveAndPaymentNotComplete = async function (offset, limit = 1000) {
  try {
    const sql = `select * from loan_account where status = 1 and partner_code in ('${PARTNER_CODE.BIZZ}') and payment_status <> 0 and cust_id is not null offset ${offset} limit ${limit}`
    const result = await global.poolRead.query(sql, [])
    return result.rows
  } catch (e) {
    console.log(e)
  }
}

const findAllActiveCustId = async function (offset, limit = 1000) {
  try {
    const sql = `select distinct (cust_id) from loan_account
        where status = 1 and payment_status <> 0 and cust_id is not null
        offset ${offset} limit ${limit}`
    const result = await global.poolRead.query(sql, [])
    return result.rows
  } catch (e) {
    console.log(e)
    return []
  }
}

const findAllCustIdToRunDpd = async function (date, offset, limit = 1000) {
  try {
    const sql = `select distinct (cust_id) from loan_account
        where ((status = 1 and payment_status <> 0) or 
               (status = 3 and date_part('day', $1::date::timestamp - termination_date::date::timestamp) <= 90)) 
           and cust_id is not null 
        offset ${offset} limit ${limit}`
    const result = await global.poolRead.query(sql, [date])
    return result.rows
  } catch (e) {
    console.log(e)
    return []
  }
}

async function updateIsWritingOffbyDebtAckContractNumber(
  debtAckContractNumber,
  isWriteOff = IS_WRITE_OFF,
  writeOffDate
) {
  console.log('Update updateIsWritingOff payload ', debtAckContractNumber)
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    },
    fieldsValueObj: {
      is_writing_off: isWriteOff,
      write_off_date: writeOffDate
    },
    isUpdatedTime: true
  })
}

async function updateLoanStatusAndPaymentStatus(payload) {
  const fieldsValueObj = {
    payment_status: payload.paymentStatus,
    status: payload.status,
    termination_date: payload.terminationDate || null
  }
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      loan_id: payload.debtAckContractId
    },
    fieldsValueObj
  })
}

const findActiveKunnByCustIdAndChannel = async function (payload) {
  try {
    let sql = `select la.debt_ack_contract_number as kunn_number,* from loan_account la left join voucher vc on la.debt_ack_contract_number = vc.debt_ack_contract_number 
                  where la.status = 1 and la.payment_status <> 0 and cust_id = $1`
    const params = [payload.custId]
    let index = 1
    if (payload.channel) {
      index++
      sql += ` and la.partner_code = $${index}`
      params.push(payload.channel)
    }
    if (payload.type == KUNN_TYPE.WITHDRAWAL) {
      sql += ' and vc.voucher_code is null'
    } else if (payload.type == KUNN_TYPE.AMORT) {
      sql += ' and vc.voucher_code is not null'
    }
    if (payload.orderBy) {
      sql += ' order by la.loan_id ' + payload.orderBy
    }
    const result = await global.poolWrite.query(sql, params)
    return result.rows
  } catch (e) {
    return []
  }
}
const updateActiveKunn = async function (payload) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      loan_id: payload.loanId
    },
    fieldsValueObj: {
      int_amt: payload.intAmt,
      fee_amt: payload.feeAmt,
      rls_amt: payload.rlsAmt,
      start_date: payload.startDate,
      active_date: payload.startDate,
      end_date: payload.endDate,
      status: DEBT_ACK_STATUS.ACTIVE,
      activation_date: payload.startDate,
      rls_date: new Date(),
      bill_day: payload.billDay
    },
    isUpdatedTime: true
  })
}

const updateStatusLoanAccount = async function (debtAckContractNumber, status) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    },
    fieldsValueObj: {
      status
    }
  })
}

async function findListContractByCustIdV2(custId) {
  return await baseRepo.baseSelectV2({
    tableName,
    queryWhereObj: {
      cust_id: custId,
      status: In([DEBT_ACK_STATUS.ACTIVE, DEBT_ACK_STATUS.TERMINATED])
    },
    queryOrderObj: {
      dpd: 'DESC'
    }
  })
}
const updateIntLoanAccount = async function (debtAckContractNumber, intAmt) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    },
    fieldsValueObj: {
      int_amt: intAmt
    }
  })
}

module.exports = {
  updateIsWritingOffbyDebtAckContractNumber,
  updateLoanAccount,
  findLoanAccByDebtAckContractNumber,
  findListContractByCustId,
  findFirstActiveKunnByContractNumberAndContractType,
  findAllActiveAndPaymentNotComplete,
  getListActiveLoanAccByPartnerCodeAndContractType,
  findListContractByContractNumber,
  updateLoanAccountAmount,
  updateLoanStatusAndPaymentStatus,
  findActiveKunnByCustIdAndChannel,
  updateActiveKunn,
  updateStatusLoanAccount,
  findAllActiveCustId,
  findAllCustIdToRunDpd,
  findListContractByCustIdV2,
  findListContractByCustIdToRunDpd,
  findOneLoanAcc,
  updateLoanAccountSuspendAmtAndNonAllocationAmt,
  updateIntLoanAccount,
  findAllFactoringActiveAndPaymentNotComplete
}
