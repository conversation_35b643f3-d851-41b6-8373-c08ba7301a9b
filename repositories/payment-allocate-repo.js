const insPaymentAllocate = async function (poolWrite, payload) {
  const sql = `INSERT INTO payment_allocate
    (product_code, mc_limit_id, code, "name", "type", value, idx, status, owner_id, is_testing, created_date, updated_date, created_by)
    VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, now(), now(), $11) returning *`
  return await poolWrite.query(sql, [
    payload.productCode,
    payload.mcLimitId,
    payload.code,
    payload.name,
    payload.type,
    payload.value,
    payload.idx,
    payload.status,
    payload.ownerId,
    payload.isTesting,
    payload.createdBy
  ])
}

const findAllPaymentAllocateCodeAndActive = async function (payload) {
  const sql = `SELECT id, product_code, mc_limit_id, code, name, "type", value, idx, status, owner_id, is_testing, created_date, updated_date, created_by
    FROM payment_allocate where code = $1 and status = 1 and start_time is null and end_time is null order by idx`
  return await global.poolRead.query(sql, [payload.code])
}
const findAllPaymentAllocateCodeAndActiveAndRangeTime = async function (payload) {
  const sql = `SELECT *
    FROM payment_allocate where code = $1 and status = 1 
    and start_time <= $2 and end_time > $2
    order by idx`
  return await global.poolRead.query(sql, [payload.code, payload.activeDate])
}

const findAllPaymentAllocateCode = async function () {
  const sql = `SELECT *
    FROM payment_allocate where status = 1 
    order by idx`
  const result = await global.poolRead.query(sql, [])
  return result.rows
}
module.exports = {
  insPaymentAllocate,
  findAllPaymentAllocateCodeAndActive,
  findAllPaymentAllocateCodeAndActiveAndRangeTime,
  findAllPaymentAllocateCode
}
