const { FLAG_ACTIVE } = require('../utils/constant')
const baseRepo = require('./base.repo')

const tableName = 'loan_amort'

const insertLoanAmort = async function (payload, flagActive = FLAG_ACTIVE) {
  const result = await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: {
      debt_ack_contract_number: payload.debtAckContractNumber,
      amt_amort: payload.amtAmort,
      int_rate: payload.intRate,
      start_date: payload.startDate,
      end_date: payload.endDate,
      tenor: payload.tenor,
      flag_active: flagActive,
      annex_number: payload.annexNumber,
      prev_amort_id: payload.prevAmortId
    },
    isReturn: true
  })
  if (result.rows.length) {
    return result.rows[0].amort_id
  }
  return null
}
const findCurrentAmort = async function (debtAckContractNumber, flagActive = FLAG_ACTIVE) {
  const result = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: flagActive
    }
  })
  if (result.length) {
    return result[0]
  }
  return {}
}
const updateAmortFlagActive = async function (debtAckContractNumber, oldFlagActive, newflagActive) {
  const result = await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: oldFlagActive
    },
    fieldsValueObj: {
      flag_active: newflagActive
    },
    isUpdatedTime: true,
    isReturn: true
  })
  if (result.length) {
    return result[0]
  }
  return {}
}

const updateAmortFlagActiveByAnnexNumber = async function (annexNumber, newflagActive) {
  const result = await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      annex_number: annexNumber
    },
    fieldsValueObj: {
      flag_active: newflagActive
    },
    isUpdatedTime: true,
    isReturn: true
  })
  if (result.length) {
    return result[0]
  }
  return {}
}
const updateAmortFlagActiveByAmortId = async function (amortId, newflagActive) {
  const result = await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      amort_id: amortId
    },
    fieldsValueObj: {
      flag_active: newflagActive
    },
    isUpdatedTime: true,
    isReturn: true
  })
  if (result.length) {
    return result[0]
  }
  return {}
}
const findInitAmort = async function (debtAckContractNumber) {
  const result = await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber
    },
    queryOrderArr: ['amort_id', 'asc'],
    limit: 1
  })
  if (result.length) {
    return result[0]
  }
  return {}
}

const deleteByDebtAckContractNumber = async function (debt_ack_contract_number) {
  console.log('deleteByDebtAckContractNumber:', debt_ack_contract_number)
  const sql = 'delete from loan_amort where debt_ack_contract_number = $1;'
  try {
    return Promise.all([
      global.poolWrite.query(sql, [debt_ack_contract_number]),
    ])
  } catch (error) {
    console.log('Error deleteByDebtAckContractNumber', error)
  }
}

module.exports = {
  insertLoanAmort,
  findCurrentAmort,
  updateAmortFlagActive,
  updateAmortFlagActiveByAnnexNumber,
  updateAmortFlagActiveByAmortId,
  deleteByDebtAckContractNumber,
  findInitAmort
}
