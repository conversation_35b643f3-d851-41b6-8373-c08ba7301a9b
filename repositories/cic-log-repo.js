const { DATE_FORMAT } = require('../utils/constant')
const { Between, MoreThanOrEqual, LessThanOrEqual } = require('../utils/find-operator')
const baseRepo = require('./base.repo')
const moment = require('moment')

const tableName = 'cic_log'
const insBatchCicLog = async function (records) {
  return await baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj: records
  })
}

async function findMaxCicByCustId(custId, startDate, endDate) {
  const queryWhereObj = {
    cust_id: custId
  }
  startDate = startDate ? moment(startDate).format(DATE_FORMAT.YYYYMMDD2) : null
  endDate = endDate ? moment(endDate).format(DATE_FORMAT.YYYYMMDD2) : null
  if (startDate && endDate) {
    queryWhereObj.risk_date = Between([startDate, endDate])
  } else if (startDate) {
    queryWhereObj.risk_date = MoreThanOrEqual(startDate)
  } else if (endDate) {
    queryWhereObj.risk_date = LessThanOrEqual(endDate)
  }
  return await baseRepo.baseSelectV2({
    tableName,
    queryWhereObj,
    selectQuery: 'max(risk_grp_val)'
  })
}
module.exports = {
  insBatchCicLog,
  findMaxCicByCustId
}
