const baseRepo = require('./base.repo')
const constant = require('../utils/constant')

const tableName = 'promotion_installment_deduction'

const insertManyDeductionInstallment = async function (records) {
  return await baseRepo.insertMany({
    tableName,
    arrayFieldsValueObj: records
  })
}

const insertDeductionInstallment = async function (payload) {
  const result = await baseRepo.baseInsert({
    tableName,
    fieldsValueObj: payload,
    isReturn: true
  })
  if (result.rows.length) {
    return result.rows[0]
  }
  return null
}
const findInstallmentClosedDeduction = async function (debtAckContractNumber) {
  const sql = `select * from installment i join promotion_installment_deduction pid on i.id  = pid.installment_id and pid.flag_active = 1
  where i.debt_ack_contract_number = $1 and i.status = $2 and i.closed = $3`
  const res = await global.poolRead.query(sql, [
    debtAckContractNumber,
    constant.INSTALLMENT.STATUS.ACTIVE,
    constant.INSTALLMENT.CLOSE.TRUE
  ])

  return res.rows
}

const findListInstallmentDeduction = async function (debtAckContractNumber, flagActive = constant.FLAG_ACTIVE) {
  return await baseRepo.baseReadAll({
    tableName,
    queryWhereObj: {
      debt_ack_contract_number: debtAckContractNumber,
      flag_active: flagActive
    }
  })
}

const updateStatusByInstallmentId = async function (newFlagActive, oldFlagActive, installmentId) {
  return await baseRepo.baseUpdate({
    tableName,
    queryWhereObj: {
      installment_id: installmentId,
      flag_active: oldFlagActive
    },
    fieldsValueObj: {
      flag_active: newFlagActive
    }
  })
}
module.exports = {
  insertManyDeductionInstallment,
  findInstallmentClosedDeduction,
  insertDeductionInstallment,
  updateStatusByInstallmentId,
  findListInstallmentDeduction
}
