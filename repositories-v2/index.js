const BillOnDueRepository = require('./bill-on-due.repository')
const LoanAccountRepository = require('./loan-account.repository')
const CicLogRepository = require('./cic-log.repository')
const CollDpdHistRepository = require('./coll-dpd-hist.repository')
const ContractRiskGrpRepository = require('./contract-risk-grp.repository')
const CustRiskGrpRepository = require('./cust-risk-grp.repository')
const DisbursementRepository = require('./disbursement.repository')
const InstallmentRepository = require('./installment.repository')
const InsuranceRepository = require('./insurance.repository')
const IrChargeRepository = require('./ir-charge.repository')
const IrRepository = require('./ir.repository')
const LoanAmortRepository = require('./loan-amort.repository')
const LoanAnnexRepository = require('./loan-annex.repository')
const LoanContractLimitRepository = require('./loan-contract-limit.repository')
const LoanRiskGrpRepository = require('./loan-risk-grp.repository')
const PaymentRepository = require('./payment.repository')
const PaymentDetailRepository = require('./payment-detail.repository')
const LoanStatusHstRepository = require('./loan-status-hst.repository')
const WelcomePackageRepository = require('./welcome-package.repository')
const FreezeHstRepository = require('./freeze-hst.repository')

module.exports = {
  BillOnDueRepository,
  LoanAccountRepository,
  CicLogRepository,
  CollDpdHistRepository,
  ContractRiskGrpRepository,
  CustRiskGrpRepository,
  DisbursementRepository,
  InstallmentRepository,
  InsuranceRepository,
  IrChargeRepository,
  IrRepository,
  LoanAmortRepository,
  LoanAnnexRepository,
  LoanContractLimitRepository,
  LoanRiskGrpRepository,
  PaymentRepository,
  PaymentDetailRepository,
  LoanStatusHstRepository,
  WelcomePackageRepository,
  FreezeHstRepository
}
