const BaseRepository = require('./base.repository')
const { FreezeHstEntity, metadata } = require('../entities/freeze-hst.entity')

class FreezeHstRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = FreezeHstEntity

  /**
   * Create new FreezeHstEntity based on the provided parameters.
   * @param {FreezeHstEntity} data - Data of entity
   * @returns {FreezeHstEntity} -  Entities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entities based on the provided parameters.
   * @param {FreezeHstEntity & FreezeHstEntity[]} data - Data of entity
   * @returns {Promise<FreezeHstEntity & FreezeHstEntity[]>} -  Entities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {FreezeHstEntity} options.where - Where conditions.
   * @param {FreezeHstEntity} options.order - Order condition.
   * @returns {Promise<FreezeHstEntity>} - A promise that resolves to an array of Entities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {FreezeHstEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {FreezeHstEntity} options.order - Order conditions.
   * @returns {Promise<FreezeHstEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entities based on the provided parameters.
   * @param {FreezeHstEntity} where - Where conditions for querying entities.
   * @returns {Promise<FreezeHstEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {FreezeHstEntity} options.where - Where conditions.
   * @param {FreezeHstEntity} options.data - Order conditions.
   * @returns {Promise<FreezeHstEntity[]>} -  Entities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = FreezeHstRepository
