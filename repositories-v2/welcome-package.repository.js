const BaseRepository = require('./base.repository')
const { WelcomePackageEntity, metadata } = require('../entities/welcome-package.entity')

class WelcomePackageRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = WelcomePackageEntity

  /**
   * Create new WelcomePackageEntity based on the provided parameters.
   * @param {WelcomePackageEntity} data - Data of entity
   * @returns {WelcomePackageEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {WelcomePackageEntity & WelcomePackageEntity[]} data - Data of entity
   * @returns {Promise<WelcomePackageEntity & WelcomePackageEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {WelcomePackageEntity} options.where - Where conditions.
   * @param {WelcomePackageEntity} options.order - Order condition.
   * @returns {Promise<WelcomePackageEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {WelcomePackageEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {WelcomePackageEntity} options.order - Order conditions.
   * @returns {Promise<WelcomePackageEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entity based on the provided parameters.
   * @param {WelcomePackageEntity} where - Where conditions for querying entities.
   * @returns {Promise<WelcomePackageEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {WelcomePackageEntity} options.where - Where conditions.
   * @param {WelcomePackageEntity} options.data - Order conditions.
   * @returns {Promise<WelcomePackageEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = WelcomePackageRepository
