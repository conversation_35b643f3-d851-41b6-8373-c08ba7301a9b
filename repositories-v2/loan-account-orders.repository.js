const BaseRepository = require('./base.repository')
const { LoanAccountOrdersEntity, metadata } = require('../entities/loan-account-orders.entity')
class LoanAccountOrdersRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = LoanAccountOrdersEntity

  /**
   * Create new LoanAccountOrdersEntity based on the provided parameters.
   * @param {LoanAccountOrdersEntity} data - Data of entity
   * @returns {LoanAccountOrdersEntity} -  LoanAccountOrdersEntity.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save LoanAccountOrdersEntities based on the provided parameters.
   * @param {LoanAccountOrdersEntity & LoanAccountOrdersEntity[]} data - Data of entity
   * @returns {Promise<LoanAccountOrdersEntity & LoanAccountOrdersEntity[]>} -  LoanAccountOrdersEntities.
   */
  static async save(data) {
    return super.save(data)
  }

    /**
     * Save multiple LoanAccountOrdersEntities in batch.
     * @param {LoanAccountOrdersEntity[]} dataArray - Array of LoanAccountOrdersEntity objects.
     * @returns {Promise<LoanAccountOrdersEntity[]>} - A promise that resolves to an array of saved LoanAccountOrdersEntities.
     */
    static async saveBatch(dataArray) {
      // Implement batch save logic here since super.saveBatch is not defined in BaseRepository
      if (!Array.isArray(dataArray)) {
        throw new Error('dataArray must be an array')
      }
      // You may want to use Promise.all or a transaction depending on your ORM/database
      const results = []
      for (const data of dataArray) {
        const saved = await this.save(data)
        results.push(saved)
      }
      return results
    }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {IrChargeEntity} options.where - Where conditions.
   * @param {IrChargeEntity} options.order - Order condition.
   * @returns {Promise<IrChargeEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all IrEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {IrChargeEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {IrChargeEntity} options.order - Order conditions.
   * @returns {Promise<IrChargeEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by IrEntities based on the provided parameters.
   * @param {IrChargeEntity} where - Where conditions for querying entities.
   * @returns {Promise<IrChargeEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update IrEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {IrChargeEntity} options.where - Where conditions.
   * @param {IrChargeEntity} options.data - Order conditions.
   * @returns {Promise<IrChargeEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = LoanAccountOrdersRepository
