const BaseRepository = require('./base.repository')
const { IrChargeEntity, metadata } = require('../entities/ir-charge.entity')

class IrChargeRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = IrChargeEntity

  /**
   * Create new IrChargeEntity based on the provided parameters.
   * @param {IrChargeEntity} data - Data of entity
   * @returns {IrChargeEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save IrEntities based on the provided parameters.
   * @param {IrChargeEntity & IrChargeEntity[]} data - Data of entity
   * @returns {Promise<IrChargeEntity & IrChargeEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

    /**
     * Save multiple IrChargeEntities in batch.
     * @param {IrChargeEntity[]} dataArray - Array of IrChargeEntity objects.
     * @returns {Promise<IrChargeEntity[]>} - A promise that resolves to an array of saved IrChargeEntities.
     */
    static async saveBatch(dataArray) {
      // Implement batch save logic here since super.saveBatch is not defined in BaseRepository
      if (!Array.isArray(dataArray)) {
        throw new Error('dataArray must be an array')
      }
      // You may want to use Promise.all or a transaction depending on your ORM/database
      const results = []
      for (const data of dataArray) {
        const saved = await this.save(data)
        results.push(saved)
      }
      return results
    }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {IrChargeEntity} options.where - Where conditions.
   * @param {IrChargeEntity} options.order - Order condition.
   * @returns {Promise<IrChargeEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all IrEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {IrChargeEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {IrChargeEntity} options.order - Order conditions.
   * @returns {Promise<IrChargeEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by IrEntities based on the provided parameters.
   * @param {IrChargeEntity} where - Where conditions for querying entities.
   * @returns {Promise<IrChargeEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update IrEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {IrChargeEntity} options.where - Where conditions.
   * @param {IrChargeEntity} options.data - Order conditions.
   * @returns {Promise<IrChargeEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = IrChargeRepository
