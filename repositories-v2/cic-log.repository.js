const BaseRepository = require('./base.repository')
const { CicLogEntity, metadata } = require('../entities/cic-log.entity')

class CicLogRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = CicLogEntity

  /**
   * Create new CicLogEntity based on the provided parameters.
   * @param {CicLogEntity} data - Data of entity
   * @returns {CicLogEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {CicLogEntity & CicLogEntity[]} data - Data of entity
   * @returns {Promise<CicLogEntity & CicLogEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {CicLogEntity} options.where - Where conditions.
   * @param {CicLogEntity} options.order - Order condition.
   * @returns {Promise<CicLogEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {CicLogEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {CicLogEntity} options.order - Order conditions.
   * @returns {Promise<CicLogEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entity based on the provided parameters.
   * @param {CicLogEntity} where - Where conditions for querying entities.
   * @returns {Promise<CicLogEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {CicLogEntity} options.where - Where conditions.
   * @param {CicLogEntity} options.data - Order conditions.
   * @returns {Promise<CicLogEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = CicLogRepository
