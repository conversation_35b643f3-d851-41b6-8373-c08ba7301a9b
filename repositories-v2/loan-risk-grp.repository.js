const BaseRepository = require('./base.repository')
const { LoanRiskGrpEntity, metadata } = require('../entities/loan-risk-grp.entity')

class LoanRiskGrpRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = LoanRiskGrpEntity

  /**
   * Create new LoanRiskGrpEntity based on the provided parameters.
   * @param {LoanRiskGrpEntity} data - Data of entity
   * @returns {LoanRiskGrpEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {LoanRiskGrpEntity & LoanRiskGrpEntity[]} data - Data of entity
   * @returns {Promise<LoanRiskGrpEntity & LoanRiskGrpEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {LoanRiskGrpEntity} options.where - Where conditions.
   * @param {LoanRiskGrpEntity} options.order - Order condition.
   * @returns {Promise<LoanRiskGrpEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanRiskGrpEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {LoanRiskGrpEntity} options.order - Order conditions.
   * @returns {Promise<LoanRiskGrpEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entity based on the provided parameters.
   * @param {LoanRiskGrpEntity} where - Where conditions for querying entities.
   * @returns {Promise<LoanRiskGrpEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanRiskGrpEntity} options.where - Where conditions.
   * @param {LoanRiskGrpEntity} options.data - Order conditions.
   * @returns {Promise<LoanRiskGrpEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = LoanRiskGrpRepository
