const BaseRepository = require('./base.repository')
const { LoanOrderFeesEntity, metadata } = require('../entities/loan-order-fees.entity')

class LoanOrderFeesRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = LoanOrderFeesEntity

  /**
   * Create new LoanOrderFeesEntity based on the provided parameters.
   * @param {LoanOrderFeesEntity} data - Data of entity
   * @returns {LoanOrderFeesEntity} -  LoanOrderFeesEntity.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save LoanOrderFeesEntities based on the provided parameters.
   * @param {LoanOrderFeesEntity & LoanOrderFeesEntity[]} data - Data of entity
   * @returns {Promise<LoanOrderFeesEntity & LoanOrderFeesEntity[]>} -  LoanOrderFeesEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {LoanOrderFeesEntity} options.where - Where conditions.
   * @param {LoanOrderFeesEntity} options.order - Order condition.
   * @returns {Promise<LoanOrderFeesEntity>} - A promise that resolves to an array of LoanOrderFeesEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all LoanOrderFeesEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanOrderFeesEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {LoanOrderFeesEntity} options.order - Order conditions.
   * @returns {Promise<LoanOrderFeesEntity[]>} - A promise that resolves to an array of LoanOrderFeesEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by LoanOrderFeesEntities based on the provided parameters.
   * @param {LoanOrderFeesEntity} where - Where conditions for querying entities.
   * @returns {Promise<LoanOrderFeesEntity[]>} - A promise that resolves to an array of LoanOrderFeesEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update LoanOrderFeesEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanOrderFeesEntity} options.where - Where conditions.
   * @param {LoanOrderFeesEntity} options.data - Order conditions.
   * @returns {Promise<LoanOrderFeesEntity[]>} -  LoanOrderFeesEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = LoanOrderFeesRepository
