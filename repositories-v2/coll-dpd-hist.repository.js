const BaseRepository = require('./base.repository')
const { CollDpdHistEntity, metadata } = require('../entities/coll-dpd-hist.entity')

class CollDpdHistRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = CollDpdHistEntity

  /**
   * Create new CollDpdHistEntity based on the provided parameters.
   * @param {CollDpdHistEntity} data - Data of entity
   * @returns {CollDpdHistEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {CollDpdHistEntity & CollDpdHistEntity[]} data - Data of entity
   * @returns {Promise<CollDpdHistEntity & CollDpdHistEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {CollDpdHistEntity} options.where - Where conditions.
   * @param {CollDpdHistEntity} options.order - Order condition.
   * @returns {Promise<CollDpdHistEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {CollDpdHistEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {CollDpdHistEntity} options.order - Order conditions.
   * @returns {Promise<CollDpdHistEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entity based on the provided parameters.
   * @param {CollDpdHistEntity} where - Where conditions for querying entities.
   * @returns {Promise<CollDpdHistEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {CollDpdHistEntity} options.where - Where conditions.
   * @param {CollDpdHistEntity} options.data - Order conditions.
   * @returns {Promise<CollDpdHistEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = CollDpdHistRepository
