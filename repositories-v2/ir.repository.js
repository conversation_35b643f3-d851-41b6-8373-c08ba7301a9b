const BaseRepository = require('./base.repository')
const { IrEntity, metadata } = require('../entities/ir.entity')

class IrRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = IrEntity

  /**
   * Create new IrEntity based on the provided parameters.
   * @param {IrEntity} data - Data of entity
   * @returns {IrEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save IrEntities based on the provided parameters.
   * @param {IrEntity & IrEntity[]} data - Data of entity
   * @returns {Promise<IrEntity & IrEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {IrEntity} options.where - Where conditions.
   * @param {IrEntity} options.order - Order condition.
   * @returns {Promise<IrEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all IrEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {IrEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {IrEntity} options.order - Order conditions.
   * @returns {Promise<IrEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by IrEntities based on the provided parameters.
   * @param {IrEntity} where - Where conditions for querying entities.
   * @returns {Promise<IrEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update IrEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {IrEntity} options.where - Where conditions.
   * @param {IrEntity} options.data - Order conditions.
   * @returns {Promise<IrEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = IrRepository
