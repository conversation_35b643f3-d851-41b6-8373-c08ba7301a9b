const BaseRepository = require('./base.repository')
const { InsuranceEntity, metadata } = require('../entities/insurance.entity')

class InsuranceRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = InsuranceEntity

  /**
   * Create new InsuranceEntity based on the provided parameters.
   * @param {InsuranceEntity} data - Data of entity
   * @returns {InsuranceEntity} -  InsuranceEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {InsuranceEntity & InsuranceEntity[]} data - Data of entity
   * @returns {Promise<InsuranceEntity & InsuranceEntity[]>} -  InsuranceEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {InsuranceEntity} options.where - Where conditions.
   * @param {InsuranceEntity} options.order - Order condition.
   * @returns {Promise<InsuranceEntity>} - A promise that resolves to an array of InsuranceEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all InsuranceEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {InsuranceEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {InsuranceEntity} options.order - Order conditions.
   * @returns {Promise<InsuranceEntity[]>} - A promise that resolves to an array of InsuranceEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by InsuranceEntities based on the provided parameters.
   * @param {InsuranceEntity} where - Where conditions for querying entities.
   * @returns {Promise<InsuranceEntity[]>} - A promise that resolves to an array of InsuranceEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update InsuranceEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {InsuranceEntity} options.where - Where conditions.
   * @param {InsuranceEntity} options.data - Order conditions.
   * @returns {Promise<InsuranceEntity[]>} -  InsuranceEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = InsuranceRepository
