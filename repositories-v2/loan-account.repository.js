const BaseRepository = require('./base.repository')
const { LoanAccountEntity, metadata } = require('../entities/loan-account.entity')

class LoanAccountRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = LoanAccountEntity

  /**
   * Create new LoanAccountEntity based on the provided parameters.
   * @param {LoanAccountEntity} data - Data of entity
   * @returns {LoanAccountEntity} -  Entities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entities based on the provided parameters.
   * @param {LoanAccountEntity & LoanAccountEntity[]} data - Data of entity
   * @returns {Promise<LoanAccountEntity & LoanAccountEntity[]>} -  Entities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {LoanAccountEntity} options.where - Where conditions.
   * @param {LoanAccountEntity} options.order - Order condition.
   * @returns {Promise<LoanAccountEntity>} - A promise that resolves to an array of Entities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanAccountEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {LoanAccountEntity} options.order - Order conditions.
   * @returns {Promise<LoanAccountEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entities based on the provided parameters.
   * @param {LoanAccountEntity} where - Where conditions for querying entities.
   * @returns {Promise<LoanAccountEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanAccountEntity} options.where - Where conditions.
   * @param {LoanAccountEntity} options.data - Order conditions.
   * @returns {Promise<LoanAccountEntity[]>} -  Entities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = LoanAccountRepository
