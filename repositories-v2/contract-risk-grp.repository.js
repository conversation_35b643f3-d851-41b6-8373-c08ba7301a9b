const BaseRepository = require('./base.repository')
const { ContractRiskGrpEntity, metadata } = require('../entities/contract-risk-grp.entity')

class ContractRiskGrpRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = ContractRiskGrpEntity

  /**
   * Create new ContractRiskGrpEntity based on the provided parameters.
   * @param {ContractRiskGrpEntity} data - Data of entity
   * @returns {ContractRiskGrpEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save IrChargesEntities based on the provided parameters.
   * @param {ContractRiskGrpEntity & ContractRiskGrpEntity[]} data - Data of entity
   * @returns {Promise<ContractRiskGrpEntity & ContractRiskGrpEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {ContractRiskGrpEntity} options.where - Where conditions.
   * @param {ContractRiskGrpEntity} options.order - Order condition.
   * @returns {Promise<ContractRiskGrpEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {ContractRiskGrpEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {ContractRiskGrpEntity} options.order - Order conditions.
   * @returns {Promise<ContractRiskGrpEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entity based on the provided parameters.
   * @param {ContractRiskGrpEntity} where - Where conditions for querying entities.
   * @returns {Promise<ContractRiskGrpEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {ContractRiskGrpEntity} options.where - Where conditions.
   * @param {ContractRiskGrpEntity} options.data - Order conditions.
   * @returns {Promise<ContractRiskGrpEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = ContractRiskGrpRepository
