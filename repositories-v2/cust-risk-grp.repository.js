const BaseRepository = require('./base.repository')
const { CustRiskGrpEntity, metadata } = require('../entities/cust-risk-grp.entity')

class CustRiskGrpRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = CustRiskGrpEntity

  /**
   * Create new CustRiskGrpEntity based on the provided parameters.
   * @param {CustRiskGrpEntity} data - Data of entity
   * @returns {CustRiskGrpEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {CustRiskGrpEntity & CustRiskGrpEntity[]} data - Data of entity
   * @returns {Promise<CustRiskGrpEntity & CustRiskGrpEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {CustRiskGrpEntity} options.where - Where conditions.
   * @param {CustRiskGrpEntity} options.order - Order condition.
   * @returns {Promise<CustRiskGrpEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {CustRiskGrpEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {CustRiskGrpEntity} options.order - Order conditions.
   * @returns {Promise<CustRiskGrpEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entity based on the provided parameters.
   * @param {CustRiskGrpEntity} where - Where conditions for querying entities.
   * @returns {Promise<CustRiskGrpEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {CustRiskGrpEntity} options.where - Where conditions.
   * @param {CustRiskGrpEntity} options.data - Order conditions.
   * @returns {Promise<CustRiskGrpEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = CustRiskGrpRepository
