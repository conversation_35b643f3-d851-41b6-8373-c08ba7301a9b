const BaseRepository = require('./base.repository')
const { LoanStatusHstEntity, metadata } = require('../entities/loan-status-hst.entity')

class LoanStatusHstRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = LoanStatusHstEntity

  /**
   * Create new LoanStatusHstEntity based on the provided parameters.
   * @param {LoanStatusHstEntity} data - Data of entity
   * @returns {LoanStatusHstEntity} -  Entities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entities based on the provided parameters.
   * @param {LoanStatusHstEntity & LoanStatusHstEntity[]} data - Data of entity
   * @returns {Promise<LoanStatusHstEntity & LoanStatusHstEntity[]>} -  Entities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {LoanStatusHstEntity} options.where - Where conditions.
   * @param {LoanStatusHstEntity} options.order - Order condition.
   * @returns {Promise<LoanStatusHstEntity>} - A promise that resolves to an array of Entities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanStatusHstEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {LoanStatusHstEntity} options.order - Order conditions.
   * @returns {Promise<LoanStatusHstEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entities based on the provided parameters.
   * @param {LoanStatusHstEntity} where - Where conditions for querying entities.
   * @returns {Promise<LoanStatusHstEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanStatusHstEntity} options.where - Where conditions.
   * @param {LoanStatusHstEntity} options.data - Order conditions.
   * @returns {Promise<LoanStatusHstEntity[]>} -  Entities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = LoanStatusHstRepository
