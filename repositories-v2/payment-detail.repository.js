const BaseRepository = require('./base.repository')
const { PaymentDetailEntity, metadata } = require('../entities/payment-detail.entity')

class PaymentDetailRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = PaymentDetailEntity

  /**
   * Create new PaymentDetailEntity based on the provided parameters.
   * @param {PaymentDetailEntity} data - Data of entity
   * @returns {PaymentDetailEntity} -  Entities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entities based on the provided parameters.
   * @param {PaymentDetailEntity & PaymentDetailEntity[]} data - Data of entity
   * @returns {Promise<PaymentDetailEntity & PaymentDetailEntity[]>} -  Entities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {PaymentDetailEntity} options.where - Where conditions.
   * @param {PaymentDetailEntity} options.order - Order condition.
   * @returns {Promise<PaymentDetailEntity>} - A promise that resolves to an array of Entities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {PaymentDetailEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {PaymentDetailEntity} options.order - Order conditions.
   * @returns {Promise<PaymentDetailEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entities based on the provided parameters.
   * @param {PaymentDetailEntity} where - Where conditions for querying entities.
   * @returns {Promise<PaymentDetailEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {PaymentDetailEntity} options.where - Where conditions.
   * @param {PaymentDetailEntity} options.data - Order conditions.
   * @returns {Promise<PaymentDetailEntity[]>} -  Entities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = PaymentDetailRepository
