const BaseRepository = require('./base.repository')
const { BillOnDueEntity, metadata } = require('../entities/bill-on-due.entity')

class BillOnDueRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = BillOnDueEntity

  /**
   * Create new BillOnDueEntity based on the provided parameters.
   * @param {BillOnDueEntity} data - Data of entity
   * @returns {BillOnDueEntity} -  BillOnDueEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {BillOnDueEntity & BillOnDueEntity[]} data - Data of entity
   * @returns {Promise<BillOnDueEntity & BillOnDueEntity[]>} -  BillOnDueEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {BillOnDueEntity} options.where - Where conditions.
   * @param {BillOnDueEntity} options.order - Order condition.
   * @returns {Promise<BillOnDueEntity>} - A promise that resolves to an array of BillOnDueEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all BillOnDueEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {BillOnDueEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {BillOnDueEntity} options.order - Order conditions.
   * @returns {Promise<BillOnDueEntity[]>} - A promise that resolves to an array of BillOnDueEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by BillOnDueEntities based on the provided parameters.
   * @param {BillOnDueEntity} where - Where conditions for querying entities.
   * @returns {Promise<BillOnDueEntity[]>} - A promise that resolves to an array of BillOnDueEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update BillOnDueEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {BillOnDueEntity} options.where - Where conditions.
   * @param {BillOnDueEntity} options.data - Order conditions.
   * @returns {Promise<BillOnDueEntity[]>} -  BillOnDueEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = BillOnDueRepository
