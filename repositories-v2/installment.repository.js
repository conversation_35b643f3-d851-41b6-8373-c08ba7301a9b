const BaseRepository = require('./base.repository')
const { InstallmentEntity, metadata } = require('../entities/installment.entity')

class InstallmentRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = InstallmentEntity

  /**
   * Create new InstallmentEntity based on the provided parameters.
   * @param {InstallmentEntity} data - Data of entity
   * @returns {InstallmentEntity} -  InstallmentEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {InstallmentEntity & InstallmentEntity[]} data - Data of entity
   * @returns {Promise<InstallmentEntity & InstallmentEntity[]>} -  InstallmentEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {InstallmentEntity} options.where - Where conditions.
   * @param {InstallmentEntity} options.order - Order condition.
   * @returns {Promise<InstallmentEntity>} - A promise that resolves to an array of InstallmentEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all InstallmentEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {InstallmentEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {InstallmentEntity} options.order - Order conditions.
   * @returns {Promise<InstallmentEntity[]>} - A promise that resolves to an array of InstallmentEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by InstallmentEntities based on the provided parameters.
   * @param {InstallmentEntity} where - Where conditions for querying entities.
   * @returns {Promise<InstallmentEntity[]>} - A promise that resolves to an array of InstallmentEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update InstallmentEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {InstallmentEntity} options.where - Where conditions.
   * @param {InstallmentEntity} options.data - Order conditions.
   * @returns {Promise<InstallmentEntity[]>} -  InstallmentEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = InstallmentRepository
