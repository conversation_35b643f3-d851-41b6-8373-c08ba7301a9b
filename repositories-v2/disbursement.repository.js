const BaseRepository = require('./base.repository')
const { DisbursementEntity, metadata } = require('../entities/disbursement.entity')

class DisbursementRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = DisbursementEntity

  /**
   * Create new DisbursementEntity based on the provided parameters.
   * @param {DisbursementEntity} data - Data of entity
   * @returns {DisbursementEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entity based on the provided parameters.
   * @param {DisbursementEntity & DisbursementEntity[]} data - Data of entity
   * @returns {Promise<DisbursementEntity & DisbursementEntity[]>} -  IrEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {DisbursementEntity} options.where - Where conditions.
   * @param {DisbursementEntity} options.order - Order condition.
   * @returns {Promise<DisbursementEntity>} - A promise that resolves to an array of IrEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {DisbursementEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {DisbursementEntity} options.order - Order conditions.
   * @returns {Promise<DisbursementEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entity based on the provided parameters.
   * @param {DisbursementEntity} where - Where conditions for querying entities.
   * @returns {Promise<DisbursementEntity[]>} - A promise that resolves to an array of IrEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update entity based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {DisbursementEntity} options.where - Where conditions.
   * @param {DisbursementEntity} options.data - Order conditions.
   * @returns {Promise<DisbursementEntity[]>} -  IrEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = DisbursementRepository
