const { baseSave, baseSelectV2, baseUpdateV2 } = require('../repositories/base.repo')
const _ = require('lodash')
class BaseRepository {
  static metadata
  static EntityClass

  static create(data) {
    return new this.EntityClass(_.omit(data, [this.metadata.primaryKey]))
  }

  static async save(data) {
    return baseSave(data, this.metadata)
  }

  static castArrayToEntities(arrayOfData = []) {
    return arrayOfData.map((data) => new this.EntityClass(data))
  }

  static async findAll({ where, limit, offset, order }) {
    const listResult = await baseSelectV2({
      tableName: this.metadata.tableName,
      limit,
      offset,
      queryWhereObj: where,
      queryOrderObj: order
    })
    return this.castArrayToEntities(listResult)
  }

  static async findAllBy(where) {
    const listResult = await baseSelectV2({
      tableName: this.metadata.tableName,
      queryWhereObj: where
    })
    return this.castArrayToEntities(listResult)
  }

  static async findOne({ where, order }) {
    const result = await baseSelectV2({
      tableName: this.metadata.tableName,
      limit: 1,
      queryWhereObj: where,
      queryOrderObj: order
    })
    if (result.length) {
      return new this.EntityClass(result?.[0])
    }
    return undefined
  }

  static async findOneBy(where) {
    const result = await baseSelectV2({
      tableName: this.metadata.tableName,
      limit: 1,
      queryWhereObj: where
    })
    if (result.length) {
      return new this.EntityClass(result?.[0])
    }
    return undefined
  }

  static async update({ where, data }) {
    this.metadata.updatedDateColumn && (data[this.metadata.updatedDateColumn] = new Date())
    const listResult = await baseUpdateV2({
      tableName: this.metadata.tableName,
      queryWhereObj: where,
      fieldsValueObj: data,
      isReturn: true
    })
    return this.castArrayToEntities(listResult)
  }
}

module.exports = BaseRepository
