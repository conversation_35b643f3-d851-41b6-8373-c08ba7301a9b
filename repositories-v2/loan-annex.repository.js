const BaseRepository = require('./base.repository')
const { LoanAnnexEntity, metadata } = require('../entities/loan-annex.entity')

class LoanAnnexRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = LoanAnnexEntity

  /**
   * Create new LoanAnnexEntity based on the provided parameters.
   * @param {LoanAnnexEntity} data - Data of entity
   * @returns {LoanAnnexEntity} -  LoanAnnexEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save LoanAnnexEntities based on the provided parameters.
   * @param {LoanAnnexEntity & LoanAnnexEntity[]} data - Data of entity
   * @returns {Promise<LoanAnnexEntity & LoanAnnexEntity[]>} -  LoanAnnexEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {LoanAnnexEntity} options.where - Where conditions.
   * @param {LoanAnnexEntity} options.order - Order condition.
   * @returns {Promise<LoanAnnexEntity>} - A promise that resolves to an array of LoanAnnexEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all LoanAnnexEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanAnnexEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {LoanAnnexEntity} options.order - Order conditions.
   * @returns {Promise<LoanAnnexEntity[]>} - A promise that resolves to an array of LoanAnnexEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by LoanAnnexEntities based on the provided parameters.
   * @param {LoanAnnexEntity} where - Where conditions for querying entities.
   * @returns {Promise<LoanAnnexEntity[]>} - A promise that resolves to an array of LoanAnnexEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update LoanAnnexEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanAnnexEntity} options.where - Where conditions.
   * @param {LoanAnnexEntity} options.data - Order conditions.
   * @returns {Promise<LoanAnnexEntity[]>} -  LoanAnnexEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = LoanAnnexRepository
