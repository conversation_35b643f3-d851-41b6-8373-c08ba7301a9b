const BaseRepository = require('./base.repository')
const { LoanContractLimitEntity, metadata } = require('../entities/loan-contract-limit.entity')

class LoanContractLimitRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = LoanContractLimitEntity

  /**
   * Create new LoanContractLimitEntity based on the provided parameters.
   * @param {LoanContractLimitEntity} data - Data of entity
   * @returns {LoanContractLimitEntity} -  Entities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entities based on the provided parameters.
   * @param {LoanContractLimitEntity & LoanContractLimitEntity[]} data - Data of entity
   * @returns {Promise<LoanContractLimitEntity & LoanContractLimitEntity[]>} -  Entities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {LoanContractLimitEntity} options.where - Where conditions.
   * @param {LoanContractLimitEntity} options.order - Order condition.
   * @returns {Promise<LoanContractLimitEntity>} - A promise that resolves to an array of Entities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all by Entities based on the provided parameters.
   * @param {LoanContractLimitEntity} where - Where conditions for querying entities.
   * @returns {Promise<LoanContractLimitEntity>} - A promise that resolves to an array of Entities.
   */
  static async findOneBy(where) {
    return super.findOneBy(where)
  }

  /**
   * Find all Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanContractLimitEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {LoanContractLimitEntity} options.order - Order conditions.
   * @returns {Promise<LoanContractLimitEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entities based on the provided parameters.
   * @param {LoanContractLimitEntity} where - Where conditions for querying entities.
   * @returns {Promise<LoanContractLimitEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanContractLimitEntity} options.where - Where conditions.
   * @param {LoanContractLimitEntity} options.data - Order conditions.
   * @returns {Promise<LoanContractLimitEntity[]>} -  Entities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = LoanContractLimitRepository
