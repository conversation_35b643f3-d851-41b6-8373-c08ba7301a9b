const BaseRepository = require('./base.repository')
const { PaymentEntity, metadata } = require('../entities/payment.entity')

class PaymentRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = PaymentEntity

  /**
   * Create new PaymentEntity based on the provided parameters.
   * @param {PaymentEntity} data - Data of entity
   * @returns {PaymentEntity} -  Entities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save Entities based on the provided parameters.
   * @param {PaymentEntity & PaymentEntity[]} data - Data of entity
   * @returns {Promise<PaymentEntity & PaymentEntity[]>} -  Entities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {PaymentEntity} options.where - Where conditions.
   * @param {PaymentEntity} options.order - Order condition.
   * @returns {Promise<PaymentEntity>} - A promise that resolves to an array of Entities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {PaymentEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {PaymentEntity} options.order - Order conditions.
   * @returns {Promise<PaymentEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by Entities based on the provided parameters.
   * @param {PaymentEntity} where - Where conditions for querying entities.
   * @returns {Promise<PaymentEntity[]>} - A promise that resolves to an array of Entities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update Entities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {PaymentEntity} options.where - Where conditions.
   * @param {PaymentEntity} options.data - Order conditions.
   * @returns {Promise<PaymentEntity[]>} -  Entities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = PaymentRepository
