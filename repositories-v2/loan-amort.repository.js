const BaseRepository = require('./base.repository')
const { LoanAmortEntity, metadata } = require('../entities/loan-amort.entity')

class LoanAmortRepository extends BaseRepository {
  static metadata = metadata
  static EntityClass = LoanAmortEntity

  /**
   * Create new IrEntity based on the provided parameters.
   * @param {LoanAmortEntity} data - Data of entity
   * @returns {LoanAmortEntity} -  IrEntities.
   */
  static create(data) {
    return super.create(data)
  }

  /**
   * Save LoanAmortEntities based on the provided parameters.
   * @param {LoanAmortEntity & LoanAmortEntity[]} data - Data of entity
   * @returns {Promise<LoanAmortEntity & LoanAmortEntity[]>} -  LoanAmortEntities.
   */
  static async save(data) {
    return super.save(data)
  }

  /**
   *  Find One entity
   * @param {Object} options - Options for querying entities.
   * @param {LoanAmortEntity} options.where - Where conditions.
   * @param {LoanAmortEntity} options.order - Order condition.
   * @returns {Promise<LoanAmortEntity>} - A promise that resolves to an array of LoanAmortEntities.
   */
  static async findOne({ where, order }) {
    return super.findOne({ where, order })
  }

  /**
   * Find all LoanAmortEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanAmortEntity} options.where - Where conditions.
   * @param {number} options.limit - Limit the number of results.
   * @param {number} options.offset - Offset for paginated results.
   * @param {LoanAmortEntity} options.order - Order conditions.
   * @returns {Promise<LoanAmortEntity[]>} - A promise that resolves to an array of LoanAmortEntities.
   */
  static async findAll({ where, limit, offset, order }) {
    return super.findAll({ where, limit, offset, order })
  }

  /**
   * Find all by LoanAmortEntities based on the provided parameters.
   * @param {LoanAmortEntity} where - Where conditions for querying entities.
   * @returns {Promise<LoanAmortEntity[]>} - A promise that resolves to an array of LoanAmortEntities.
   */
  static async findAllBy(where) {
    return super.findAllBy(where)
  }

  /**
   * Update LoanAmortEntities based on the provided parameters.
   * @param {Object} options - Options for querying entities.
   * @param {LoanAmortEntity} options.where - Where conditions.
   * @param {LoanAmortEntity} options.data - Order conditions.
   * @returns {Promise<LoanAmortEntity[]>} -  LoanAmortEntities.
   */
  static async update({ where, data }) {
    return super.update({ where, data })
  }
}

module.exports = LoanAmortRepository
