# LMS-MC-SERVICE
Lms-mc-service là service được sử dụng để làm các nghiệp vụ hạn mức merchant

# Getting Started
## Định nghĩa
* Tài khoản hạn mức có 03 trạng thái như sau:
    * 0 là đang bị block
    * 1 là đang active
    * 2 là đang bị removed.
* Trạng thái của khoản vay trên bảng disbursement được định nghĩa như sau:
    * INIT: Là đang khởi tạo khoản tiêu dùng, đang chờ kiểm tra các điều kiện.
    * DISBURMENT_IN_PROGESSING: Là trạng thái đang chờ để giải ngân.
    * ACTIVATED: Là đã giải ngân thành công và khoản tiêu dùng được kích hoạt.
* Đ<PERSON><PERSON> nghĩa type trong bảng installment
    * 1 là số tiền gốc
    * 2 là số tiền lãi
    * 3 là lãi quá hạn của gốc
    * 4 là lãi quá hạn của lãi
    * 5 là các loại phí/ phạt
* Trạng thái payment_status trong installment
    * 0 là đã được thanh toán hết
    * 1 là chưa được thanh toán
    * 2 là đang được thanh toán nhưng chưa hết
* Định nghĩa type trong bảng ir_charge
    * 1 là lãi xuất kỳ
    * 2 là lãi xuất gốc quá hạn
    * 3 là lãi xuất lãi quá hạn  
1. API health check service
````
GET: /lms-mc/v1/healthcheck
````
# 2. API khởi tạo hạn mức
```
POST: /lms-mc/v1/merchant-limit/create
```
Request body mẫu:
```
{
    "productCode":"CREDITMCLIMITPR123",
    "amount": 120000000,
    "custId": 1,
    "contractNumber":"CREDIT_MC_000005",
    "tenor":12,
    "periodicity": 1,
    "graceDayNumber":15,
    "billDay": 20,
    "startDate":"2021-05-06",
    "endDate":"2022-05-06",
    "irCharge":[
        {
            "irCode":"IR001",
            "irName":"Lãi xuất theo kỳ",
            "irType":1,
            "irValue":0.08
        },
        {
            "irCode":"IR002",
            "irName":"Lãi xuất gốc quá hạn",
            "irType":2,
            "irValue":0.12
        },
        {
            "irCode":"IR003",
            "irName":"Lãi xuất lãi quá hạn",
            "irType":3,
            "irValue":0.1
        }
    ]
}
```
* Trong đó:
    - productCode: Mã sản phẩm
    - amount: Số tiền hạn mức khách hàng được cấp
    - custId: Mã khách hàng
    - contractNumber: Số hợp đồng hạn mức
    - periodicity: Tính chu kỳ hạn mức

3. API kiểm tra hạn mức
```
POST: /lms-mc/v1/merchant-limit/available
```
Request body mẫu:
```
{
    "contractNumber":"CN001"
}
```
4. API tạo khế ước nhận nợ - KUNN
```
POST: /lms-mc/v1/debt-ack-contract/create
```
Request body mẫu:
```
{
    "productCode":"CREDITMCLIMITPR111",
    "amount": ***********,
    "contractNumber":"CREDIT_MC_0001",
    "productChildCode": "CREDITMCLIMITPR222",
    "notiDateNum": -5,
    "mcLimitId": 1,
    "partnerCode": "KIOTVIET",
    "beneficiaryName": "Tran Van A",
    "bankCode": "SAC",
    "bankName": "Sacombank",
    "bankAccount": "***************"
}
```
* Trong đó:
    - productCode: Mã sản phẩm
    - amount: Số tiền KUNN
    - tenor: Kỳ hạn giữa mỗi chu kỳ KUNN
    - contractNumber: Số hợp đồng hạn mức
    - productChildCode: Mã sản phẩm của KUNN
    - notiDateNum: Số ngày thông báo trước cho KH kỳ thanh toán
    - mcLimitId: Mã hạn mức

5. API giải ngân và kích hoạt khế ước - KUNN
```
POST: /lms-mc/v1/debt-ack-contract/active
```
Request body mẫu:
```
{
    "debtAckContractId": 4
}
```
* Trong đó:
    - debtAckContractId: Mã khế ước

6. API thanh toan
```
POST: /lms-mc/v1/payment/usage
```
Request body mẫu:
````
{
    "installmentAmort": 8000,
    "partnerCode":"KIOTVIET",
    "contractNumber":"CTN001",
    "paymentDate":"2021-06-20"
}
````
* Trong đó:
    - installmentAmort: số tiền khách hàng thanh toán
    - partnerCode: mã đối tác
    - contractNumber: số hợp đồng hạn mức
    - paymentDate: ngày thực hiện thanh toán
7. API in bill
```
POST: /lms-mc/v1/bill/usage
```
Request body mẫu:
````
{
    "contractNumber":"CREDIT_MC_0001",
    "billDate":"2021-06-20"
}
````
* Trong đó:
    - contractNumber: số hợp đồng hạn mức
    - billDate: ngày thực hiện in bill

8. Api tạm khóa hạn mức
```
PUT: /lms-mc/v1/merchant-limit/:mcLimitId/lock
```
* Trong đó: mcLimitId: là mã hạn mức
9. Api mở khóa hạn mức
```
PUT: /lms-mc/v1/merchant-limit/:mcLimitId/unlock
```
* Trong đó: mcLimitId: là mã hạn mức
10. Api hủy hạn mức
```
DELETE: /lms-mc/v1/merchant-limit/:mcLimitId/remove
```
* Trong đó: mcLimitId: là mã hạn mức

# Build with
Lms-mc-service được build trên nodejs.

# Vesioning
Version 1.0


# Author: 
* Lê Thanh Tùng
* Email: <EMAIL>
* Contributors: N/A

# License
This project is belong to ENVFC
##########
