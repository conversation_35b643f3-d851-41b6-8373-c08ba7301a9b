{"name": "lms-mc-service", "version": "1.0.0", "description": "LMS merchant service", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "set NODE_ENV=dev&& nodemon main.js", "uat": "set NODE_ENV=uat&& nodemon main.js", "uat-mac": "NODE_ENV=uat nodemon main.js", "dev-mac": "NODE_ENV=dev nodemon main.js", "prod": "set NODE_ENV=prod&& nodemon main.js", "start": "node main.js", "lint": "eslint --fix --ext .js,.jsx .", "format": "prettier --write \"**/*.js\""}, "repository": {"type": "git", "url": "git+https://github.com/EVNFC/lms-mc-service.git"}, "author": "Tung<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/EVNFC/lms-mc-service/issues"}, "homepage": "https://github.com/EVNFC/lms-mc-service#readme", "dependencies": {"aws-sdk": "^2.1179.0", "axios": "^1.6.2", "camelcase-keys": "^6.2.2", "convert-excel-to-json": "^1.7.0", "crypto-js": "^4.2.0", "csv-parser": "^3.0.0", "csvtojson": "^2.0.10", "date-diff": "^0.2.2", "dateformat": "^4.5.1", "decimal.js": "^10.6.0", "docxtemplater": "^3.31.2", "dotenv": "^16.0.1", "express": "^4.17.1", "express-fileupload": "^1.4.3", "form-data": "^4.0.0", "hashmap": "^2.4.0", "libreoffice-convert": "^1.4.1", "lodash": "^4.17.21", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "nodemon": "^3.0.1", "path": "^0.12.7", "pg": "^8.6.0", "pg-format": "^1.0.4", "pino": "^8.19.0", "pizzip": "^3.1.1", "redis": "^4.6.15", "stompit": "^1.0.0", "uuid": "^9.0.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@types/lodash": "^4.14.202", "eslint": "^8.21.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-promise": "^6.0.0", "pino-pretty": "^11.0.0", "prettier": "^3.2.5"}, "overrides": {"convert-excel-to-json": {"xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "cliss": "^0.0.9"}}