const common = require('../utils/common')
const FormData = require('form-data')

const signStatementMisa = async function (contractNumber, fileBuffer) {
  try {
    const dataSignStatement = new FormData()
    dataSignStatement.append('contractNumber', contractNumber)
    dataSignStatement.append('file', fileBuffer)
    const bssEsignUrl = `${global.bssEsigningUrl}/esigning/internal/misa/sign-statement`
    const rsCheck = await common.postApiV2(bssEsignUrl, dataSignStatement, dataSignStatement.getHeaders())
    if (rsCheck.code == 0) {
      console.log('[LMS-MC] signStatementMisa successfully')
      return rsCheck.data
    }
    console.log('[LMS-MC] Error signStatementMisa:', JSON.stringify(rsCheck))
    return null
  } catch (e) {
    console.log('[LMS-MC] Error signStatementMisa: ', e)
    return null
  }
}
const getFileUrl = function (contractNumber) {
  return `${global.bssEsigningUrl}/esigning/internal/contract/${contractNumber}`
}
module.exports = {
  signStatementMisa,
  getFileUrl
}
