const common = require('../utils/common')

const getProductInfo = async function (productCode) {
  try {
    const url =
      global.config.basic.product[global.env] + '/product/v1/productInfo/?prdctCode=' + encodeURIComponent(productCode)
    const result = await common.callAxios(url, {}, 'GET')
    return result?.data?.data
  } catch (err) {
    console.log(`getProductInfo error: ${err.message}`)
    console.log(err)
    return {}
  }
}
async function getProduct(productCode) {
  const url = `${global.config.basic.product[global.env]}/product/v1/productInfo/?prdctCode=` + productCode
  return common.getAPI(url, {}).then((data) => {
    if (data.status == 404) {
      return null
    }
    return data.data
  })
}

async function getVoucherPromotion(promotionCode) {
  const url =
    `${global.config.basic.product[global.env]}/product/v1/voucher/mc/promotion/detail?mcPromotionCode=` + promotionCode
  return common.getAPI(url, {}).then((data) => {
    if (data.status == 404) {
      throw new Error('Product code not found')
    }
    return data.data
  })
}
async function getProductVoucherV2(productCode, amount) {
  const url = `${global.config.basic.product[global.env]}/product/v2/productInfo?prdctCode=${productCode}&amount=${amount}`
  return common.getAPI(url, {}).then((data) => {
    if (data.status == 404) {
      return null
    }
    return data.data
  })
}
async function getIrChargeKunnProduct(productCode) {
  const url = `${global.config.basic.product[global.env]}/product/v2/irCharge?productCode=${productCode}`
  return common.getAPI(url, {}).then((data) => {
    if (data.status == 404) {
      return null
    }
    return data.data
  })
}
async function getProductInfoV4(productCode) {
  const url = `${global.config.basic.product[global.env]}/product/v4/productInfo?prdctCode=${productCode}`
  return common.getAPI(url, {}).then((data) => {
    if (data.status == 404) {
      return null
    }
    return data.data
  })
}
module.exports = {
  getProductVoucherV2,
  getProductInfo,
  getProduct,
  getVoucherPromotion,
  getIrChargeKunnProduct,
  getProductInfoV4
}
