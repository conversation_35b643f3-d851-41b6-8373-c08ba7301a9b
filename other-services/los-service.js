const common = require('../utils/common')
const constant = require('../utils/constant')
const moment = require('moment')
const { isLosUnitedPartnerCode } = require('../utils/helper')
const {REQUEST_EVENT_NAME} = require("../utils/constant");
const genCalculatorRepayment = async function (body) {
  try {
    const url = global.losServiceUrl + global.losCfg.genCalculRepayment
    const rs = await common.postAPI(url, body, undefined, { 'Content-Type': 'application/json' })
    if (rs != undefined && rs.data != undefined) {
      return rs.data
    } else {
      return
    }
  } catch (e) {
    console.log('[LMS-MC] Error genCalculatorRepayment: ', e)
  }
}
const activateKunn = async function (url, body) {
  const rs = await common.postAPI(url, body, undefined, { 'Content-Type': 'application/json' })
  if (rs != undefined && rs.data != undefined) {
    return rs.data
  } else {
    return {}
  }
}
async function callActiveKunn(loanAccountObj) {
  let url, body
  if (isLosUnitedPartnerCode(loanAccountObj.partner_code)) {
    url = global.losUnitedServiceUrl + '/contract/update-status'
    body = {
      cust_id: loanAccountObj.cust_id,
      los_type: 'LOS',
      contract_number: loanAccountObj.debt_ack_contract_number,
      status: 'ACT',
      status_date: moment(loanAccountObj.active_date).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      start_date: moment(loanAccountObj.active_date).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      end_date: moment(loanAccountObj.end_date).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      createdUser: 'LMS_MC',
      ownerId: 1
    }
  } else {
    url = global.losServiceUrl + global.losCfg.activateKunn
    body = {
      contractNumber: loanAccountObj.contract_number,
      kunnNumber: loanAccountObj.debt_ack_contract_number
    }
  }
  common
    .postAPI(
      url,
      body,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: constant.REQUEST_EVENT_NAME.ACTIVE_KUNN_LOS }
    )
    .then((data) => {
      console.log('[LMS-MC] Response call active Kunn: ', JSON.stringify(data))
    })
    .catch((err) => {
      console.log('[LMS-MC] Response call active Kunn: ', err.message)
    })
}
async function callDeactiveKunn(loanAccountObj, terminationDate) {
  let url, body
  if (isLosUnitedPartnerCode(loanAccountObj.partner_code)) {
    url = global.losUnitedServiceUrl + '/contract/update-status'
    body = {
      cust_id: loanAccountObj.cust_id,
      los_type: 'LOS',
      contract_number: loanAccountObj.debt_ack_contract_number,
      status: 'TER',
      status_date: moment(loanAccountObj.active_date).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      start_date: moment(loanAccountObj.active_date).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      end_date: moment(terminationDate).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      createdUser: 'LMS_MC',
      ownerId: 1
    }
  } else {
    url = global.losServiceUrl + global.losCfg.deactivateKunn
    body = {
      contractNumber: loanAccountObj.contract_number,
      kunnNumber: loanAccountObj.debt_ack_contract_number
    }
  }
  common
    .postAPI(
      url,
      body,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: constant.REQUEST_EVENT_NAME.UPDATE_TER_LOS }
    )
    .then((data) => {
      console.log('[LMS-MC] Response call deactive Kunn: ', JSON.stringify(data))
    })
    .catch((err) => {
      console.log('[LMS-MC] Error call deactive Kunn: ', err)
    })
}

async function callCancelKunn(loanAccountObj, cancelDate) {
  let url, body
  if (isLosUnitedPartnerCode(loanAccountObj.partner_code)) {
    url = global.losUnitedServiceUrl + '/contract/update-status'
    body = {
      cust_id: loanAccountObj.cust_id,
      los_type: 'LOS',
      contract_number: loanAccountObj.debt_ack_contract_number,
      status: 'ANN',
      status_date: moment(cancelDate).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      start_date: moment(cancelDate).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      end_date: moment(cancelDate).format(constant.DATE_FORMAT.YYYYMMDD_HHmmss),
      createdUser: 'LMS_MC',
      ownerId: 1
    }
  } else {
    url = global.losServiceUrl + global.losCfg.cancelKunn
    body = {
      contractNumber: loanAccountObj.contract_number,
      kunnNumber: loanAccountObj.debt_ack_contract_number
    }
  }

  common
    .postAPI(
      url,
      body,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: constant.REQUEST_EVENT_NAME.UPDATE_ANN_LOS }
    )
    .then((data) => {
      console.log('[LMS-MC] Response call cancel Kunn: ', JSON.stringify(data))
    })
    .catch((err) => {
      console.log('[LMS-MC] Error call cancel Kunn: ', err)
    })
}

const getSmeInfo = async function (contractNumber) {
  try {
    const losUrl = `${global.losServiceUrl}/sme/smeInfo?contractNumber=${contractNumber}`
    const rs = await common.getAPI(losUrl)
    if (rs.code == 0) {
      return rs.data
    }
    return null
  } catch (e) {
    console.log('[LMS-MC] Error getSmeInfo: ', e)
    return null
  }
}
const getContractInfo = async function (contractNumber) {
  try {
    const losUrl = `${global.losUnitedServiceUrl}/contract/info?contractNumber=${contractNumber}`
    const rs = await common.getAPI(losUrl)
    if (rs.code == 'SUCCESS') {
      return rs.data
    }
    return null
  } catch (e) {
    console.log('[LMS-MC] Error getContractInfo: ', e)
    return null
  }
}

const callUpdatePayment = async ({ contractNumber, kunnNumber }) => {
  try {
    const losUrl = `${global.losServiceUrl}/kunn/update-payment`
    const rs = await common.postAPI(losUrl, { contractNumber, kunnNumber })
    if (rs.code == 0) {
      console.log(`[LMS-MC][SME][KUNN][callUpdatePayment] success contractNumber: ${contractNumber}, kunnNumber: ${kunnNumber}`)
      return rs.data
    }
    return null
  } catch (e) {
    console.log('[LMS-MC][callUpdatePayment] Error callUpdatePayment: ', e)
    return null
  }
}

const callReceivedPayment = async ({ contractNumber, kunnNumber, paymentDate, paymentAmount }) => {
  try {
    const losUrl = `${global.losServiceUrl}/kunn/received-payment`
    const rs = await common.postAPI(losUrl, { contractNumber, kunnNumber, paymentDate, paymentAmount })
    if (rs.code == 0) {
      console.log(`[LMS-MC][SME][KUNN][callReceivedPayment] success contractNumber: ${contractNumber}, kunnNumber: ${kunnNumber}, paymentDate: ${paymentDate}, paymentAmount: ${paymentAmount}`)
      return rs.data
    }
    return null
  } catch (e) {
    console.log('[LMS-MC][callReceivedPayment] Error callReceivedPayment: ', e)
    return null
  }
}

const callReceivedRefundRequest = async ({ contract_number, cust_id, amount, start_date, end_date, lpi, int_amount, fee_amount, invoice_value, refund_value, created_by, paid_amount }) => {
  try {
    const losUrl = `${global.losServiceUrl}/kunn/refunds`
    const rs = await common.postAPI(losUrl, {
      contract_number, cust_id, amount,
      start_date, end_date, lpi, int_amount, fee_amount, invoice_value, refund_value,created_by, paid_amount
    }, null, { 'Content-type': 'application/json' }, { isLog: true, eventType: REQUEST_EVENT_NAME.CALLBACK_REFUND_REQUEST, contractNumber: contract_number })
    if (rs.code == 0) {
      console.log(`[LMS-MC][SME][KUNN][callReceivedRefundRequest] success contractNumber: ${contract_number}`)
      return rs.data
    }
    return null
  } catch (e) {
    console.log('[LMS-MC][callReceivedRefundRequest] Error callReceivedRefundRequest: ', e)
    return null
  }
}

const callRefundRequestStatus = async ({ contract_number, is_success }) => {
  try {
    const losUrl = `${global.losServiceUrl}/kunn/refunds/callback`
    const rs = await common.postAPI(losUrl, {
      contract_number, is_success
    }, null, { 'Content-type': 'application/json' }, { isLog: true, eventType: REQUEST_EVENT_NAME.CALLBACK_REFUND_REQUEST_STATUS, contractNumber: contract_number })
    if (rs.code == 0) {
      console.log(`[LMS-MC][SME][KUNN][callRefundRequestStatus] success contractNumber: ${contract_number}`)
      return rs.data
    }
    return null
  } catch (e) {
    console.log('[LMS-MC][callRefundRequestStatus] Error callRefundRequestStatus: ', e)
    return null
  }
}

module.exports = {
  genCalculatorRepayment,
  activateKunn,
  getSmeInfo,
  callDeactiveKunn,
  callActiveKunn,
  callCancelKunn,
  getContractInfo,
  callUpdatePayment,
  callReceivedPayment,
  callReceivedRefundRequest,
  callRefundRequestStatus,
}
