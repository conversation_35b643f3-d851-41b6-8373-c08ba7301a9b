const common = require('../utils/common')
const { REQUEST_EVENT_NAME } = require('../utils/constant')

async function callReceiveMoneyBE(contractNumber, amount, transactionId, appId = 'EVC') {
  try {
    if (!global.config?.data?.mobileHost[appId]) {
      return;
    }

    const url =
      global.config.basic[global.config?.data?.mobileHost[appId]][global.env] +
      global.config?.data?.mobileReceiveMoney[appId]

    const body = {
      contract_number: contractNumber,
      amount,
      transaction_id: transactionId
    }
    common
      .postAPI(
        url,
        body,
        undefined,
        { 'Content-Type': 'application/json' },
        { isLog: true, eventType: REQUEST_EVENT_NAME.CALL_RECEIVE_MONEY_BE }
      )
      .then((rs) => {
        console.log('[LMS-MC] call callReceiveMoneyBE api: ', rs)
      })
      .catch((err) => {
        console.log('[LMS-MC] Error call callReceiveMoneyBE api: ', err)
      })
  } catch (error) {
    console.log('[LMS-MC] Error at callReceiveMoneyBE: ', error)
  }
}

async function callUpdateStatusBE(contractNumber, status) {
  const url = `${global.voucherMobileBeUrl}/salary_advance/update-status-ewa`
  const body = {
    contract_number: contractNumber,
    status
  }
  common
    .postAPI(
      url,
      body,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: REQUEST_EVENT_NAME.CALL_UPDATE_STATUS_BE }
    )
    .then((rs) => {
      console.log('[LMS-MC] call callUpdateStatusBE api: ', rs)
    })
    .catch((err) => {
      console.log('[LMS-MC] Error call callUpdateStatusBE api: ', err)
    })
}

async function callNotifyDpd(requestBody) {
  const url = `${global.voucherMobileBeUrl}/notification/saveDueNotifications`
  common
    .postAPI(
      url,
      requestBody,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: REQUEST_EVENT_NAME.CALL_NOTIFY_DPD_BE }
    )
    .then((rs) => {
      console.log('[LMS-MC] call callNotifyDpd api: ', rs)
    })
    .catch((err) => {
      console.log('[LMS-MC] Error call callNotifyDpd api: ', err)
    })
}

async function callConfirmAnnex(requestBody) {
  const url = `${global.voucherMobileBeUrl}/salary_advance/requestConfirmUpdateEmi`
  common
    .postAPI(
      url,
      requestBody,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: REQUEST_EVENT_NAME.CALL_CONFIRM_ANNEX_BE }
    )
    .then((rs) => {
      console.log('[LMS-MC] call callConfirmAnnex api: ', rs)
    })
    .catch((err) => {
      console.log('[LMS-MC] Error call callConfirmAnnex api: ', err)
    })
}

async function callNotiUpdateAnnexStatus({ contract_number, annex_status, termination_date, total_payment }) {
  const body = {
    contract_number,
    annex_status,
    termination_date,
    total_payment
  }
  const url = `${global.voucherMobileBeUrl}/salary_advance/update-annex-status`
  common
    .postAPI(
      url,
      body,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: REQUEST_EVENT_NAME.CALL_UPDATE_STATUS_ANNEX_BE }
    )
    .then((rs) => {
      console.log('[LMS-MC] call callNotiUpdateAnnexStatus api: ', rs)
    })
    .catch((err) => {
      console.log('[LMS-MC] Error call callNotiUpdateAnnexStatus api: ', err)
    })
}
module.exports = {
  callReceiveMoneyBE,
  callUpdateStatusBE,
  callNotifyDpd,
  callConfirmAnnex,
  callNotiUpdateAnnexStatus
}
