const common = require('../utils/common')
const { REQUEST_EVENT_NAME, DATE_FORMAT } = require('../utils/constant')

async function saveActionAudit(
  contractNumber,
  { actionAuditType, actionCodeType },
  { startDateRun, endDateRun, title, createdUser = 'system', command = 'Success' }
) {
  try {
    const actionAuditConfig = global.configActionAudit[actionAuditType]
    const sendData = {
      contractNumber,
      systemType: actionAuditConfig.code_type,
      stepCode: actionAuditConfig.value_code,
      actionCode: actionAuditConfig[actionCodeType].value_code,
      entryDate: common.formatDate({ date: startDateRun, format: DATE_FORMAT.YYYYMMDD_HHmmss }),
      updateStatusDate: common.formatDate({ date: endDateRun, format: DATE_FORMAT.YYYYMMDD_HHmmss }),
      title,
      involveParty: createdUser,
      value: command
    }
    const url = `${global.config.basic.actionAudit[global.env]}/action-audit/v1/status-history`
    return common.postAPI(
      url,
      sendData,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: REQUEST_EVENT_NAME.SAVE_ACTION_AUDIT, contractNumber }
    )
  } catch (error) {
    console.log('ERROR save action audit', error)
  }
}

module.exports = {
  saveActionAudit
}
