const common = require('../utils/common')

const genAccessToken = function (url, apiKey) {
  common.postAPI(url, { api_key: apiKey }, undefined, { 'Content-Type': 'text/plain' }).then((rs) => {
    console.log('[LMS-MC] gen access token: ', JSON.stringify(rs))
    const response = JSON.stringify(rs)
    if (response != undefined && response.data != undefined) {
      return response.data.access_token
    }
  })
}
const createLead = function (callCenterCfg, payload) {
  // return new Promise(function (resolve, reject) {
  //     try {
  common
    .postAPI(callCenterCfg.host + callCenterCfg.urlGenToken, { api_key: callCenterCfg.apiKey }, undefined, {
      'Content-Type': 'text/plain'
    })
    .then((rs) => {
      // console.log('[LMS-MC] gen access token: ',JSON.stringify(rs))
      console.log('[LMS-MC] gen access token: ', rs)
      // let response = JSON.stringify(rs)
      // if (response != undefined && response.data != undefined) {
      //     return response.data.access_token
      // }
      // let resToken = JSON.stringify(rs)
      const headers = {
        Authorization: 'Bearer ' + rs.data.access_token,
        'Content-Type': 'application/json'
      }
      common.postAPI(callCenterCfg.host + callCenterCfg.urlCreateLead, payload, undefined, headers).then((data) => {
        console.log('[LMS-MC] push call center: ', JSON.stringify(data))
        // resolve(data)
        return JSON.stringify(data)
      })
    })
  // console.log('url====', url)
  // console.log('token====', token)
  // console.log('payload====', payload)
  // let headers = {
  //     'Authorization': 'Bearer ' + token,
  //     'Content-Type': 'application/json'
  // }
  // common.postAPI(url, payload, undefined, headers).then(data => {
  //     console.log('[LMS-MC] push call center: ', JSON.stringify(data))
  //     // resolve(data)
  //     return JSON.stringify(data)
  // })
  //     } catch (error) {
  //         reject(error)
  //     }
  // })
}
module.exports = {
  genAccessToken,
  createLead
}
