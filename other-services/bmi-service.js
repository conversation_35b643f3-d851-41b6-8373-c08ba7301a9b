const common = require('../utils/common')
const { DATE_FORMAT, REQUEST_EVENT_NAME } = require('../utils/constant')
const moment = require('moment')
const {
  validateEmail,
  getCustomerPerAddress,
  getCustomerCurAddress,
  getCustomerPhoneNumber, hashMD5
} = require('../utils/helper')

const MAPPING_GENDER = {
  M: 'Nam',
  F: 'Nữ'
}

async function callGcnSkVtd(insuranceEntity, customer, loanAccount) {
  const insurBmiConfig = global.config.data.insurBmi
  const { baseUrl, endpointCreateGcn, partnerCode, partnerSecretKey } = insurBmiConfig

  const key_hash = hashMD5(insuranceEntity.debt_ack_contract_number + partnerSecretKey)
  const url = `${baseUrl}${endpointCreateGcn}`

  console.log('callGcnSkVtd customer info: ', JSON.stringify(customer))

  // Wrap pawnId - ex: ********** -> *********
  // pawnId đối tác đang để kiểu int32. nếu dùng số HĐ thì sẽ bị tràn số
  let pawnId =insuranceEntity.debt_ack_contract_number.slice(0, 1) + insuranceEntity.debt_ack_contract_number.slice(2);

  const body = {
    key_hash,
    kenh: partnerCode,
    ma_cn: 'EVN',
    ma_khoi: '',
    ma_dvi: '000',
    so_hd_kenh: insuranceEntity.debt_ack_contract_number,
    hop_dong_tin_dung: insuranceEntity.debt_ack_contract_number,
    so_id: 0,
    ngay_ht: Number(moment().format(DATE_FORMAT.YYYYMMDD3)),
    nv: '',
    so_hd: '',
    ma_dao: '',
    ten_dao: '',
    nt_tien: 'VND',
    gio_hl: moment().format(DATE_FORMAT.HHMM),
    ngay_hl: moment().format(DATE_FORMAT.DDMMYYYY),
    gio_kt: moment(insuranceEntity.end_date, DATE_FORMAT.DDMMYYYY).format(DATE_FORMAT.HHMM),
    ngay_kt: moment(insuranceEntity.end_date, DATE_FORMAT.DDMMYYYY).format(DATE_FORMAT.DDMMYYYY),
    ngay_cap: Number(moment().format(DATE_FORMAT.YYYYMMDD3)),
    nt_phi: 'VND',
    thue: 0,
    ttoan: 0,
    ten: customer.fullName,
    ngay_sinh: moment(customer.birthDate).format(DATE_FORMAT.YYYYMMDD3),
    so_cmt: customer.idNumber,
    phone: getCustomerPhoneNumber(customer),
    ng_huong: '',
    bien_xe: '',
    so_khung: '',
    so_may: '',
    hang_xe: '',
    hieu_xe: '',
    gia_xe_kb: 0,
    tien_vc: 0,
    pt_vc: 0,
    phi_vc: 0,
    c_k_sout: 'c',
    deviceId: '',
    nam_sx: 0,
    dchi: getCustomerPerAddress(customer),
    dchi_ng_huong: getCustomerCurAddress(customer),
    so_id_kenh: 0,
    noidung: '',
    pawn_id: +pawnId,
    email_kh: '',
    ma_ycbh: insuranceEntity.insur_doc_url || '',
    gioi_tinh: MAPPING_GENDER[customer.gender],
    so_thang_vay: +loanAccount.tenor,
    so_tien_vay: +insuranceEntity.amount
  }
  if (validateEmail(customer.email?.trim())) {
    body.email_kh = customer.email?.trim();
  }

  try {
    console.log('callGcnSkVtd body:', JSON.stringify(body))
    const result = await common.retry({
      func: () =>
        common.postAPIAsync(url, body, undefined, {}, {
          isLog: true,
          eventType: REQUEST_EVENT_NAME.CALL_GCN_BMI,
          contractNumber: insuranceEntity.debt_ack_contract_number
        }),
      retryCount: 5,
      retryInterval: 1000,
      retryBackoffFactor: 1
    })
    console.log('callGcnSkVtd result:', JSON.stringify(result))
    return {
      request_body: body,
      response_body: result
    }
  } catch (error) {
    console.log('ERROR callGcnSkVtd', JSON.stringify(error).slice(0, 100))
    return {
      request_body: body,
      response_body: {}
    }
  }
}

async function callDownloadGcn(gcnUrl) {
  try {
    console.log('callDownloadGcn url:', JSON.stringify(gcnUrl))
    const result = await common.getAPIV2Async(gcnUrl, {}, 'arraybuffer')
    if (result?.status == 200) {
      console.log('callDownloadGcn result: Success')
      return result?.data
    }
    // console.log('callDownloadGcn result:', JSON.stringify(result))
    throw new Error('ERROR GET FILE')
  } catch (error) {
    console.log('ERROR callDownloadGcn', JSON.stringify(error).slice(0, 100))
    throw error
  }
}
module.exports = {
  callGcnSkVtd,
  callDownloadGcn
}
