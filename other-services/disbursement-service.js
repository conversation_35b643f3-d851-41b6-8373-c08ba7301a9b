const common = require('../utils/common')
const constant = require('../utils/constant')
const moment = require('moment')
const disbursementRepo = require('./../repositories/disbursement-repo')

async function sendDisbursementRequest(payload, loanAccObj) {
  if (payload.partnerCode == constant.PARTNER_CODE.MISA && payload.bankInfo) {
    return disbursmentMultiBanks(payload, loanAccObj)
  }
  return disbursmentBank(payload, loanAccObj)
}
async function disbursmentMultiBanks(payload, loanAccObj) {
  const listDisbursetment = []
  const listTranfer = []
  const dirbusementPayload = {
    contract_number: payload.contractNumber || payload.debtAckContractNumber,
    loan_id: loanAccObj.loan_id,
    amt: payload.disbursementAmount,
    tran_id: null,
    partner_code: payload.partnerCode,
    receiver_name: payload.beneficiaryName,
    bank_code: payload.bankCode,
    bank_name: payload.bankName,
    bank_account: payload.bankAccount,
    tran_status: constant.DISBURSEMENT.TRANS_STATUS.DISBURMENT_IN_PROGESSING,
    created_user: global.createdBy,
    session_id: loanAccObj.loan_id + '_' + moment().toDate().getTime().toString(),
    debt_ack_contract_number: payload.debtAckContractNumber,
    branch_code: payload.branchCode,
    email: payload.email,
    reference: common.makeId(8).toUpperCase(),
    type: constant.DISBURSEMENT_TYPE.DISBURSEMENT,
  }
  for (const bank of payload.bankInfo) {
    listDisbursetment.push({
      ...dirbusementPayload,
      amt: bank.disbursedAmount,
      receiver_name: bank.beneficiaryName,
      bank_code: bank.bankCode,
      bank_name: bank.bankName,
      bank_account: bank.bankAccount,
      group_acc_type: constant.GROUP_ACC_TYPE.MULTI_ACC,
      comments: bank.content
    })
    // listTranfer.push({
    //   customerName: bank.beneficiaryName,
    //   bankCode: bank.bankCode,
    //   accountNumber: bank.bankAccount,
    //   bankName: bank.bankName,
    //   amount: bank.disbursedAmount,
    //   content: bank.content
    // })
  }
  const disburmentInfo = await disbursementRepo.insertManyDisbursement(listDisbursetment)

  // goi bss de thuc hien giai ngan
  if (disburmentInfo.rowCount == 0) {
    return {
      message: '[LMS-MC] Cannot find disburment information',
      code: 2,
      statusCode: 200
    }
  }

  common.log('[LMS-MC] Consume BSS-Disbursement API to disbursement')
  const rowDis = disburmentInfo.rows[0]
  for (const disbur of disburmentInfo.rows) {
      listTranfer.push({
        customerName: disbur.receiver_name,
        bankCode: disbur.bank_code,
        accountNumber: disbur.bank_account,
        bankName: disbur.bank_name,
        amount: disbur.amt,
        content: disbur.comments,
        disburId: disbur.disb_id
      })
  }

  const disburmentBody = {
    contractNumber: rowDis.debt_ack_contract_number,
    customerName: rowDis.receiver_name,
    loanId: rowDis.loan_id.toString(),
    accType: constant.DISBURSEMENT.ACC_TYPE.ACC,
    description: `EASY CREDIT GN KU ${rowDis.debt_ack_contract_number} `,
    sessionId: rowDis.session_id,
    listTranfer,
    disbursementType: rowDis.tran_status,
    losType: 'SME',
    cusId: loanAccObj.cust_id,
    serviceCode: 'LMS_MCC',
    partnerCode: rowDis.partner_code
  }
  // console.log('disburmentBody:', disburmentBody)
  common
    .postAPI(
      global.disbursementUrl + `/api/disbursementServices/v1/internal/sme/${payload.partnerCode}/disburseRequest`,
      disburmentBody,
      '',
      {
        'Content-type': 'application/json'
      }
    )
    .then((data) => {
      common.log('[LMS-MC] Consume BSS-Disbursement api response body ' + JSON.stringify(data))
    })
    .catch((err) => {
      console.log('[LMS-MC] Call api Disbur error: ', err.message)
    })
}
async function disbursmentBank(payload, loanAccObj) {
  const dirbusementPayload = {}
  dirbusementPayload.contractNumber = payload.contractNumber || payload.debtAckContractNumber
  dirbusementPayload.loanId = loanAccObj.loan_id
  dirbusementPayload.amt = payload.disbursementAmount
  dirbusementPayload.partnerCode = payload.partnerCode
  dirbusementPayload.receiverName = payload.beneficiaryName
  dirbusementPayload.bankCode = payload.bankCode
  dirbusementPayload.bankName = payload.bankName
  dirbusementPayload.bankAccount = payload.bankAccount
  dirbusementPayload.tranStatus = constant.DISBURSEMENT.TRANS_STATUS.DISBURMENT_IN_PROGESSING
  dirbusementPayload.createdUser = global.createdBy
  dirbusementPayload.sessionId = loanAccObj.loan_id + '_' + moment().toDate().getTime().toString()
  dirbusementPayload.debtAckContractNumber = payload.debtAckContractNumber
  dirbusementPayload.branchCode = payload.branchCode
  dirbusementPayload.email = payload.email
  dirbusementPayload.reference = common.makeId(8).toUpperCase()
  dirbusementPayload.groupAccType = constant.GROUP_ACC_TYPE.SINGLE_ACC;
  dirbusementPayload.type = constant.DISBURSEMENT_TYPE.DISBURSEMENT;
  const disburmentInfo = await disbursementRepo.insert(global.poolWrite, dirbusementPayload)
  if (disburmentInfo.rowCount == 0) {
    return {
      message: '[LMS-MC] Cannot find disburment information',
      code: 2,
      statusCode: 200
    }
  }

  common.log('[LMS-MC] Consume BSS-Disbursement API to disbursement')
  const rowDis = disburmentInfo.rows[0]
  const disburmentPayload = {}
  disburmentPayload.ContractNumber = rowDis.debt_ack_contract_number
  disburmentPayload.CustomerName = rowDis.receiver_name
  disburmentPayload.AccountNumber = rowDis.bank_account
  disburmentPayload.AccType = constant.DISBURSEMENT.ACC_TYPE.ACC
  disburmentPayload.Amount = Number(rowDis.amt)
  disburmentPayload.BranchCode = rowDis.branch_code
  disburmentPayload.BankCode = rowDis.bank_code
  disburmentPayload.BankName = rowDis.bank_name
  disburmentPayload.LoanId = rowDis.loan_id.toString()
  disburmentPayload.ImxPaymentRef = rowDis.loan_id.toString()
  disburmentPayload.SessionId = rowDis.session_id
  disburmentPayload.Description = 'EASY CREDIT GN KU ' + rowDis.debt_ack_contract_number
  disburmentPayload.Channel = rowDis.partner_code
  if (payload.partnerCode == constant.PARTNER_CODE.MISA && payload.description) {
    disburmentPayload.Description = payload.description
  }
  common
    .postAPI(
      global.disbursementUrl + global.disbursementCfg.urlMwImx,
      disburmentPayload,
      '',
      { 'Content-type': 'application/json' },
      {
        isLog: true,
        eventType: constant.REQUEST_EVENT_NAME.DISBURSEMENT_REQUEST
      }
    )
    .then((data) => {
      common.log('[LMS-MC] Consume BSS-Disbursement api response body ' + JSON.stringify(data))
    })
    .catch((err) => {
      console.log('[LMS-MC] Call api Disbur error: ', err)
    })
}
async function refundFactoring(payload) {
  const dirbusementPayload = {}
  dirbusementPayload.contractNumber = payload.contractNumber
  dirbusementPayload.loanId = null
  dirbusementPayload.amt = payload.amt
  dirbusementPayload.partnerCode = payload.partnerCode
  dirbusementPayload.receiverName = payload.beneficiaryName
  dirbusementPayload.bankCode = payload.bankCode
  dirbusementPayload.bankName = payload.bankName
  dirbusementPayload.bankAccount = payload.bankAccount
  dirbusementPayload.tranStatus = constant.DISBURSEMENT.TRANS_STATUS.DISBURMENT_IN_PROGESSING
  dirbusementPayload.createdUser = global.createdBy
  dirbusementPayload.sessionId = payload.debtAckContractNumber + '_' + moment().toDate().getTime().toString()
  dirbusementPayload.debtAckContractNumber = payload.debtAckContractNumber
  dirbusementPayload.branchCode = payload.branchCode
  dirbusementPayload.email = payload.email
  dirbusementPayload.reference = common.makeId(8).toUpperCase()
  dirbusementPayload.groupAccType = constant.GROUP_ACC_TYPE.SINGLE_ACC;
  dirbusementPayload.type = constant.DISBURSEMENT_TYPE.REFUND;

  const disburmentInfo = await disbursementRepo.insert(global.poolWrite, dirbusementPayload)
  if (disburmentInfo.rowCount == 0) {
    return {
      message: '[LMS-MC] Cannot find disburment information',
      code: 2,
      statusCode: 200
    }
  }

  common.log('[LMS-MC] Refund factoring BSS-Disbursement API to disbursement')
  const rowDis = disburmentInfo.rows[0]
  const disburmentPayload = {}
  disburmentPayload.contractNumber = rowDis.debt_ack_contract_number
  disburmentPayload.customerName = rowDis.receiver_name
  disburmentPayload.accountNumber = rowDis.bank_account
  disburmentPayload.amount = Number(rowDis.amt)
  disburmentPayload.bankCode = rowDis.bank_code
  disburmentPayload.bankName = rowDis.bank_name
  disburmentPayload.createdBy = global.createdBy
  disburmentPayload.description = 'EASY CREDIT HT KUNN ' + rowDis.debt_ack_contract_number
  disburmentPayload.channel = rowDis.partner_code
  disburmentPayload.transferBankCode = payload.transferBankCode
  disburmentPayload.transferAccount = payload.transferAccount

  common
      .postAPI(
          global.disbursementUrl + global.disbursementCfg.urlRefund,
          disburmentPayload,
          '',
          { 'Content-type': 'application/json' },
          {
            isLog: true,
            eventType: constant.REQUEST_EVENT_NAME.REFUND_REQUEST
          }
      )
      .then((data) => {
        common.log('[LMS-MC] Refund factoring BSS-Disbursement api response body ' + JSON.stringify(data))
      })
      .catch((err) => {
        console.log('[LMS-MC] Call api Disbur error: ', err)
      })
}
module.exports = {
  sendDisbursementRequest,
  refundFactoring,
  disbursmentMultiBanks
}
