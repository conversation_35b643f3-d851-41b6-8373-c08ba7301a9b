const common = require('../utils/common')
const camelcaseKeys = require('camelcase-keys')
const { REQUEST_EVENT_NAME, DEBT_ACK_STATUS } = require('../utils/constant')

const getCustomerInfo = async function (url) {
  const rs = await common.getAPI(url, {})
  // console.log('getCustomerInfo: ',rs)
  if (rs != undefined && rs.data != undefined) {
    return rs.data[0] || {}
  } else {
    return {}
  }
}

const getCustomerInfoByCustId = async function (custId) {
  const url = global.crmServiceLink + global.crmCfg.getCustomerInfo + '?custId=' + custId
  const rs = await common.getAPI(url, {})
  if (rs != undefined && rs.data != undefined) {
    return rs.data[0] || {}
  } else {
    return {}
  }
}
const terminationDebt = async function (crmServiceLink, debtAckContractNumber) {
  common
    .putAPI(
      crmServiceLink + '/customer-loan/debt-ack-contract/' + debtAckContractNumber + '/termination',
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: REQUEST_EVENT_NAME.TERMINATION_DEBT_CRM, contractNumber: debtAckContractNumber }
    )
    .then((rs) => {
      console.log('[LMS-MC] call terminationDebt api: ', rs)
    })
    .catch((err) => {
      console.log('[LMS-MC] Error call terminationDebt api: ', err)
    })
}

async function callCrmCreateKunn(loanAccountObj) {
  const reqBody = camelcaseKeys(loanAccountObj)
  common.postAPI(
    global.crmServiceLink + global.crmCfg.createDebtAckContract,
    reqBody,
    undefined,
    { 'Content-Type': 'application/json' },
    { isLog: true, eventType: REQUEST_EVENT_NAME.CREATE_KUNN_CRM }
  )
}
const activeLoan = async function (contractNumber) {
  const url = global.crmServiceLink + '/cust-loan/contract-number/' + contractNumber + '/active'
  common
    .putAPI(
      url,
      undefined,
      { 'Content-Type': 'application/json' },
      { isLog: true, eventType: REQUEST_EVENT_NAME.ACTIVE_LOAN_CRM, contractNumber }
    )
    .then((rs) => {
      console.log('[LMS-MC] call api activeLoan: ', JSON.stringify(rs))
    })
    .catch((err) => {
      console.log('[LMS-MC] Error api activeLoan: ', err)
    })
}

const updateCrmDi = async function (loanAccountObj, payload) {
  const requestId = 'LMS' + common.makeId(2) + new Date().getTime()
  const reqBody = {
    requestId,
    custId: loanAccountObj.cust_id,
    username: loanAccountObj.phone_number,
    eventName: payload.eventName,
    oldValue: payload.oldValue,
    newValue: payload.newValue,
    partnerCode: loanAccountObj.partner_code,
    note: payload.note
  }
  common
    .postAPI(
      global.crmServiceLink + global.crmCfg.updateDI,
      reqBody,
      undefined,
      { 'Content-Type': 'application/json' },
      {
        isLog: true,
        eventType: REQUEST_EVENT_NAME.UPDATE_DI_CRM,
        contractNumber: loanAccountObj.debt_ack_contract_number
      }
    )
    .then((rs) => {
      console.log('[LMS-MC] call api updateCrmDi: ', JSON.stringify(rs))
    })
    .catch((err) => {
      console.log('[LMS-MC] Error api updateCrmDi: ', err)
    })
}

const updateTerminate = async function (loanAccountObj, statusDate, nonAllocate) {
  const body = {
    custId: loanAccountObj.cust_id,
    contractNumber: loanAccountObj.debt_ack_contract_number,
    status: DEBT_ACK_STATUS.TERMINATED_STR,
    statusDate,
    nonAllocate
  }
  common.log('body call updateTerminate los: ' + JSON.stringify(body))

  const url = global.crmServiceLink + global.crmCfg.updateTerminated
  common
    .postAPI(
      url,
      body,
      undefined,
      { 'Content-Type': 'application/json' },
      {
        isLog: true,
        eventType: REQUEST_EVENT_NAME.UPDATE_TER_CRM,
        contractNumber: loanAccountObj.debt_ack_contract_number
      }
    )
    .then((rs) => {
      console.log('[LMS-MC] call api updateTerminate: ', JSON.stringify(rs))
    })
    .catch((err) => {
      console.log('[LMS-MC] Error api updateTerminate: ', err)
    })
}
const sendQueueDpdRiskGrp = async function (body) {
  common.log('body call sendQueueDpdRiskGrp Crm: ' + JSON.stringify(body))

  const url = global.config.basic.crmService[global.env] + global.config?.data?.crm?.queueDpdRiskGrp
  common
    .postAPI(
      url,
      body,
      undefined,
      { 'Content-Type': 'application/json' },
      {
        isLog: true,
        eventType: REQUEST_EVENT_NAME.QUEUE_DPD_RISKGRP_CRM,
        contractNumber: body.debtAckContractNumber
      }
    )
    .then((rs) => {
      console.log('[LMS-MC] call api sendQueueDpdRiskGrp: ', JSON.stringify(rs))
    })
    .catch((err) => {
      console.log('[LMS-MC] Error api sendQueueDpdRiskGrp: ', err)
    })
}
module.exports = {
  sendQueueDpdRiskGrp,
  getCustomerInfo,
  terminationDebt,
  callCrmCreateKunn,
  activeLoan,
  updateCrmDi,
  updateTerminate,
  getCustomerInfoByCustId
}
