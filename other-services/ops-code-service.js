const common = require('../utils/common')

const sendOpscode = function (url, event, objData) {
  const bodyData = {
    event: {
      apiName: event.apiName,
      eventType: event.eventType
    },
    data: {
      tenor: objData.tenor,
      partnerCode: objData.partnerCode,
      valueDate: objData.valueDate,
      prinAmt: objData.prinAmt,
      intAmt: objData.intAmt,
      remainingPrinAmt: objData.remainingPrinAmt,
      collectionAmt: objData.collectionAmt,
      principleAmt: objData.principleAmt,
      profitDtAmt: objData.profitDtAmt,
      lossDtAmt: objData.lossDtAmt,
      loanAmt: objData.loanAmt,
      issuranceAmt: objData.issuranceAmt,
      startDate: objData.startDate,
      custId: objData.custId,
      custIdType: objData.custIdType,
      idType: objData.idType,
      custIdNo: objData.custIdNo,
      custName: objData.custName,
      custRoleType: objData.custRoleType,
      contractId: objData.contractId,
      tranId: objData.tranId,
      loanId: objData.loanId,
      tranDate: objData.tranDate,
      currency: objData.currency,
      contractNo: objData.contractNo,
      tranType: objData.tranType,
      userReq: objData.userReq,
      isurranceContract: objData.isurranceContract != undefined ? objData.isurranceContract : 2,
      riskGroup: objData.riskGroup,
      oldRiskGroup: objData.oldRiskGroup,
      earlyTerminationFee: objData.earlyTerminationFee,
      dues: objData.dues,
      matchedPrinAmt: objData.matchedPrinAmt,
      matchedIntAmt: objData.matchedIntAmt,
      matchedLpiPrinAmt: objData.matchedLpiPrinAmt,
      matchedLpiIrAmt: objData.matchedLpiIrAmt,
      matchedFeeAmt: objData.matchedFeeAmt,
      tenorDay: objData.tenorDay
    }
  }
  console.log('[LMS-MC] request call opcode: ', JSON.stringify(bodyData))
  // console.log('[LMS-MC] begin call api opcode')
  common
    .postAPI(url, bodyData, undefined, { 'Content-Type': 'application/json' })
    .then((data) => {
      console.log('[LMS-MC] Ket qua tra ve khi gui opcode: ', JSON.stringify(data))
    })
    .catch((err) => {
      console.log('[LMS-MC] Error call opcode: ', err)
    })
}
module.exports = {
  sendOpscode
}
