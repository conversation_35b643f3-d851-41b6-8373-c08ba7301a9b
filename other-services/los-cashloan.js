const common = require('../utils/common')
const genCalculatorRepayment = async function (body) {
  try {
    const url = global.config.basic.losCashloan[global.env] + '/los-cashloan/v1/genCalculatorRepayment'
    const rs = await common.postAPI(url, body, undefined, { 'Content-Type': 'application/json' })
    if (rs != undefined && rs.data != undefined) {
      return rs.data
    } else {
      return
    }
  } catch (e) {
    console.log('[LMS-MC] Error genCalculatorRepayment: ', e)
  }
}
module.exports = {
  genCalculatorRepayment
}
