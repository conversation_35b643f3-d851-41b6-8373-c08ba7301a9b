const common = require('../utils/common')

async function sendSMS(payload) {
  try {
    const hostPrefix = global.config.basic.sms[global.env]

    const url = `${hostPrefix}/sms/v1/send`
    const body = {
      channel: 'LMS_MC_SERVICE',
      source: 'EASY CREDIT',
      dest: payload.phoneNumber,
      content: payload.content
    }
    const result = await common.postAPI(url, body)
    console.log('send sms result:', JSON.stringify(result))
    return result
  } catch (error) {
    console.log('ERROR send sms', JSON.stringify(error).slice(0, 100))
  }
}
module.exports = {
  sendSMS
}
