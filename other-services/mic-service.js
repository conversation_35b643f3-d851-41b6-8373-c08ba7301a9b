const common = require('../utils/common')
const { DATE_FORMAT, REQUEST_EVENT_NAME } = require('../utils/constant')
const moment = require('moment')
const {
  validateEmail,
  getCustomerPerAddress,
  getCustomerCurAddress,
  getCustomerPhoneNumber
} = require('../utils/helper')

const MAPPING_GENDER = {
  M: '1',
  F: '2'
}
async function callGetToken() {
  try {
    const insurMicConfig = global.config.data.insurMic
    const { baseUrl, getTokenEndpoint, username, password, clientId, clientSecret } = insurMicConfig

    const url = `${baseUrl}${getTokenEndpoint}`
    const body = {
      username,
      password,
      clientId,
      clientSecret
    }
    const result = await common.postAPI(url, body)
    console.log('callGetToken result:', JSON.stringify(result))

    if (result.code == '00') {
      const {
        data: { access_token, merchant }
      } = result
      return { access_token, merchant }
    }
    throw new Error('Can not get token')
  } catch (error) {
    console.log('ERROR callGetToken', JSON.stringify(error).slice(0, 100))
    throw error
  }
}

async function callTinhPhiSkVtd(
  payload = {
    start_date: '',
    end_date: '',
    amount: 0,
    insur_type: 'G0'
  }
) {
  const insurMicConfig = global.config.data.insurMic
  const { baseUrl, phiSkVtdEndpoint, maNV, maSP } = insurMicConfig
  const kieu_hd = payload.insur_type.slice(0, 1)
  const goi = payload.insur_type.slice(1)

  const url = `${baseUrl}${phiSkVtdEndpoint}`
  const body = {
    nv: maNV,
    kieu_hd,
    ngay_hl: payload.start_date,
    ngay_kt: payload.end_date,
    tien: payload.amount,
    ma_sp: maSP,
    goi
  }
  try {
    const tokenData = await common.retry({
      func: () => callGetToken(),
      retryCount: 5,
      retryInterval: 1000,
      retryBackoffFactor: 1
    })
    const headers = {
      Authorization: 'Bearer ' + tokenData?.access_token,
      'Content-Type': 'application/json',
      merchant: tokenData?.merchant
    }
    console.log('callTinhPhiSkVtd body:', JSON.stringify(body))
    const result = await common.retry({
      func: () => common.postAPI(url, body, undefined, headers),
      retryCount: 5,
      retryInterval: 1000,
      retryBackoffFactor: 1
    })
    console.log('callTinhPhiSkVtd result:', JSON.stringify(result))
    return result
  } catch (error) {
    console.log('ERROR callTinhPhiSkVtd', JSON.stringify(error).slice(0, 100))
  }
}

async function callGcnSkVtd(insuranceEntity, customer) {
  const insurMicConfig = global.config.data.insurMic
  const { baseUrl, gcnSkVtdEndpoint, maNV, maSP } = insurMicConfig

  const kieu_hd = insuranceEntity.insur_type.slice(0, 1)
  const goi = insuranceEntity.insur_type.slice(1)

  const url = `${baseUrl}${gcnSkVtdEndpoint}`
  console.log('callGcnSkVtd customer info: ', JSON.stringify(customer))
  const body = {
    nv: maNV,
    ma_sp: maSP,
    id_tras: insuranceEntity.id_tras,
    kieu_hd,
    ttoan: Number(insuranceEntity.insur_amt),
    gcn_vtd_ttin_hd: {
      ngay_hl: insuranceEntity.start_date,
      ngay_kt: insuranceEntity.end_date,
      tien: Number(insuranceEntity.amount),
      hd_vay: insuranceEntity.debt_ack_contract_number,
      ng_huong: customer.fullName,
      goi
    },
    gcn_vtd_ttin_kh: {
      lkh: 'C',
      ten: customer.fullName,
      gioi: MAPPING_GENDER[customer.gender],
      cmt: customer.idNumber,
      mobi: getCustomerPhoneNumber(customer),
      ng_sinh: moment(customer.birthDate).format(DATE_FORMAT.DDMMYYYY),
      dchi: getCustomerPerAddress(customer) || getCustomerCurAddress(customer)
    }
  }
  if (validateEmail(customer.email?.trim())) {
    body.gcn_vtd_ttin_kh.email = customer.email?.trim()
  }
  try {
    const tokenData = await common.retry({
      func: () => callGetToken(),
      retryCount: 5,
      retryInterval: 1000,
      retryBackoffFactor: 1
    })
    const headers = {
      Authorization: 'Bearer ' + tokenData?.access_token,
      'Content-Type': 'application/json',
      merchant: tokenData?.merchant
    }
    console.log('callGcnSkVtd body:', JSON.stringify(body))
    const result = await common.retry({
      func: () =>
        common.postAPI(url, body, undefined, headers, {
          isLog: true,
          eventType: REQUEST_EVENT_NAME.CALL_GCN_MIC,
          contractNumber: insuranceEntity.debt_ack_contract_number
        }),
      retryCount: 5,
      retryInterval: 1000,
      retryBackoffFactor: 1
    })
    console.log('callGcnSkVtd result:', JSON.stringify(result))
    return {
      request_body: body,
      response_body: result
    }
  } catch (error) {
    console.log('ERROR callGcnSkVtd', JSON.stringify(error).slice(0, 100))
    return {
      request_body: body,
      response_body: {}
    }
  }
}

async function callDownloadGcn(gcnUrl) {
  try {
    const insurMicConfig = global.config.data.insurMic
    const { baseUrl, downloadFileEndpoint } = insurMicConfig

    const url = `${baseUrl}${downloadFileEndpoint}`
    const hash = gcnUrl.split('hash=')?.[1]
    const body = {
      hash
    }
    console.log('callDownloadGcn url:', JSON.stringify(url))
    console.log('callDownloadGcn body:', JSON.stringify(body))

    const result = await common.postAPI(url, body)
    console.log('callDownloadGcn result:', JSON.stringify(result))
    if (result?.code == '00') {
      return result?.data?.data
    }
    throw new Error('ERROR GET FILE')
  } catch (error) {
    console.log('ERROR callDownloadGcn', JSON.stringify(error).slice(0, 100))
    throw error
  }
}
module.exports = {
  callGetToken,
  callTinhPhiSkVtd,
  callGcnSkVtd,
  callDownloadGcn
}
