def registryCredential = 'harbor_user'
pipeline {
    agent any

    environment {
        APP_NAME = 'lms-mc-service'
        HELM_FOLDER = 'helm'
        HARBOR_URL = 'https://els-registry.evnfc.vn'
        SONAR_HOST_URL = "http://sonarqube.evnfc.vn"		
		projectKey = "ELS---LMS-MC-Service"
        //SONAR_TOKEN = "sqp_b2fec191107467f226168c13d8846b47361730dc"
    }


    stages {
        stage("Checkout code") {
            steps {
              checkout scm
            }
        }

        stage("Trivy FS Scan") {
            steps {
                sh '''
                trivy fs --severity CRITICAL,HIGH --ignore-unfixed --scanners vuln,license,secret  --format table .
                '''
            }
        }

        stage("Trivy Config Scan") {
            steps {
                sh '''
                trivy config --severity CRITICAL,HIGH --format table  .
                '''
            }
        }

        /* ====== SONARQUBE ====== */
        stage('SonarQube Scan (dev/uat, 1 key)') {
            when { anyOf { branch 'dev'; branch 'uat' } }
            steps {
                withSonarQubeEnv('SonarQube') {
                script {
                    def scanner = tool 'SonarScanner'
                    def branch  = (env.GIT_BRANCH ?: 'dev').replace('origin/','')
                    def version = "${branch}-${env.BUILD_NUMBER}"

                    sh """
                    ${scanner}/bin/sonar-scanner \
                        -Dsonar.projectKey=${env.projectKey} \
                        -Dsonar.projectVersion=${version} \
                        -Dsonar.sources=. \
                        -Dsonar.host.url=$SONAR_HOST_URL \
                        -Dsonar.login=$SONAR_AUTH_TOKEN
                    """

                    echo "Dashboard: ${env.SONAR_HOST_URL}/dashboard?id=${env.projectKey}"
                    echo "Version (Activity filter): ${version}"
                }
                }
            }
        }

        // // (tuỳ chọn) không chặn pipeline
        // stage('Quality Gate (non-blocking)') {
        //     when { anyOf { branch 'dev'; branch 'uat' } }
        //     steps {
        //         script {
        //         try {
        //             timeout(time: 10, unit: 'MINUTES') {
        //             def qg = waitForQualityGate()
        //             echo "Quality Gate: ${qg.status}" // không fail
        //             }
        //         } catch (e) { echo "Không nhận webhook Sonar (bỏ qua)" }
        //         }
        //     }
        // }

     
        stage('Build image') {
            steps {
                script {
                    // Lấy tên branch hiện tại từ môi trường Jenkins
                    def branchName = env.GIT_BRANCH?.replaceAll('origin/', '')?.replaceAll('/', '-')

                    // Tạo imageId với app name + branch name
                    imageId = "els-registry.evnfc.vn/els/${APP_NAME}-${branchName}:${BUILD_NUMBER}"

                    docker.withRegistry(HARBOR_URL, registryCredential) {
                        myapp = docker.build(imageId, '-f Dockerfile .')
                        myapp.push()
                    }
                }
            }
        }


        stage("Trivy Image Scan") {
            steps {
                script {
                    // Lấy tên branch hiện tại từ môi trường Jenkins
                    def branchName = env.GIT_BRANCH?.replaceAll('origin/', '')?.replaceAll('/', '-')
                    def imageFull = "els-registry.evnfc.vn/els/${APP_NAME}-${branchName}:$BUILD_NUMBER"
                    sh """
                    trivy image --severity CRITICAL,HIGH --format table  ${imageFull}
                    """
                }
            }
        }
         stage('Deploy to server') {
            //when {
            //    environment name: 'GIT_BRANCH', value: 'origin/uat'
            //}

            steps {
                script {
                    def branchName = env.GIT_BRANCH?.replaceAll('origin/', '')?.replaceAll('/', '-')
                    git branch: 'main',
                    credentialsId: 'user-els-gitlab-ssh',
                    url: '***********************:devops/config.git'

                sh """#!/bin/bash
                      git config --global user.email "<EMAIL>"
                      git config --global user.name "Jenkins"
                      git branch -a
                      cd services/els/${APP_NAME}
                      sed -i 's|tag: .*|tag: ${BUILD_NUMBER}|' values-${branchName}.yaml
                      git add .
                      git commit -m 'Deploy ${APP_NAME} version ${BUILD_NUMBER}'
                      git push origin main
                   """
                }
                
            }
        }
    }
}

